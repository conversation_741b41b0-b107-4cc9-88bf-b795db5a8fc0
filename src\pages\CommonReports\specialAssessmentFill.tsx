import React from 'react'
import ScoreAssessment from '@/pages/CommonReports/scoreAssessment'
import CashRewardsPunishments from '@/pages/CommonReports/cashRewardsPunishments'
import { getTabByKey } from '@/utils/utils'

interface IProps {
  //选中的是哪个节点
  leafKey: string
  //是否可见，true可见 false不可见
  isVisible?: boolean
  //权限,view是查看
  permission?: string
  //组织机构id
  orgId?: string
  //全局搜索参数
  globalSearch?: any
}

export default class extends React.Component<IProps> {
  scoreAssessment = React.createRef<ScoreAssessment>()
  cashRewardsPunishments = React.createRef<CashRewardsPunishments>()

  state = {
    childTabs: [
      {
        code: '01',
        value: '分值考核'
      },
      {
        code: '02',
        value: '现金奖惩'
      }
    ],
    //已选子tab
    selectedChildTab: '01'
  }

  componentDidMount(): void {
    //获取tabType
    let tab = getTabByKey(this.props.globalSearch?.tab)
    if (tab?.id == '4') {
      this.setState({ selectedChildTab: tab?.childId })
    }
  }

  //提交前校验
  submitYz = () => {
    if (this.scoreAssessment?.current && !this.scoreAssessment?.current.submitYz()) {
      return false
    }
    if (this.cashRewardsPunishments?.current && !this.cashRewardsPunishments?.current.submitYz()) {
      return false
    }
    return true
  }

  handleChildTab = (e) => {
    this.setState({ selectedChildTab: e.code })
  }
  render(): React.ReactNode {
    return (
      <div style={{ position: 'relative', height: '100%' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <div style={{ display: 'flex' }}>
            {this.state.childTabs.map((item) => (
              <div
                className={`common-tab-button common-tab-button-small ${
                  this.state.selectedChildTab == item.code && 'button-small-selected'
                }`}
                style={{ marginRight: '10px' }}
                onClick={() => this.handleChildTab(item)}
                key={item.code}
              >
                {item.value}
              </div>
            ))}
          </div>
        </div>
        <div style={{ height: 'calc(100% - 35px)' }}>
          <div style={{ height: '100%', display: this.state.selectedChildTab == '01' ? 'block' : 'none' }}>
            <ScoreAssessment
              ref={this.scoreAssessment}
              permission={this.props.permission}
              isVisible={this.props.isVisible}
              leafKey={this.props.leafKey}
              orgId={this.props.orgId}
            />
          </div>
          <div style={{ height: '100%', display: this.state.selectedChildTab == '02' ? 'block' : 'none' }}>
            <CashRewardsPunishments
              ref={this.cashRewardsPunishments}
              permission={this.props.permission}
              isVisible={this.props.isVisible}
              leafKey={this.props.leafKey}
              orgId={this.props.orgId}
            />
          </div>
        </div>
      </div>
    )
  }
}
