import React from 'react'
import { Pagination as BasePagination } from 'antd'
import './style.scss'

interface IPaginationParams {
  /** 每页显示数量 */
  pageSize: number
  /** 当前页数 */
  pageNum: number
  /** 总量 */
  total: number
  /** 页数变化时 */
  onChange?: (page: number) => void
}
export default class Pagination extends React.Component<IPaginationParams> {
  render() {
    return (
      <BasePagination
        className="pagination-theme1"
        current={this.props.pageNum}
        total={this.props.total}
        pageSize={this.props.pageSize}
        // showQuickJumper
        showTotal={(total) => `共${total}条`}
        // showSizeChanger={false}
        onChange={(page) => this.props?.onChange(page)}
      />
    )
  }
}
