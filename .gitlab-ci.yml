# For more information, see: https://docs.gitlab.com/ee/ci/yaml/index.html#stages

stages:
  - build

before_script:
  - echo "当前用户名是：$(whoami)"
  - export NVM_DIR="/home/<USER>/.nvm" && [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
  - echo "Compiling the code..."

build:
  stage: build
  script:
    - echo "当前分支： $CI_COMMIT_REF_NAME"
    - echo "提交ID： $CI_COMMIT_SHA"
    - echo "VITE_APP_VERSION = \"$CI_COMMIT_REF_NAME\"" > .env
    - echo "VITE_APP_COMMIT_ID = \"$CI_COMMIT_SHA\"" >> .env
    - cat .env
    
    - nvm use
    - npm i
    - npm run build
    - sshpass -p "$SSH_PASSWORD_35" ssh root@************** -o StrictHostKeyChecking=no -p 1135 'mv /usr/local/app/nginxapp/pdas_vue/html/ca /usr/local/app/nginxapp/pdas_vue && rm -rf /usr/local/app/nginxapp/pdas_vue/html/*'
    - sshpass -p "$SSH_PASSWORD_35" scp -o StrictHostKeyChecking=no -P 1135 -r ./dist/* root@**************:/usr/local/app/nginxapp/pdas_vue/html
    - sshpass -p "$SSH_PASSWORD_35" ssh root@************** -o StrictHostKeyChecking=no -p 1135 'mv /usr/local/app/nginxapp/pdas_vue/ca /usr/local/app/nginxapp/pdas_vue/html && docker restart pdas_vue'

  rules:
    - if: '$CI_COMMIT_BRANCH =~ /release/'
