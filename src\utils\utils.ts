import MessageSelf from '@/components/message'
import moment from 'moment'

/**
 * 根据数字获取小数位数，如果是整数，则显示整数，如果是小数，保留2位
 */
export function getNumber(value, digit?: number) {
  if (Number.isInteger(value)) {
    return value.toString() // 如果是整数，则直接返回整数的字符串形式
  } else {
    return parseFloat(value.toFixed(digit || decimalPlaces)) // 如果是小数，保留2位
  }
}

/**
 * 获取默认选中的节点
 */
export function getLeafKey(node, selected?: boolean) {
  //如果selected是true则设置默认选中的内容，否则默认选中第一个
  //本月的26日到下个月的25日 选中本月。
  //如当前时间在8月26日-9月25日（含25日）之间，不管周期树有没有9月10月，选中2024年8月。
  if (selected) {
    //获取当前日期
    let currentDate = moment().format('DD')
    let currentDateYY = ''
    //当前日期和26进行比较，如果比26小或者等于，获取当前月份，如果比26大，则获取下一个月份
    if (currentDate <= '25') {
      currentDateYY = moment().add(-1, 'months').format('YYYYMM')
    } else if (currentDate > '25') {
      currentDateYY = moment().format('YYYYMM')
    }

    let returnNode = getDefaultSelectedMonth(node, currentDateYY)
    return returnNode
  }
  if (!node.subMenuVoList || node.subMenuVoList.length === 0) {
    return node // 当前节点就是叶子节点
  } else {
    // 递归地查找子节点
    for (let child of node.subMenuVoList) {
      let leafNode = getLeafKey(child, selected)
      if (leafNode !== null) {
        return leafNode // 找到叶子节点，返回它
      }
    }
  }
  return null // 没有找到叶子节点
}

export function getDefaultSelectedMonth(node, currentDateYY) {
  if (node.dateId === currentDateYY) {
    if (node.subMenuVoList?.length > 0) {
      return node.subMenuVoList[0]
    }
    return node
  }
  if (node.subMenuVoList) {
    for (let child of node.subMenuVoList) {
      const found = getDefaultSelectedMonth(child, currentDateYY)
      if (found) {
        if (found.subMenuVoList?.length > 0) {
          return found.subMenuVoList[0]
        }
        return found
      }
    }
  }
  return null // 如果没有找到，返回 null
}

//获取uuid
export function getUUID() {
  // 生成随机的 UUID
  // var uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
  //   var r = (Math.random() * 16) | 0,
  //     v = c == 'x' ? r : (r & 0x3) | 0x8
  //   return v.toString(16)
  // })
  //由于数据库中存的是数字，所以随机生成20位的数字
  let number = ''
  while (number.length < 20) {
    // 每次生成一组随机数字，确保不会超出 20 位
    number += Math.floor(Math.random() * 10).toString()
  }
  return number
}

/**
 * 根据组织机构代码，组织机构数组，获取组织机构名称
 * @param orgIds
 * @param list
 * @returns
 */
export function getOrgNames(orgIds, list) {
  let orgNames = []
  for (let i = 0; i < orgIds.length; i++) {
    let orgId = orgIds[i]
    let dict = list.find((item) => item.id == orgId)
    if (dict) {
      orgNames.push(dict.orgName)
    }
  }
  return orgNames.join('、')
}

/**
 * 小数位数
 */
export const decimalPlaces = 1

// debounce
export function debounce<T extends (...args: any[]) => void>(fn: T, delay: number): (...args: Parameters<T>) => void {
  let timer = null
  return function (this: any, ...args: Parameters<T>) {
    if (timer) {
      clearTimeout(timer)
    }
    timer = setTimeout(() => {
      fn.apply(this, args)
    }, delay)
  }
}

const statusDictLs = [
  { code: '10', value: '未提交' },
  { code: '11', value: '待归口管理部门审批', value2: '已提交待审批' },
  { code: '12', value: '待组织部审批', value2: '已审批(归口部门)' },
  { code: '2', value: ' 已审批' },
  { code: '22', value: '作废' }
]

export function getStatusValue(code) {
  let obj = statusDictLs.find((item) => item.code === code)
  if (obj) {
    return obj.value
  }
  return ''
}

const statusDictJX = [
  { code: '-1', value: '已驳回' },
  { code: '0', value: '未提交' },
  { code: '1', value: '已提交待审批' },
  { code: '2', value: ' 已审批' }
]

export function getStatusValueJx(code) {
  let obj = statusDictJX.find((item) => item.code === code)
  if (obj) {
    return obj.value
  }
  return ''
}

//审核状态
const statusDictSh = [
  { code: '-1', value: '已驳回未提交' },
  { code: '0', value: '未审核' },
  { code: '1', value: '已审核' },
  { code: '3', value: '无数据' }
]

export function getStatusValueSh(code) {
  let obj = statusDictSh.find((item) => item.code === code)
  if (obj) {
    return obj.value
  }
  return ''
}

//提交状态
const statusDictTj = [
  { code: '3', value: '无数据' },
  { code: '0', value: '已填未提交' },
  { code: '1', value: '已提交' }
]

export function getStatusValueTj(code) {
  let obj = statusDictTj.find((item) => item.code === code)
  if (obj) {
    return obj.value
  }
  return ''
}

export function validateNumber(value, tips, max, min) {
  if (!value) {
    return true
  }
  if (max.toString()) {
    if (value > max) {
      MessageSelf(tips, 'warning')
      return false
    }
  }
  if (min.toString()) {
    if (value < min) {
      MessageSelf(tips, 'warning')
      return false
    }
  }
  return true
}

//初始化一个map
let tabMap = new Map()
tabMap.set('keyPerformPlanFill', {
  id: '1',
  title: '重点绩效计划'
})
tabMap.set('performIndicatAssessFill', {
  id: '2',
  title: '业绩指标考核'
})
tabMap.set('professEvaluationFill', {
  id: '3',
  childId: '01',
  title: '专业评价-市公司评价'
})
tabMap.set('professEvaluationFill-xgspj', {
  id: '3',
  childId: '02',
  title: '专业评价-县公司评价'
})
tabMap.set('specialAssessmentFill', {
  id: '4',
  childId: '01',
  title: '专项考核-分值考核'
})
tabMap.set('specialAssessmentFill-xjjc', {
  id: '4',
  childId: '02',
  title: '专项考核-现金奖惩'
})
tabMap.set('uploadSupportMaterials', { id: '5', title: '佐证材料' })
tabMap.set('temporaryUnconventional', { id: '6', title: '临时性非常规工作' })
tabMap.set('specialEventRewards', { id: '7', title: '特殊事项奖励' })

//根据key获取tab值
export function getTabByKey(key) {
  return tabMap.get(key)
}

// 处理返回数据格式
// 对于 关键业务指标、重点专项任务、科技创新奖励加分、党建工作考核、其中重点专项任务 中 indicatorName有区别
export function groupByIndicator(arr, indicatorKey = 'indicatorName') {
  const map = new Map()
  arr.forEach((item) => {
    // 用 indicatorName + targetScore 作为唯一分组key
    const key = item[indicatorKey] + '||' + item.targetScore
    if (!map.has(key)) {
      map.set(key, {
        indicatorName: item[indicatorKey], // 使用动态属性名
        targetScore: item.targetScore,
        seqNum: item.seqNum,
        results: []
      })
    }
    map.get(key).results.push({
      distributionResult: item.distributionResult,
      targetScoreDetail: item.targetScoreDetail
    })
  })
  // 转为数组
  return Array.from(map.values())
}
