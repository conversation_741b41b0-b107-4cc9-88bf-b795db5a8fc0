import React from 'react'
import KeyPerformance from './keyPerformance'
import KeynoteWork from './KeynoteWork'
import TechnologicalInnovation from './technologicalInnovation'
import PartyConstruction from './technologicalInnovation'

import { getTabByKey } from '@/utils/utils'

interface IProps {
  //选中的是哪个节点
  leafKey: string
  //全局搜索参数
  globalSearch?: any
}

export default class extends React.Component<IProps> {
  state = {
    childTabs: [
      {
        code: '01',
        value: '关键业务指标'
      },
      {
        code: '02',
        value: '重点专项任务'
      },
      {
        code: '03',
        value: '科技创新奖励加分'
      },
      {
        code: '04',
        value: '党建工作考核'
      },
      {
        code: '05',
        value: '分值计算规则'
      }
    ],
    //已选子tab
    selectedChildTab: '01'
  }

  componentDidMount(): void {
    //获取tabType
    let tab = getTabByKey(this.props.globalSearch?.tab)
    if (tab?.id == '3') {
      this.setState({ selectedChildTab: tab?.childId })
    }
  }
  handleChildTab = (e) => {
    this.setState({ selectedChildTab: e.code })
  }
  render(): React.ReactNode {
    return (
      <div style={{ position: 'relative', height: '100%' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <div style={{ display: 'flex' }}>
            {this.state.childTabs.map((item) => (
              <div
                key={item.code}
                className={`common-tab-button common-tab-button-small ${
                  this.state.selectedChildTab == item.code && 'button-small-selected'
                }`}
                style={{ marginRight: '10px' }}
                onClick={() => this.handleChildTab(item)}
              >
                {item.value}
              </div>
            ))}
          </div>
        </div>
        <div style={{ height: 'calc(100% - 45px)' }}>
          <div style={{ height: '100%', display: this.state.selectedChildTab == '01' ? 'block' : 'none' }}>
            <KeyPerformance leafKey={this.props.leafKey} />
          </div>
          <div style={{ height: '100%', display: this.state.selectedChildTab == '02' ? 'block' : 'none' }}>
            <KeynoteWork leafKey={this.props.leafKey} />
          </div>
          <div style={{ height: '100%', display: this.state.selectedChildTab == '03' ? 'block' : 'none' }}>
            <TechnologicalInnovation leafKey={this.props.leafKey} />
          </div>
          <div style={{ height: '100%', display: this.state.selectedChildTab == '04' ? 'block' : 'none' }}>
            <PartyConstruction leafKey={this.props.leafKey} />
          </div>
          <div style={{ height: '100%', display: this.state.selectedChildTab == '05' ? 'block' : 'none' }}>
            <ol className="circle-list">
              <li>
                <h3 style={{ fontWeight: 700, fontFamily: 'SimHei' }}>业绩指标考核分值计算规则</h3>
              </li>
              <li>
                <p>
                  根据公司《绩效管理实施细则》(济电人资〔2024〕196号)、《2024年绩效考核方案》(济电人资〔2024〕203号)相关规定，2024年度业绩指标考核分值计算方式如下。
                </p>
              </li>
              <li>
                <p>
                  考核分值=(指标排名贡献度×40%+目标达成度×20%+排名提升度×20%+指标得分率×20%)×基础分×K，其中K为调节系数。统筹各模块考核总分，拟定关键业绩指标、重点专项任务、科技创新奖励加分调节系数K取值为5，党建工作考核调节系数K取值为3。
                </p>
              </li>
              <li>
                <span className="circle-num">①</span>
                关键业绩指标：排名贡献度、目标达成度、排名提升度均按照《绩效管理实施细则》中的评分规则取值。对考核结果为负值的指标，综合考虑相关专业整体排名，对调节系数K差异化取值，专业整体排名第一的调节系数K取0；每降低一名，K增加1。
              </li>
              <li>
                <span className="circle-num">②</span>
                重点专项任务：排名贡献度方面，取得卓越，或取得优秀、为最优档位的为100%；取得优秀、非最优档位的为90%。目标达成度方面，完成目标，或未完成目标、为最优档位的为100%；未完成目标、非最优档位的为50%。排名提升度均为0。
              </li>
              <li>
                <span className="circle-num">③</span>
                科技创新奖励加分：排名贡献度为90%，目标达成度为100%，排名提升度为0。
              </li>
              <li>
                <span className="circle-num">④</span>
                党建工作考核：排名贡献度、目标达成度、排名提升度均按照《绩效管理实施细则》中的评分规则取值。
              </li>
            </ol>
            <style>{`
              .circle-list {
                margin: 12px 0 0 20px;
                padding: 0;
                list-style: none;
              }
              .circle-list li {
                margin-bottom: 8px;
                padding-left: 0;
                text-indent: 0;
                line-height: 1.8;
              }
              .circle-num {
                font-size: 16px;
                vertical-align: baseline;
                margin-right: 4px;
              }
            `}</style>
          </div>
        </div>
      </div>
    )
  }
}
