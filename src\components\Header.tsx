import React from 'react'
import logo from '../assets/images/logo.png'
import user from '../assets/images/youyonghuming.png'
import { Dropdown, Menu, Form, Input, FormInstance, Button, Modal } from 'antd'
import ModalC from '@/components/Modal/index'
import { withRouter } from 'tr-cheers'
import { EncryptRSA } from '@/utils'
import <PERSON><PERSON> from '@/utils/cookie'
import axios from 'axios'
import MessageSelf from '../components/message'
import { SearchOutlined } from '@ant-design/icons'

// @ts-ignore
@withRouter
export default class extends React.Component {
  formRef = React.createRef<FormInstance>()
  state: any = {
    isModalVisible: false,
    pwdObj: {
      oldPassword: '',
      newPassword: '',
      confirmPassword: ''
    },
    userName: localStorage.getItem('userName'),
    keywords: ''
  }
  modalVisible() {
    let resetObj = {
      oldPassword: '',
      newPassword: '',
      confirmPassword: ''
    }
    this.setState({ pwdObj: Object.assign(this.state.pwdObj, resetObj) })
    this.setState(
      {
        isModalVisible: true
      },
      () => {
        this.formRef.current.resetFields()
      }
    )
  }
  //校验弱密码
  checkWeakPassword(password: string) {
    //密码必须大于8位
    if (password.length < 8) {
      return false
    }
    //包含数字字母和特殊符号
    let reg = /^(?=.*[a-zA-Z])(?=.*[0-9])(?=.*[^a-zA-Z0-9]).{8,}$/
    return reg.test(password)
  }
  handleOk() {
    this.setState({ pwdObj: Object.assign(this.state.pwdObj, this.formRef.current.getFieldsValue()) }, () => {
      if (
        this.state.pwdObj.newPassword &&
        this.state.pwdObj.confirmPassword &&
        this.state.pwdObj.newPassword != this.state.pwdObj.confirmPassword
      ) {
        Modal.warning({
          title: '提示',
          content: '两次密码输入不一致！',
          zIndex: 1100
        })
        return
      }
      this.formRef.current
        .validateFields()
        .then(() => {
          if (!this.checkWeakPassword(this.state.pwdObj.newPassword)) {
            Modal.warning({
              title: '提示',
              content: '密码必须大于8位且包含数字字母和特殊符号！',
              zIndex: 1100
            })
            return
          }
          let param = {
            newPw: EncryptRSA(this.state.pwdObj.newPassword),
            oldPw: EncryptRSA(this.state.pwdObj.oldPassword)
          }
          axios
            .put('/api/v1/ca/person/password', param)

            .then((res: any) => {
              if (res.code == 0) {
                MessageSelf('修改成功', 'success')
                this.setState({ isModalVisible: false })
                this.gotoLogin()
              } else {
                MessageSelf(res.msg, 'error')
              }
            })
        })
        .catch(({ errorFields }) => {
          MessageSelf(errorFields[0].errors[0], 'error')
        })
    })
  }
  gotoLogin() {
    ;(this.props as any).navigate('/login')
    Cookie.set('token', '')
    Cookie.set('userId', '')
    localStorage.clear()
  }

  handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      if (!this.state.keywords) {
        Modal.warning({
          title: '提示',
          content: '请输入搜索内容！',
          zIndex: 1100
        })
        return
      }
      let encodedName = ''
      encodedName = encodeURIComponent(this.state.keywords)
      ;(this.props as any).navigate('/globalSearch/?keywords=' + encodedName)
    }
  }
  render() {
    const dropdownMenu2 = (
      <Menu>
        <Menu.Item key="下载中心" onClick={this.modalVisible.bind(this)}>
          修改密码
        </Menu.Item>
        <Menu.Item key="消息中心" onClick={() => this.gotoLogin()}>
          退出登录
        </Menu.Item>
      </Menu>
    )
    return (
      <div className="header">
        <div className="title">
          <img src={logo} alt="" style={{ marginRight: '20px', width: '51px', height: '51px' }} />
          <div>{Global.title}</div>
        </div>

        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Input
            value={this.state.keywords}
            onChange={(e) => this.setState({ keywords: e.target.value })}
            onPressEnter={this.handleKeyPress}
            placeholder="请输入搜索内容"
            // suffix={<SearchOutlined />}
            style={{ width: '250px', marginRight: '20px', marginTop: '26px', borderRadius: '5px' }}
          />
          <Dropdown
            overlay={dropdownMenu2}
            trigger={['click']}
            overlayStyle={{
              top: '80px'
            }}
          >
            <div className="user">
              <div className="user-header">
                <div>
                  <img src={user} alt="" />
                </div>
                <div>{this.state.userName}，您好</div>
              </div>
            </div>
          </Dropdown>
        </div>

        <ModalC
          title="修改密码"
          visible={this.state.isModalVisible}
          width="450px"
          height="300px"
          onClose={() => this.setState({ isModalVisible: false })}
        >
          <div className="modal-content-body">
            <Form ref={this.formRef} name="basic" initialValues={this.state.pwdObj}>
              <Form.Item
                label="原密码"
                name="oldPassword"
                rules={[{ required: true, message: '请输入原密码' }]}
                labelCol={{ style: { width: '100px', textAlign: 'right' } }}
                wrapperCol={{ span: 20 }}
              >
                <Input.Password className="width-middle password" placeholder="请输入原密码" />
              </Form.Item>
              <Form.Item
                label="新密码"
                name="newPassword"
                rules={[{ required: true, message: '请输入新密码' }]}
                labelCol={{ style: { width: '100px', textAlign: 'right' } }}
                wrapperCol={{ span: 20 }}
              >
                <Input.Password placeholder="请输入新密码" className="width-middle password" />
              </Form.Item>
              <Form.Item
                label="确认密码"
                name="confirmPassword"
                rules={[{ required: true, message: '请再次输入新密码' }]}
                labelCol={{ style: { width: '100px', textAlign: 'right' } }}
                wrapperCol={{ span: 20 }}
              >
                <Input.Password placeholder="请再次输入新密码" className="width-middle password" />
              </Form.Item>
            </Form>
          </div>
          <div style={{ display: 'flex', justifyContent: 'end' }}>
            <Button
              className="tr-btn"
              style={{ marginRight: '12px' }}
              onClick={() => this.setState({ isModalVisible: false })}
            >
              取消
            </Button>
            <Button
              type="primary"
              className="tr-btn"
              onClick={() => {
                this.handleOk()
              }}
            >
              确定
            </Button>
          </div>
        </ModalC>
      </div>
    )
  }
}
