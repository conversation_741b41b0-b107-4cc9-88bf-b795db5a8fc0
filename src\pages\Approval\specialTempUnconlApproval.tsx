import React from 'react'
import { Iconfont } from 'tr-cheers'
import DateMenu from '@/components/DateMenu'
import { Button, Checkbox, Empty, Modal } from 'antd'
import { getLeafKey, getNumber, getOrgNames, getStatusValue } from '@/utils/utils'
import ModalInfo from '@/components/Modal/index'
import {
  getMenuTemplAudit,
  getOrgList,
  getTempNonRoutineWorkReportInvalid,
  getTemplAuditList,
  passTempNonRoutineWork,
  rejectTempNonRoutineWork
} from '@/api'
import MessageSelf from '@/components/message'
import { LoadingContext } from '@/components/load/loadingProvider'
import TextArea from 'antd/lib/input/TextArea'

export default class ComponentName extends React.Component {
  static contextType = LoadingContext
  context: React.ContextType<typeof LoadingContext>

  state = {
    dateId: '', //时间
    repoOrgId: '', //组织机构id
    //叶子节点
    leafKey: '',
    leftMenu: [],
    keyList: [], // table值
    isShow: true, // 是否显示列表
    bmList: [], // 部门列表
    isModalVisible: false,
    rejectReason: ''
  }

  componentDidMount() {
    this.getBmList()
    //获取左侧菜单
    this.getLeftMenu()
  }

  getBmList = () => {
    getOrgList('01,02,03').then((res: any) => {
      if (res?.data?.list?.length > 0) {
        this.setState({ bmList: res.data.list })
      }
    })
  }

  getLeftMenu() {
    if (localStorage.getItem('userId')) {
      getMenuTemplAudit(localStorage.getItem('userId')).then((res) => {
        if (res?.data?.length > 0) {
          let leftMenu = res.data
          //设置dateId和叶子节点
          let leaf = null
          for (let i = 0; i < leftMenu.length; i++) {
            if (!leaf) {
              leaf = getLeafKey(leftMenu[i], true)
            } else {
              break
            }
          }
          if (leaf.dateId.indexOf('-') > -1) {
            let node = leaf.dateId.split('-')
            this.setState(
              {
                leftMenu: leftMenu,
                leafKey: leaf.dateId,
                dateId: node[0],
                repoOrgId: node[1],
                isShow: true
              },
              () => {
                this.getKeyList() // 临时性非常规工作填报table数据
              }
            )
          } else {
            this.setState({ leftMenu: leftMenu, leafKey: leaf.dateId, dateId: leaf.dateId, isShow: false })
          }
        }
      })
    } else {
      window.location.href = '#/login'
    }
  }

  //菜单树点击
  handleMenu = (e) => {
    if (e.key.indexOf('-') > -1) {
      //有单位
      let key = e.key.split('-')
      this.setState({ isShow: true, dateId: key[0], leafKey: e.key, repoOrgId: key[1] }, () => {
        //获取表格内容
        this.getKeyList()
      })
    } else {
      //没有待审批的数据
      this.setState({ keyList: [], isShow: false, dateId: e.key, leafKey: e.key })
    }
  }

  getKeyList = () => {
    getTemplAuditList({ date: this.state.dateId, repoOrgId: this.state.repoOrgId }).then((res) => {
      if (res?.data?.list?.length > 0) {
        let list = res.data.list
        for (let i = 0; i < list.length; i++) {
          let count = 0
          let item = list[i]
          for (let j = 0; j < item?.list?.length; j++) {
            let obj = item.list[j]
            for (let k = 0; k < obj?.allLineList?.length; k++) {
              let line = obj?.allLineList[k]
              let orgIds = []
              for (let m = 0; m < line?.lineList?.length; m++) {
                orgIds.push(line.lineList[m].orgId)
                count = count + line.score
              }
              line.orgIds = orgIds
            }
          }
          item.hj = getNumber(count)
        }
        this.setState({ keyList: res.data.list })
      } else {
        this.setState({ keyList: [], isShow: false })
      }
    })
  }

  handleSubmit = () => {
    let _this = this
    Modal.confirm({
      title: '通过',
      content: '确认审批通过吗?',
      onOk: () => {
        _this.context.showLoading()
        passTempNonRoutineWork({ date: this.state.dateId, repoOrgId: this.state.repoOrgId })
          .then((res: any) => {
            if (res.code === '0') {
              MessageSelf('审批通过成功', 'success')
              _this.getKeyList()
            }
          })
          .finally(() => {
            _this.context.hideLoading()
          })
      }
    })
  }

  handleRevoke = () => {
    this.setState({ isModalVisible: true, rejectReason: '' })
  }

  //作废
  handleNullify = (tnrwrId) => {
    let _this = this
    Modal.confirm({
      title: '作废提示',
      content: '确定作废吗?',
      onOk: () => {
        //调用作废接口
        _this.context.showLoading()
        getTempNonRoutineWorkReportInvalid({ tnrwrIds: tnrwrId })
          .then((res: any) => {
            MessageSelf('作废成功！', 'success')
            //作废成功后，刷新列表
            if (res?.code == '0') {
              _this.getKeyList()
            }
          })
          .finally(() => {
            _this.context.hideLoading()
          })
      }
    })
  }

  handleOk = () => {
    let _this = this
    _this.context.showLoading()
    rejectTempNonRoutineWork({
      date: this.state.dateId,
      repoOrgId: this.state.repoOrgId,
      rejectReason: this.state.rejectReason
    })
      .then((res: any) => {
        if (res.code === '0') {
          MessageSelf('驳回成功', 'success')
          _this.setState({ isModalVisible: false, rejectReason: '' })
          _this.getKeyList()
        }
      })
      .finally(() => {
        _this.context.hideLoading()
      })
  }

  render() {
    return (
      <div className="main-common">
        <div className="main-left">
          {this.state.dateId && (
            <DateMenu
              menuList={this.state.leftMenu}
              dateId={this.state.dateId}
              leafKey={this.state.leafKey}
              onClick={(e) => this.handleMenu(e)}
            />
          )}
        </div>
        <div className="main-right">
          <div className="content-top" style={{ marginLeft: 'auto' }}>
            <div style={{ display: 'flex' }}>
              <div
                className={`common-button2 ${!this.state.keyList.length && 'disabled-div'}`}
                onClick={this.handleSubmit}
              >
                <Iconfont type="icon-woyiyuedu" style={{ fontSize: '22px' }} />
                <div className="common-text">通过</div>
              </div>
              <div
                className={`common-button2 ${!this.state.keyList.length && 'disabled-div'}`}
                style={{ marginLeft: '10px' }}
                onClick={this.handleRevoke}
              >
                <Iconfont type="icon-chehui" style={{ fontSize: '22px' }} />
                <div className="common-text">驳回</div>
              </div>
            </div>
          </div>
          {this.state.isShow ? (
            <div className="content-center">
              <div className="div-table">
                <table className="common-table">
                  <thead>
                    <tr>
                      <th>序号</th>
                      <th>归口管理</th>
                      <th>提报部门</th>
                      <th>考评内容</th>
                      <th>部门名称</th>
                      <th>得分</th>
                      <th>合计</th>
                      <th>数据状态</th>
                      <th>操作</th>
                    </tr>
                  </thead>
                  <tbody>
                    {this.state.keyList.map((outItem, outIndex) => {
                      //计算rowspan
                      let rowSpan = 0
                      outItem.list.map((row) => {
                        rowSpan = rowSpan + row?.allLineList?.length
                      })
                      return outItem.list.map((centerItem, centerIndex) => {
                        return centerItem.allLineList.map((item, index) => {
                          return (
                            <tr key={`${outIndex} + ${centerIndex} + ${index}`}>
                              {centerIndex == 0 && index == 0 && (
                                <td style={{ width: '45px' }} rowSpan={rowSpan}>
                                  {outIndex + 1}
                                </td>
                              )}
                              {centerIndex == 0 && index == 0 && <td rowSpan={rowSpan}>{outItem.prof}</td>}
                              {index == 0 && <td rowSpan={centerItem.allLineList.length}>{centerItem.repoDepName}</td>}
                              {index == 0 && (
                                <td rowSpan={centerItem.allLineList.length}>{centerItem.assessContent}</td>
                              )}
                              <td>{getOrgNames(item.orgIds, this.state.bmList)}</td>
                              <td>{item.score}</td>
                              {centerIndex == 0 && index == 0 && (
                                <td style={{ width: '45px' }} rowSpan={rowSpan}>
                                  {outItem.hj}
                                </td>
                              )}
                              {index == 0 && (
                                <td style={{ width: '100px' }} rowSpan={centerItem.allLineList.length}>
                                  {getStatusValue(centerItem.status)}
                                </td>
                              )}
                              {index == 0 && (
                                <td rowSpan={centerItem.allLineList.length}>
                                  <div className="table-operate">
                                    <div
                                      onClick={() => this.handleNullify(centerItem.tnrwrId)}
                                      className={`${centerItem.status != '11' && 'disabled-table-div'}`}
                                    >
                                      <Iconfont type="icon-a-zuofei5" style={{ fontSize: '16px' }} />
                                      <span className="operate-text">作废</span>
                                    </div>
                                  </div>
                                </td>
                              )}
                            </tr>
                          )
                        })
                      })
                    })}
                  </tbody>
                </table>
              </div>
            </div>
          ) : (
            <div style={{ paddingTop: '20px' }}>
              <Empty description="暂无待审批的数据" />
            </div>
          )}
        </div>
        <ModalInfo
          title="驳回原因"
          visible={this.state.isModalVisible}
          width="600px"
          maxHeight="400px"
          onClose={() => this.setState({ isModalVisible: false })}
        >
          <div>
            <TextArea
              autoSize
              maxLength={200}
              value={this.state.rejectReason}
              placeholder="请填写驳回原因"
              onChange={(obj) => this.setState({ rejectReason: obj.target.value })}
            />
          </div>
          <div style={{ display: 'flex', justifyContent: 'end', marginTop: '20px' }}>
            <Button
              className="tr-btn"
              style={{ marginRight: '12px' }}
              onClick={() => this.setState({ isModalVisible: false })}
            >
              取消
            </Button>
            <Button
              type="primary"
              className="tr-btn"
              onClick={() => {
                this.handleOk()
              }}
            >
              确定
            </Button>
          </div>
        </ModalInfo>
      </div>
    )
  }
}
