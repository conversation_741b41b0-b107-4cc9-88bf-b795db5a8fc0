import {
  getKeyKpiSummaryrDetails,
  getPartyBuildingRankingDetails,
  getSafetyManagementRankingDetails,
  getRedLineIndicatorsDetails,
  getCompanyLeadershipDetails,
  getKeyWorkAssessmentDetails,
  getSpecialRewardsDetails,
  getMonthlyAssessmentAvgDetails
} from '@/api'
import React from 'react'
import { LoadingContext } from '@/components/load/loadingProvider'

interface IProps {
  //选中的是哪个节点
  leafKey: string
}

// 定义表格数据类型
interface TableDataItem {
  id?: number
  department: string
  indicator: string
  score: number
  licheng: number
  zhangqiu: number
  changqing: number
  pingyin: number
  jiyang: number
  shanghe: number
}

// 定义章丘公司关键业绩指标表格数据类型
interface PerformanceEvaluationItem {
  id: number
  indicatorName: string
  evaluationDepartment: string
  baseScore: number
  ranking: number
  score: number
  adjustmentScore: number
  adjustmentReason: string
}

export default class extends React.Component<IProps> {
  static contextType = LoadingContext
  context: React.ContextType<typeof LoadingContext>

  state = {
    //table数据
    tableData: [] as TableDataItem[],
    // 章丘公司关键业绩指标表格数据
    performanceEvaluationData: [] as PerformanceEvaluationItem[],
    dateId: '',
    keyList: [],
    // 党建工作考核
    partyConstructionData: [],
    // 安全管理工作考核
    safetyManagementData: [],
    // 红线指标
    redLineIndicatorsData: [],
    // 公司领导评价
    companyLeadershipData: [],
    // 重点工作考核
    keyWorkAssessmentData: [],
    // 特殊事项奖励
    specialRewards: [],
    // 月度考核结果平均分
    monthlyAssessmentAvgData: []
  }

  componentDidMount(): void {
    // 初始化时调用API获取数据
    this.setState({ dateId: this.props.leafKey }, () => {
      this.getKeyList()
    })
  }

  componentDidUpdate(prevProps: Readonly<IProps>, prevState: Readonly<{}>, snapshot?: any): void {
    if (this.props.leafKey != prevProps.leafKey) {
      this.setState({ dateId: this.props.leafKey }, () => {
        // 获取列表数据
        this.getKeyList()
      })
    }
  }

  getKeyList = () => {
    if (this.state.dateId) {
      this.context.showLoading()
      // this.state.dateId.split('-')[0] 是截取年份
      // --关键业绩指标
      getKeyKpiSummaryrDetails({ dateYear: this.state.dateId.split('-')[0], orgName: '章丘公司' })
        .then((res) => {
          // console.log('章丘公司', res)
          if (res.data && res.data.list) {
            // 分离总分及排名和其他指标数据
            const totalScoreItem = res.data.list.find((item: any) => item.indicatorName === '总分及排名')
            const otherItems = res.data.list.filter((item: any) => item.indicatorName !== '总分及排名')

            // 转换其他指标数据
            const convertedData = otherItems.map((item: any, index: number) => ({
              id: item.ksId || item.id || item.seqNum || index,
              indicatorName: item.indicatorName,
              evaluationDepartment: item.evaluationDepartment,
              baseScore: item.baseScore,
              ranking: item.ranking,
              score: item.score,
              adjustmentScore: item.adjustmentScore,
              adjustmentReason: item.adjustmentReason
            }))

            // 如果有总分及排名数据，添加到末尾
            if (totalScoreItem) {
              const totalScoreConverted = {
                id: totalScoreItem.ksId || totalScoreItem.id || totalScoreItem.seqNum || 'total',
                indicatorName: totalScoreItem.indicatorName,
                evaluationDepartment: totalScoreItem.evaluationDepartment || '',
                baseScore: totalScoreItem.baseScore,
                ranking: totalScoreItem.ranking,
                score: totalScoreItem.score,
                adjustmentScore: totalScoreItem.adjustmentScore,
                adjustmentReason: totalScoreItem.adjustmentReason
              }
              convertedData.push(totalScoreConverted)
            }

            this.setState({ performanceEvaluationData: convertedData })
          }
          this.context.hideLoading()
        })
        .catch(() => {
          this.context.hideLoading()
        })
      // -----党建工作考核
      getPartyBuildingRankingDetails({ dateYear: this.state.dateId.split('-')[0], orgName: '章丘公司' })
        .then((res) => {
          // console.log('获取党建工作考核表22', res)
          this.setState({ partyConstructionData: res.data.list })
          this.context.hideLoading()
        })
        .catch(() => {
          this.context.hideLoading()
        })
      // -----安全管理工作考核
      getSafetyManagementRankingDetails({ dateYear: this.state.dateId.split('-')[0], orgName: '章丘公司' })
        .then((res) => {
          // console.log('获取党建工作考核表22', res)
          this.setState({ safetyManagementData: res.data.list })
          this.context.hideLoading()
        })
        .catch(() => {
          this.context.hideLoading()
        })
      // -----红线指标
      getRedLineIndicatorsDetails({ dateYear: this.state.dateId.split('-')[0], orgName: '章丘公司' })
        .then((res) => {
          // console.log('获取党建工作考核表22', res)
          this.setState({ redLineIndicatorsData: res.data.list })
          this.context.hideLoading()
        })
        .catch(() => {
          this.context.hideLoading()
        })
      // -----公司领导评价
      getCompanyLeadershipDetails({ dateYear: this.state.dateId.split('-')[0], orgName: '章丘公司' })
        .then((res) => {
          // console.log('获取党建工作考核表22', res)
          this.setState({ companyLeadershipData: res.data.list })
          this.context.hideLoading()
        })
        .catch(() => {
          this.context.hideLoading()
        })
      // -----重点工作考核
      getKeyWorkAssessmentDetails({ dateYear: this.state.dateId.split('-')[0], orgName: '章丘公司' })
        .then((res) => {
          // console.log('获取重点工作考核333', res)
          this.setState({ keyWorkAssessmentData: res.data.list })
          this.context.hideLoading()
        })
        .catch(() => {
          this.context.hideLoading()
        })
      // -----特殊事项奖励
      getSpecialRewardsDetails({ dateYear: this.state.dateId.split('-')[0], orgName: '章丘公司' })
        .then((res) => {
          // console.log('获取重点工作考核333', res)
          this.setState({ specialRewards: res.data.list })
          this.context.hideLoading()
        })
        .catch(() => {
          this.context.hideLoading()
        })
      // -----月度考核结果平均分
      getMonthlyAssessmentAvgDetails({ dateYear: this.state.dateId.split('-')[0], orgName: '章丘公司' })
        .then((res) => {
          // console.log('获取重点工作考核333', res)
          this.setState({ monthlyAssessmentAvgData: res.data.list })
          this.context.hideLoading()
        })
        .catch(() => {
          this.context.hideLoading()
        })
    }
  }
  // ----关键业绩指标表格--按评价部门分组数据
  groupDataByDepartment = (data: PerformanceEvaluationItem[]) => {
    const grouped: { [key: string]: PerformanceEvaluationItem[] } = {}

    data.forEach((item) => {
      const department = item.evaluationDepartment || '其他'
      if (!grouped[department]) {
        grouped[department] = []
      }
      grouped[department].push(item)
    })

    return grouped
  }
  // 渲染合并部门列的表格行
  renderMergedDepartmentRows = () => {
    const { performanceEvaluationData } = this.state

    // 分离总分及排名和其他数据
    const totalScoreItem = performanceEvaluationData.find((item) => item.indicatorName === '总分及排名')
    const otherData = performanceEvaluationData.filter((item) => item.indicatorName !== '总分及排名')

    const groupedData = this.groupDataByDepartment(otherData)
    const rows: React.ReactNode[] = []
    let globalIndex = 1

    Object.keys(groupedData).forEach((department) => {
      const departmentItems = groupedData[department]

      // 添加该部门下的所有指标
      departmentItems.forEach((item, index) => {
        if (index === 0) {
          // 第一行：显示部门名称并合并行
          rows.push(
            <tr key={`${department}-${item.id}-${globalIndex}`}>
              <td>{globalIndex}</td>
              <td>{item.indicatorName}</td>
              <td rowSpan={departmentItems.length}>{item.evaluationDepartment}</td>
              <td>{item.baseScore}</td>
              <td>{item.ranking}</td>
              <td>{item.score}</td>
              <td>{item.adjustmentScore}</td>
              <td style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-word', textAlign: 'left' }}>
                {item.adjustmentReason}
              </td>
            </tr>
          )
        } else {
          // 后续行：不渲染部门列，因为已经被合并了
          rows.push(
            <tr key={`${department}-${item.id}-${globalIndex}`}>
              <td>{globalIndex}</td>
              <td>{item.indicatorName}</td>
              <td>{item.baseScore}</td>
              <td>{item.ranking}</td>
              <td>{item.score}</td>
              <td>{item.adjustmentScore}</td>
              <td style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-word', textAlign: 'left' }}>
                {item.adjustmentReason}
              </td>
            </tr>
          )
        }
        globalIndex++
      })
    })

    // 在最后添加总分及排名行
    if (totalScoreItem) {
      rows.push(
        <tr key="total-score">
          <td colSpan={3}>总分及排名</td>
          <td>{totalScoreItem.baseScore}</td>
          <td>{totalScoreItem.ranking}</td>
          <td>{totalScoreItem.score}</td>
          <td>{totalScoreItem.adjustmentScore}</td>
          <td style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-word', textAlign: 'left' }}>
            {totalScoreItem.adjustmentReason}
          </td>
        </tr>
      )
    }

    return rows
  }
  // ----渲染党建工作考核表格行
  renderPartyConstructionRows = () => {
    const { partyConstructionData } = this.state
    const rows: React.ReactNode[] = []

    partyConstructionData.forEach((item, index) => {
      if (item.evaluationDepartment === '合计' || item.evaluationDepartment === '标准总分及排名') {
        // 合计行和标准总分及排名行：合并评价部门和指标名称列
        const displayText = item.evaluationDepartment === '合计' ? '合计' : '标准总分及排名'
        rows.push(
          <tr key={`${item.evaluationDepartment}-${index}`}>
            <td colSpan={2}>{displayText}</td>
            <td>{item.score}</td>
            <td>{item.achievedScore}</td>
            <td>{item.ranking}</td>
          </tr>
        )
      } else {
        // 普通行：正常显示
        rows.push(
          <tr key={`normal-${index}`}>
            <td>{item.evaluationDepartment}</td>
            <td>{item.indicatorName}</td>
            <td>{item.score}</td>
            <td>{item.achievedScore}</td>
            <td>{item.ranking}</td>
          </tr>
        )
      }
    })

    return rows
  }

  // ----渲染重点工作考核表格行
  renderKeyWorkAssessmentRows = () => {
    const { keyWorkAssessmentData } = this.state
    const rows: React.ReactNode[] = []

    // 分离合计行、标准分行、排名行和其他数据
    const totalItem = keyWorkAssessmentData.find((item) => item.taskName === '合计')
    const standardItem = keyWorkAssessmentData.find((item) => item.taskName === '标准分')
    const rankingItem = keyWorkAssessmentData.find((item) => item.taskName === '排名')
    const otherItems = keyWorkAssessmentData.filter(
      (item) => item.taskName !== '合计' && item.taskName !== '标准分' && item.taskName !== '排名'
    )

    // 先渲染普通数据行
    otherItems.forEach((item, index) => {
      rows.push(
        <tr key={`normal-${item.kwaId || index}`}>
          <td>{item.seqNum || index + 1}</td>
          <td>{item.taskName}</td>
          <td>{item.type}</td>
          <td>{item.leadingDepartment}</td>
          <td>{item.score}</td>
        </tr>
      )
    })

    // 在最后添加合计行
    if (totalItem) {
      rows.push(
        <tr key="total-row">
          <td colSpan={4}>合计</td>
          <td>{totalItem.score}</td>
        </tr>
      )
    }

    // 在最后添加标准分行
    if (standardItem) {
      rows.push(
        <tr key="standard-row">
          <td colSpan={4}>标准分</td>
          <td>{standardItem.score}</td>
        </tr>
      )
    }

    // 在最后添加排名行
    if (rankingItem) {
      rows.push(
        <tr key="ranking-row">
          <td colSpan={4}>排名</td>
          <td>{rankingItem.score}</td>
        </tr>
      )
    }

    return rows
  }
  render(): React.ReactNode {
    const { tableData, performanceEvaluationData } = this.state

    return (
      <div style={{ height: 'calc(100% - 10px)' }}>
        <div className="div-table" style={{ height: 'calc(100vh - 250px)', overflow: 'auto' }}>
          {/* 关键业绩指标 */}
          <h3 style={{ fontWeight: 700, fontSize: '14px', margin: '10px 0' }}>关键业绩指标</h3>
          <table className="common-table" style={{ tableLayout: 'auto', minWidth: '100%', marginBottom: '25px' }}>
            <thead>
              <tr>
                <th
                  rowSpan={2}
                  style={{
                    whiteSpace: 'nowrap'
                  }}
                >
                  序号
                </th>
                <th rowSpan={2} style={{ whiteSpace: 'nowrap' }}>
                  指标名称
                </th>
                <th rowSpan={2} style={{ whiteSpace: 'nowrap' }}>
                  评价部门
                </th>
                <th rowSpan={2} style={{ whiteSpace: 'nowrap' }}>
                  基础分值
                </th>
                <th colSpan={4} style={{ textAlign: 'center', whiteSpace: 'nowrap' }}>
                  章丘公司
                </th>
              </tr>
              <tr>
                <th style={{ whiteSpace: 'nowrap' }}>排名</th>
                <th style={{ whiteSpace: 'nowrap' }}>得分</th>
                <th style={{ whiteSpace: 'nowrap' }}>加减分</th>
                <th style={{ whiteSpace: 'nowrap', textAlign: 'left' }}>加减分依据</th>
              </tr>
            </thead>
            <tbody>
              {performanceEvaluationData.length > 0 ? (
                this.renderMergedDepartmentRows()
              ) : (
                <tr>
                  <td colSpan={8} style={{ textAlign: 'center', padding: '20px' }}>
                    暂无数据
                  </td>
                </tr>
              )}
            </tbody>
          </table>
          {/* 党建工作考核 */}
          <h3 style={{ fontWeight: 700, fontSize: '14px', margin: '10px 0' }}>党建工作考核</h3>
          <table className="common-table" style={{ tableLayout: 'auto', minWidth: '100%', marginBottom: '25px' }}>
            <thead>
              <tr>
                <th rowSpan={2}>评价部门</th>
                <th rowSpan={2}>指标名称</th>
                <th rowSpan={2}>分值</th>
                <th colSpan={2} style={{ textAlign: 'center' }}>
                  章丘公司
                </th>
              </tr>
              <tr>
                <th>得分</th>
                <th>排名</th>
              </tr>
            </thead>
            <tbody>
              {this.state.partyConstructionData.length > 0 ? (
                <>
                  {/* 动态渲染数据行 */}
                  {this.renderPartyConstructionRows()}
                </>
              ) : (
                <tr>
                  <td colSpan={5} style={{ textAlign: 'center', padding: '20px' }}>
                    暂无数据
                  </td>
                </tr>
              )}
            </tbody>
          </table>
          {/* 安全管理工作考核 */}
          <h3 style={{ fontWeight: 700, fontSize: '14px', margin: '20px 0 10px 0' }}>安全管理工作考核</h3>
          <table className="common-table" style={{ tableLayout: 'auto', minWidth: '100%', marginBottom: '25px' }}>
            <thead>
              <tr>
                <th>评价部门</th>
                <th>单位</th>
                <th>原始分</th>
                <th>加减分依据</th>
                <th>标准总分</th>
                <th>排名</th>
              </tr>
            </thead>
            <tbody>
              {this.state.safetyManagementData.length > 0 ? (
                this.state.safetyManagementData.map((item: any, index: number) => (
                  // 长文本列使用 whiteSpace: 'pre-wrap' 和 wordBreak: 'break-word' 确保文本换行显示
                  // 部门列使用 whiteSpace: 'nowrap' 保持单行显示
                  <tr key={index}>
                    <td>{item.evaluationDepartment}</td>
                    <td>{item.unit}</td>
                    <td>{item.originalScore}</td>
                    <td>{item.adjustmentReason}</td>
                    <td>{item.standardTotalScore}</td>
                    <td>{item.ranking}</td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={6} style={{ textAlign: 'center', padding: '20px' }}>
                    暂无数据
                  </td>
                </tr>
              )}
            </tbody>
          </table>
          {/* 红线指标 */}
          <h3 style={{ fontWeight: 700, fontSize: '14px', margin: '20px 0 10px 0' }}>“红线指标”</h3>
          <table className="common-table" style={{ tableLayout: 'auto', minWidth: '100%', marginBottom: '25px' }}>
            <thead>
              <tr>
                <th>评价部门</th>
                <th>单位</th>
                <th>总分</th>
                <th>扣分项目</th>
                <th>扣分依据</th>
              </tr>
            </thead>
            <tbody>
              {this.state.redLineIndicatorsData.length > 0 ? (
                this.state.redLineIndicatorsData.map((item: any, index: number) => (
                  // 长文本列使用 whiteSpace: 'pre-wrap' 和 wordBreak: 'break-word' 确保文本换行显示
                  // 部门列使用 whiteSpace: 'nowrap' 保持单行显示
                  <tr key={index}>
                    <td>{item.evaluationDepartment}</td>
                    <td>{item.unit}</td>
                    <td>{item.totalScore}</td>
                    <td>{item.deductionItems}</td>
                    <td>{item.deductionBasis}</td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={5} style={{ textAlign: 'center', padding: '20px' }}>
                    暂无数据
                  </td>
                </tr>
              )}
            </tbody>
          </table>
          {/* 公司领导评价 */}
          <h3 style={{ fontWeight: 700, fontSize: '14px', margin: '20px 0 10px 0' }}>公司领导评价</h3>
          <table className="common-table" style={{ tableLayout: 'auto', minWidth: '100%', marginBottom: '25px' }}>
            <thead>
              <tr>
                <th>单位</th>
                <th>总分</th>
                <th>排名</th>
              </tr>
            </thead>
            <tbody>
              {this.state.companyLeadershipData.length > 0 ? (
                this.state.companyLeadershipData.map((item: any, index: number) => (
                  // 长文本列使用 whiteSpace: 'pre-wrap' 和 wordBreak: 'break-word' 确保文本换行显示
                  // 部门列使用 whiteSpace: 'nowrap' 保持单行显示
                  <tr key={index}>
                    <td>{item.unit}</td>
                    <td>{item.totalScore}</td>
                    <td>{item.ranking}</td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={3} style={{ textAlign: 'center', padding: '20px' }}>
                    暂无数据
                  </td>
                </tr>
              )}
            </tbody>
          </table>
          {/* 重点工作考核 */}
          <h3 style={{ fontWeight: 700, fontSize: '14px', margin: '20px 0 10px 0' }}>重点工作考核</h3>
          <table className="common-table" style={{ tableLayout: 'auto', minWidth: '100%', marginBottom: '25px' }}>
            <thead>
              <tr>
                <th>序号</th>
                <th>任务名称</th>
                <th>类型</th>
                <th>牵头部门</th>
                <th>章丘公司</th>
              </tr>
            </thead>
            <tbody>
              {this.state.keyWorkAssessmentData.length > 0 ? (
                <>
                  {/* 动态渲染数据行 */}
                  {this.renderKeyWorkAssessmentRows()}
                </>
              ) : (
                <tr>
                  <td colSpan={5} style={{ textAlign: 'center', padding: '20px' }}>
                    暂无数据
                  </td>
                </tr>
              )}
            </tbody>
          </table>
          {/* 特殊事项奖励 */}
          <h3 style={{ fontWeight: 700, fontSize: '14px', margin: '20px 0 10px 0' }}>特殊事项奖励</h3>
          <table className="common-table" style={{ tableLayout: 'auto', minWidth: '100%', marginBottom: '25px' }}>
            <thead>
              <tr>
                <th>单位</th>
                <th>先进评比奖励</th>
                <th>特殊贡献奖励</th>
                <th>排名</th>
              </tr>
            </thead>
            <tbody>
              {this.state.specialRewards.length > 0 ? (
                this.state.specialRewards.map((item: any, index: number) => (
                  // 长文本列使用 whiteSpace: 'pre-wrap' 和 wordBreak: 'break-word' 确保文本换行显示
                  // 部门列使用 whiteSpace: 'nowrap' 保持单行显示
                  <tr key={index}>
                    <td>{item.unit}</td>
                    <td>{item.excellentEvaluationReward}</td>
                    <td>{item.specialContributionReward}</td>
                    <td>{item.ranking}</td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={4} style={{ textAlign: 'center', padding: '20px' }}>
                    暂无数据
                  </td>
                </tr>
              )}
            </tbody>
          </table>
          {/* 月度考核结果平均分 */}
          <h3 style={{ fontWeight: 700, fontSize: '14px', margin: '20px 0 10px 0' }}>月度考核结果平均分</h3>
          <table className="common-table" style={{ tableLayout: 'auto', minWidth: '100%', marginBottom: '55px' }}>
            <thead>
              <tr>
                <th>单位</th>
                <th>关键业绩指标</th>
                <th>专业评价</th>
                <th>重点工作</th>
                <th>专项考核</th>
              </tr>
            </thead>
            <tbody>
              {this.state.monthlyAssessmentAvgData.length > 0 ? (
                this.state.monthlyAssessmentAvgData.map((item: any, index: number) => (
                  // 长文本列使用 whiteSpace: 'pre-wrap' 和 wordBreak: 'break-word' 确保文本换行显示
                  // 部门列使用 whiteSpace: 'nowrap' 保持单行显示
                  <tr key={index}>
                    <td>{item.unit}</td>
                    <td>{item.keyPerformanceIndicators}</td>
                    <td>{item.professionalEvaluation}</td>
                    <td>{item.keyWork}</td>
                    <td>{item.specialAssessment}</td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={5} style={{ textAlign: 'center', padding: '20px' }}>
                    暂无数据
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    )
  }
}
