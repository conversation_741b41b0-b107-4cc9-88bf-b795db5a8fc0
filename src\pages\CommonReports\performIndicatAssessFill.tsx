import React from 'react'
import { getNumber, getUUID, decimalPlaces, debounce, validateNumber } from '@/utils/utils'
import { Iconfont } from 'tr-cheers'
import { Select, Input, InputNumber, Modal, Checkbox, Button } from 'antd'
import ModalInfo from '@/components/Modal/index'

import {
  deletePerfAssess,
  getOrgList,
  getPerfAssessList,
  getDictListByName,
  savePerfAssess,
  indicatorAssessReportSwap,
  indicatorAssessReportPinned,
  indicatorAssessOrgSwap,
  indicatorAssessOrgPinned
} from '@/api'
import { LoadingContext } from '@/components/load/loadingProvider'
import Department from '../Common/department'
const { TextArea } = Input

interface IProps {
  //选中的是哪个节点
  leafKey: string
  //是否可见，true可见 false不可见
  isVisible?: boolean
  //权限,view是查看
  permission?: string
  //机构id
  orgId?: string
}

export default class extends React.Component<IProps> {
  static contextType = LoadingContext
  context: React.ContextType<typeof LoadingContext>
  intervalId = null // 在这里定义 intervalId
  //保存前数据
  preKeyList: any = []

  state = {
    dateId: '',
    menuType: '',
    //表格list
    performanList: [],
    //指标名称列表
    zbmcList: [],
    //考评依据
    kpyjList: null,
    //部门list
    bmList: [],
    //已选择的行
    selectedRows: [],
    //已选择的指标代码
    selectedZb: [],
    isModalVisible: false,
    // 选择考评依据的行和列
    index: 0,
    smallIndex: 0,
    reasonItem: {
      list: [],
      select: ''
    },
    RowReasonItem: {
      list: [],
      select: ''
    },
    loading: false,
    departMent: {
      isDepartVisible: false, //弹出部门选择
      indexBig: '',
      indexSmall: '',
      selectedCode: []
    }
  }

  componentDidMount() {
    if (this.props.leafKey.includes('-')) {
      const [dateId, menuType] = this.props.leafKey.split('-')

      this.setState({ dateId, menuType }, () => {
        this.initializeData()
      })
    }
    this.getBmList()

    // 每隔2分钟（120000毫秒）执行
    this.intervalId = setInterval(this.saveAll, 120000)
  }

  componentWillUnmount() {
    // 清除定时器
    clearInterval(this.intervalId)
  }

  //定时保存
  saveAll = () => {
    let list = [...this.state.performanList]
    for (let i = 0; i < list.length; i++) {
      let item = list[i]
      if (item.isEdit) {
        if (JSON.stringify(this.preKeyList[i]) !== JSON.stringify(item)) {
          //保存大行
          this.handleSaveParent(i, 'regular')
        }
      } else {
        let smallList = item.allLineList
        let smallEdit = false
        for (let j = 0; j < smallList.length; j++) {
          let smallItem = smallList[j]
          if (smallItem.isSmallEdit) {
            smallEdit = true
            break
          }
        }

        if (smallEdit && JSON.stringify(this.preKeyList[i]) !== JSON.stringify(item)) {
          this.handleSaveParent(i, 'regular')
        }
      }
    }
    //记录当前数据
    this.preKeyList = JSON.parse(JSON.stringify(list))
  }

  componentDidUpdate(prevProps: Readonly<IProps>) {
    if (prevProps.leafKey !== this.props.leafKey) {
      if (this.props.leafKey.includes('-')) {
        const [dateId, menuType] = this.props.leafKey.split('-')
        this.setState({ dateId, menuType, selectedRows: [] }, () => {
          this.initializeData()
        })
      }
    }
  }

  initializeData = async () => {
    this.context.showLoading()
    try {
      const res = await getDictListByName({
        categoryType: '业绩指标考核',
        type: '指标名称',
        orgId: this.props.orgId
      })
      this.setState({ zbmcList: res.data.list })
      this.getPerformanceList()
    } catch (error) {
      console.error('Error loading data:', error)
    } finally {
      this.context.hideLoading()
    }
  }

  getKpList = async (list) => {
    let promises = list.map((item) => {
      if (item.indicatorName) {
        return getDictListByName({
          categoryType: '业绩指标考核',
          type: '考评依据-' + item.indicatorName,
          orgId: this.props.orgId
        }).then((res) => {
          return { key: item.indicatorName, value: res.data.list }
        })
      }
      return Promise.resolve(null)
    })

    Promise.all(promises).then((results) => {
      const kpList = new Map()
      results.forEach((result) => {
        if (result) {
          kpList.set(result.key, result.value)
        }
      })
      this.setState({ kpyjList: kpList, performanList: list })
    })
  }

  getPerformanceList = async () => {
    let params: any = {
      date: this.state.dateId,
      orgId: this.props.orgId
    }
    if (this.props.permission == 'view') {
      params = {
        ...params,
        status: '1',
        source: 'audit'
      }
    }
    try {
      getPerfAssessList(params).then((res) => {
        let list = res.data.list
        let performanceList = []
        let selectedZb = []
        list.map((item) => {
          let obj = {
            id: item.piarId,
            pprId: item.pprId,
            indicatorName: item.indicatorName,
            assessContent: item.assessContent,
            hj: 0,
            isEdit: item.indicatorName && item.assessContent ? false : true,
            sort: item.sort,
            allLineList: []
          }
          let totalScore = 0
          item.allLineList.map((obj2) => {
            let obj3 = {
              bmmc: obj2.lineList.map((item) => item.orgName).join('、'),
              bmmcCode: obj2.lineList.map((item) => item.orgId),
              score: obj2.score,
              reason: obj2.lineList[0].reason,
              rowIdx: obj2.rowIdx,
              isSmallEdit: obj2.score && obj2.lineList[0].reason ? false : true
            }
            totalScore = totalScore + obj3.bmmcCode.length * obj3.score
            obj.allLineList.push(obj3)
          })
          selectedZb.push(item.indicatorName)
          // totalScore取一位小数
          obj.hj = getNumber(totalScore)

          if (item.allLineList.length == 0) {
            obj.allLineList.push({
              bmmc: '',
              bmmcCode: [],
              score: '',
              reason: '',
              isSmallEdit: true
            })
          }
          performanceList.push(obj)
        })
        this.getKpList(performanceList)
        this.setState({ selectedZb })
      })
    } catch (error) {
      console.error('Error fetching performance list:', error)
    } finally {
      this.context.hideLoading()
    }
  }

  getBmList = () => {
    getOrgList('01,02,03', '1').then((res) => {
      this.setState({ bmList: res.data.list })
    })
  }

  //提交前校验
  submitYz = () => {
    //默认填写完成
    let flag = true
    //判断是否有正在填写的内容
    let list = this.state.performanList
    for (let i = 0; i < list.length; i++) {
      let item = list[i]
      if (item.isEdit) {
        flag = false
        Modal.warning({
          title: '提示',
          content: `【业绩指标考核填报】中，第${i + 1}行“指标名称、考评内容”需填写完成，并保存！`,
          zIndex: 1100
        })
        break
      }
      let bmqkList = list[i].allLineList
      for (let j = 0; j < bmqkList.length; j++) {
        let bmqkItem = bmqkList[j]
        if (bmqkItem.isSmallEdit) {
          flag = false
          Modal.warning({
            title: '提示',
            content: `【业绩指标考核填报】中，第${i + 1}行"部门名称、得分、考评依据"需填写完成，并保存！`,
            zIndex: 1100
          })
          break
        }
        if (!bmqkItem.reason) {
          flag = false
          Modal.warning({
            title: '提示',
            content: `【业绩指标考核填报】中，第${i + 1}行"考评依据"需填写完成，并保存！`,
            zIndex: 1100
          })
          break
        }
        if (!flag) break
      }

      if (!flag) break
    }

    return flag
  }

  //新增一行
  handleAdd = () => {
    let list = [...this.state.performanList]
    for (let i = 0; i < list.length; i++) {
      let obj = list[i]
      // 如果有正在编辑的内容，不能进行新增
      if (obj.isEdit) {
        // if (obj.isEdit || obj.allLineList.filter((item) => item.isSmallEdit).length > 0) {
        Modal.warning({
          title: '提示',
          content: '有正在编辑的内容，请完成后，再新增一行！',
          zIndex: 1100
        })
        return
      }
    }
    list.push({
      id: '',
      indicatorName: '',
      assessContent: '',
      hj: 0,
      isEdit: true,
      //临时id
      cacheId: getUUID(),
      allLineList: [
        {
          bmmc: '',
          bmmcCode: [],
          score: '',
          reason: '',
          isSmallEdit: true
        }
      ]
    })
    this.setState({ performanList: list })
  }

  //批量删除行
  handleDelete = () => {
    let _this = this
    Modal.confirm({
      title: '删除提示',
      content: '确定删除吗?',
      onOk: () => {
        let allId = _this.state.performanList.map((item) => item.id)
        let cachIdList = _this.state.selectedRows.filter((item) => !allId.includes(item))
        let idList = _this.state.selectedRows.filter((item) => allId.includes(item))
        if (idList.length === 0) {
          let list = _this.state.performanList.filter((item) => !cachIdList.includes(item.cacheId))
          _this.setState({ performanList: list, selectedRows: [], selectedZb: [] })
          return
        }
        _this.context.showLoading()
        //调用删除接口
        deletePerfAssess({ ids: idList.join(',') })
          .then((res: any) => {
            if (res.code == '0') {
              _this.context.hideLoading()
              //重新获取已选中的指标
              let selectedZb = []
              let schList = _this.state.performanList.filter(
                (item) => !_this.state.selectedRows.includes(item.id || item.cacheId)
              )
              schList.map((item) => selectedZb.push(item.indicatorName))
              _this.setState({ performanList: schList, selectedRows: [], selectedZb })
            }
          })
          .finally(() => {
            this.context.hideLoading()
          })
      }
    })
  }

  handleContent = (value, index, tdName, indexSmall?: number) => {
    let list = [...this.state.performanList]
    //如果是zbmcCode指标名称，将选择的保存到已选指标中
    if (tdName === 'indicatorName') {
      if (value) {
        // 先将之前的指标删除，再将新的指标添加
        let selectedZb = [...this.state.selectedZb]
        selectedZb = selectedZb.filter((item) => item !== list[index].indicatorName)
        selectedZb.push(value)
        this.setState({ selectedZb })
        // 根据指标名称，获取考评依据
        getDictListByName({
          categoryType: '业绩指标考核',
          type: '考评依据-' + value,
          orgId: this.props.orgId
        }).then((res) => {
          this.state.kpyjList.set(value, res.data.list)
          list[index].allLineList.map((item) => {
            item.reason = ''
          })
        })
      } else {
        //如果value是空，获取之前选择的内容，再已选择的指标中删除
        let orginZb = list[index].indicatorName
        let selectedZb = [...this.state.selectedZb]
        selectedZb = selectedZb.filter((item) => item !== orginZb)
        this.setState({ selectedZb })
      }
      list[index].allLineList.map((item) => {
        item.isSmallEdit = true
      })
      this.setState({ performanList: list })
    }
    if (indexSmall || indexSmall == 0) {
      list[index].allLineList[indexSmall][tdName] = value
    } else {
      list[index][tdName] = value
    }

    //如果部门和得分，则需要计算合计
    if (tdName === 'bmmcCode' || tdName === 'score') {
      let count = this.getHj(index, list)
      list[index].hj = count
    }
    this.setState({ performanList: list })
  }

  //计算合计
  getHj = (index, list) => {
    let count = 0
    let bmqkList = list[index].allLineList
    for (let i = 0; i < bmqkList.length; i++) {
      let obj = bmqkList[i]
      count = count + obj.bmmcCode.length * obj.score
    }
    return getNumber(count)
  }

  //保存大行
  handleSaveParent = (index, type?: string) => {
    //校验
    let item = this.state.performanList[index]
    if (!item.indicatorName) {
      if (!type) {
        Modal.warning({
          title: '提示',
          content: '请选择指标名称！',
          zIndex: 1100
        })
      }

      return
    }
    if (!item.assessContent.trim()) {
      if (!type) {
        Modal.warning({
          title: '提示',
          content: '请填写考评内容！',
          zIndex: 1100
        })
      }
      return
    }
    //自动保存校验小行内容
    if (type) {
      //校验
      let smallList = item.allLineList
      for (let i = 0; i < smallList.length; i++) {
        let objSmall = smallList[i]
        //部门名称不能为空
        if (objSmall.bmmcCode.length == 0) {
          return
        }
        //得分不能为空
        if (!objSmall.score) {
          return
        }
        //考评依据不能为空
        if (!objSmall.reason) {
          return
        }
        // 当前行的指标名称
        let zbmc = item.indicatorName
        // 寻找所有的相同指标的数据
        let sameZbList = this.state.performanList.filter((item) => item.indicatorName === zbmc)
        for (let j = 0; j < objSmall?.bmmcCode?.length; j++) {
          let orgId = objSmall.bmmcCode[j]
          let total = 0
          for (let k = 0; k < sameZbList?.length; k++) {
            let item1 = sameZbList[k]
            for (let m = 0; m < item1?.allLineList?.length; m++) {
              let item2 = item1.allLineList[m]
              if (item2.bmmcCode.includes(orgId)) {
                total = total + item2.score
              }
            }
          }
          if (getNumber(total) > 0.5) {
            return
          }
        }
      }
    }
    let list = [...this.state.performanList]
    let params = this.getSaveData(list, index)
    if (!type) {
      this.context.showLoading()
    }
    savePerfAssess(params)
      .then((res) => {
        list[index].id = res.data.piarId
        list[index].pprId = res.data.pprId
        list[index].sort = res.data.sort
        let smallList = list[index].allLineList
        for (let i = 0; i < smallList.length; i++) {
          smallList[i].rowIdx = res.data.allLineList[i]?.rowIdx
        }
        if (!type) {
          list[index].isEdit = false
        }

        this.setState({ performanList: list })
      })
      .finally(() => {
        if (!type) {
          this.context.hideLoading()
        }
      })
  }

  //编辑大行
  handleEditParent = (index) => {
    let list = [...this.state.performanList]
    list[index].isEdit = true
    this.setState({ performanList: list })
  }

  //保存一行
  handleSaveSmall = (index, indexSmall, type?: string) => {
    //校验
    let obj = this.state.performanList[index].allLineList[indexSmall]
    if (obj.bmmcCode.length == 0) {
      if (!type) {
        Modal.warning({
          title: '提示',
          content: '请选择部门名称！',
          zIndex: 1100
        })
      }

      return
    }
    if (!obj.score) {
      if (!type) {
        Modal.warning({
          title: '提示',
          content: '请输入得分！',
          zIndex: 1100
        })
      }

      return
    }
    if (!obj.reason) {
      if (!type) {
        Modal.warning({
          title: '提示',
          content: '请选择考评依据！',
          zIndex: 1100
        })
      }

      return
    }
    let list2 = [...this.state.performanList]
    // 当前行的指标名称
    let zbmc = list2[index].indicatorName
    // 寻找所有的相同指标的数据
    let sameZbList = list2.filter((item) => item.indicatorName === zbmc)
    // 循环当前行选择的部门，计算相同部门的得分总和，如果有部门的总和大于0.5，则提示，同一指标下xxx部门的得分总和不能大于0.5
    let flag = false
    let errInfo = {
      indicatorName: '',
      orgName: ''
    }
    obj.bmmcCode.map((orgId) => {
      let total = 0
      sameZbList.map((item) => {
        item.allLineList.map((item2) => {
          if (item2.bmmcCode.includes(orgId)) {
            total = total + item2.score
          }
        })
      })
      if (getNumber(total) > 0.5) {
        flag = true
        // 将该部门和指标名称保存到errInfo中
        errInfo = {
          indicatorName: this.state.zbmcList?.find((dict) => dict.dictKey === zbmc)?.value,
          orgName: this.state.bmList.filter((zditem) => zditem.id == orgId)[0].orgName
        }
      }
    })
    if (flag) {
      if (!type) {
        Modal.warning({
          title: '提示',
          // xxx部门的xxx指标的总得分已超过0.5分，请检查
          content: `【${errInfo.orgName}】部门的【${errInfo.indicatorName}】指标的总得分已超过0.5分，请检查！`
        })
      }

      return
    }

    if (!type) {
      this.context.showLoading()
    }

    let data = this.getSaveData(list2, index)
    savePerfAssess(data)
      .then((res) => {
        this.context.hideLoading()
        //根据职能部门字典，查找职能部门名称
        let bmmcList = list2[index]['allLineList'][indexSmall].bmmcCode
        let bms = []
        bmmcList.map((id) => {
          let znbmKey = this.state.bmList.filter((item) => item.id == id)
          bms.push(znbmKey[0].orgName)
        })
        let bm = bms.join('、')
        list2[index]['allLineList'][indexSmall].bmmc = bm
        list2[index].id = res.data.piarId
        list2[index].pprId = res.data.pprId
        let smallList = list2[index].allLineList
        for (let i = 0; i < smallList.length; i++) {
          smallList[i].rowIdx = res.data.allLineList[i]?.rowIdx
        }
        if (!type) {
          list2[index]['allLineList'][indexSmall].isSmallEdit = false
        }

        this.setState({ performanList: list2 })
      })
      .finally(() => {
        if (!type) {
          this.context.hideLoading()
        }
      })
  }

  //编辑
  handleEditSmall = (index, indexSmall) => {
    let list = [...this.state.performanList]
    list[index]['allLineList'][indexSmall].isSmallEdit = true
    this.setState({ performanList: list })
  }

  //删除小行
  handleDeleteSmall = (index, indexSmall) => {
    let _this = this
    Modal.confirm({
      title: '删除提示',
      content: '确定删除吗?',
      onOk: () => {
        let list = [..._this.state.performanList]
        list[index]['allLineList'].splice(indexSmall, 1)
        _this.context.showLoading()
        //调用删除接口
        let data = _this.getSaveData(list, index)
        savePerfAssess(data)
          .then((res: any) => {
            if (res.code == '0') {
              //重新计算合计
              let count = _this.getHj(index, list)
              list[index].hj = count
              _this.setState({ performanList: list })
              _this.context.hideLoading()
            }
          })
          .finally(() => {
            this.context.hideLoading()
          })
      }
    })
  }

  //新增一小行
  handleAddSmall = (index) => {
    //判断有无正在编辑的内容，如果有，不能进行编辑，提示：有正在编辑的内容，请完成后，再新增一行
    let flag = false //默认没有编辑的
    let yyList = this.state.performanList[index].allLineList
    for (let i = 0; i < yyList.length; i++) {
      let obj = yyList[i]
      if (obj.isSmallEdit) {
        flag = true
        break
      }
    }
    if (flag) {
      Modal.warning({
        title: '提示',
        content: '有正在编辑的内容，请完成后，再新增一行！',
        zIndex: 1100
      })
      return
    }
    let list = [...this.state.performanList]
    list[index].allLineList.push({
      bmmc: '',
      bmmcCode: [],
      score: '',
      kpyj: '',
      reason: '',
      isSmallEdit: true
    })
    this.setState({ performanList: list })
  }

  getSaveData = (list, index) => {
    // list.map((item) => {
    let item = list[index]
    let obj = {
      piarId: item.id,
      pprId: item.pprId,
      indicatorName: item.indicatorName,
      assessContent: item.assessContent,
      date: this.state.dateId,
      allLineList: [],
      sort: index == 0 ? item.sort || 1 : parseInt(list[index - 1].sort + 1)
    }
    item.allLineList.map((obj2) => {
      let obj3 = {
        score: obj2.score,
        reason: obj2.reason,
        lineList: []
      }
      obj2.bmmcCode.map((orgId) => {
        obj3.lineList.push({
          orgId: orgId,
          orgName: this.state.bmList.filter((zditem) => zditem.id == orgId)[0].orgName
        })
      })
      obj.allLineList.push(obj3)
    })
    return obj
  }

  //复选框
  handleCheckBox = (value, key) => {
    if (value) {
      //true
      let list = [...this.state.selectedRows]
      list.push(key)
      this.setState({ selectedRows: list })
    } else {
      //false
      let list = [...this.state.selectedRows]
      let index = list.indexOf(key)
      if (index > -1) {
        list.splice(index, 1)
      }
      this.setState({ selectedRows: list })
    }
  }

  //全选，全不选
  onCheckAllChange = () => {
    let selectedRows = []
    if (this.state.performanList.length > 0) {
      if (this.state.performanList.length != this.state.selectedRows.length) {
        //全选
        let list = this.state.performanList
        list.map((item) => {
          selectedRows.push(item.id || item.cacheId)
        })
      }
    }
    this.setState({
      selectedRows: selectedRows
    })
  }

  openModalTable = (list, reason, index, index2) => {
    // 如果没有选择指标名称，提示选择指标名称
    if (!this.state.performanList[index].indicatorName) {
      Modal.warning({
        title: '提示',
        content: '请先选择指标名称！',
        zIndex: 1100
      })
      return
    }
    if (!list || list.length == 0) {
      Modal.warning({
        title: '提示',
        content: '当前指标没有考评依据',
        zIndex: 1100
      })
      return
    }
    let _this = this
    let list2 = list.map((dict) => {
      return {
        dictKey: dict.dictKey,
        value: dict.value
      }
    })
    _this.setState({
      isModalVisible: true,
      index,
      smallIndex: index2,
      reasonItem: { list, select: reason },
      RowReasonItem: { list: list2, select: reason }
    })
  }

  handleOk = () => {
    let list = [...this.state.performanList]
    let index = this.state.index
    let index2 = this.state.smallIndex
    let reasonItem = this.state.reasonItem
    list[index].allLineList[index2].reason = reasonItem.select
    this.handleContent(reasonItem.select, index, 'reason', index2)
    this.setState({ isModalVisible: false })
  }

  //选择部门名称
  openModalDepartMent = (indexBig, indexSmall) => {
    let list = [...this.state.performanList]
    let selectedCode = list[indexBig].allLineList[indexSmall].bmmcCode
    this.setState({ departMent: { isDepartVisible: true, indexBig, indexSmall, selectedCode } })
  }

  //部门选择确定
  determine = (selectedList) => {
    let list = [...this.state.performanList]
    let indexBig = this.state.departMent.indexBig
    let indexSmall = this.state.departMent.indexSmall
    let bmmcCode = []
    selectedList?.map((item) => bmmcCode.push(item.id))
    list[indexBig].allLineList[indexSmall].bmmcCode = bmmcCode
    list[indexBig].allLineList[indexSmall].bmmc = selectedList?.map((item) => item.orgName).join('，')
    let count = this.getHj(indexBig, list)
    list[indexBig].hj = count
    this.setState({ performanList: list })
  }

  //移动
  handleMove = (index, type) => {
    let list = [...this.state.performanList]
    if (type == 'up') {
      if (index == 0) {
        return
      }
      let temp = list[index]
      let temp2 = list[index - 1]
      if (!temp2?.id || !temp?.id) {
        return
      }
      list[index] = list[index - 1]
      list[index - 1] = temp
      this.saveSwap(temp2.id, temp.id)
    } else if (type == 'down') {
      if (index == list.length - 1) {
        return
      }
      let temp = list[index]
      let temp2 = list[index + 1]
      if (!temp2?.id || !temp?.id) {
        return
      }
      list[index] = list[index + 1]
      list[index + 1] = temp
      this.saveSwap(temp2.id, temp.id)
    } else if (type == 'top') {
      if (index == 0) {
        return
      }
      //置顶
      let temp = list[index]
      if (!temp?.id) {
        return
      }
      list.splice(index, 1)
      list.unshift(temp)
      this.savePinned(temp.id)
    } else if (type == 'bottom') {
      //暂时无置底功能
      if (index == list.length - 1) {
        return
      }
      //置底
      let temp = list[index]
      list.splice(index, 1)
      list.push(temp)
      // this.savePinned(temp.id, '2')
    }
    this.setState({ performanList: list })
  }

  saveSwap = (id1, id2) => {
    this.context.showLoading()
    indicatorAssessReportSwap({ id1: id1, id2: id2 })
      .then((res) => {
        this.getPerformanceList()
      })
      .finally(() => {
        this.context.hideLoading()
      })
  }

  savePinned = (id) => {
    this.context.showLoading()
    indicatorAssessReportPinned({ date: this.state.dateId, piarId: id })
      .then((res) => {
        this.getPerformanceList()
      })
      .finally(() => {
        this.context.hideLoading()
      })
  }

  //移动小行
  handleMoveSmall = (index, indexSmall, type) => {
    let list = [...this.state.performanList]
    let listSmall = list[index].allLineList
    if (type == 'up') {
      if (indexSmall == 0) {
        return
      }
      let temp = listSmall[indexSmall]
      let temp2 = listSmall[indexSmall - 1]
      if (!list[index]?.id || !temp?.rowIdx?.toString() || !temp2?.rowIdx?.toString()) {
        return
      }
      listSmall[indexSmall] = listSmall[indexSmall - 1]
      listSmall[indexSmall - 1] = temp
      this.saveSwapSmall(list[index].id, temp.rowIdx, temp2.rowIdx)
    } else if (type == 'down') {
      if (indexSmall == listSmall.length - 1) {
        return
      }
      let temp = listSmall[indexSmall]
      let temp2 = listSmall[indexSmall + 1]
      if (!list[index]?.id || !temp.rowIdx?.toString() || !temp2.rowIdx?.toString()) {
        return
      }
      listSmall[indexSmall] = listSmall[indexSmall + 1]
      listSmall[indexSmall + 1] = temp
      this.saveSwapSmall(list[index].id, temp.rowIdx, temp2.rowIdx)
    } else if (type == 'top') {
      if (indexSmall == 0) {
        return
      }
      //置顶
      let temp = listSmall[indexSmall]
      if (!list[index]?.id || !temp.rowIdx?.toString()) {
        return
      }
      listSmall.splice(indexSmall, 1)
      listSmall.unshift(temp)
      this.savePinnedSmall(list[index].id, temp.rowIdx)
    } else if (type == 'bottom') {
      //暂时无置底功能
      if (indexSmall == listSmall.length - 1) {
        return
      }
      //置底
      let temp = listSmall[indexSmall]
      listSmall.splice(indexSmall, 1)
      listSmall.push(temp)
    }
    this.setState({ performanList: list })
  }

  saveSwapSmall = (piarId: string, rowIdx1: number, rowIdx2: number) => {
    this.context.showLoading()
    indicatorAssessOrgSwap({ piarId, rowIdx1, rowIdx2 })
      .then((res) => {
        this.getPerformanceList()
      })
      .finally(() => {
        this.context.hideLoading()
      })
  }

  savePinnedSmall = (piarId: string, rowIdx: number) => {
    this.context.showLoading()
    indicatorAssessOrgPinned({ piarId, rowIdx })
      .then((res) => {
        this.getPerformanceList()
      })
      .finally(() => {
        this.context.hideLoading()
      })
  }

  render(): React.ReactNode {
    return (
      <>
        <div style={{ height: '100%' }}>
          {this.props.permission != 'view' && (
            <div style={{ display: 'flex', justifyContent: 'end' }}>
              <div
                className={`common-button2 common-button3 ${
                  // (this.state.selectedZb.length == this.state.zbmcList.length || !this.props.isVisible) &&
                  !this.props.isVisible && 'disabled-div'
                }`}
                onClick={() => this.handleAdd()}
              >
                <Iconfont type="icon-xinzeng" style={{ fontSize: '20px' }} />
                <div className="common-text2">新增一行</div>
              </div>
              <div
                style={{ marginLeft: '10px' }}
                className={`common-button2 common-button3 ${
                  (this.state.selectedRows.length == 0 || !this.props.isVisible) && 'disabled-div'
                }`}
                onClick={() => this.handleDelete()}
              >
                <Iconfont type="icon-shanchu" style={{ fontSize: '20px' }} />
                <div className="common-text">删除</div>
              </div>
            </div>
          )}

          <div style={{ height: 'calc(100% - 35px)' }}>
            <div className="div-table">
              <table className="common-table">
                <thead>
                  <tr>
                    {this.props.permission != 'view' && (
                      <th>
                        <Checkbox
                          onChange={() => this.onCheckAllChange()}
                          checked={
                            this.state.performanList.length > 0 &&
                            this.state.selectedRows.length == this.state.performanList.length
                              ? true
                              : false
                          }
                        ></Checkbox>
                      </th>
                    )}

                    <th>序号</th>
                    {this.props.permission != 'view' && <th>顺序调整</th>}
                    <th>指标名称</th>
                    <th>考评内容</th>
                    <th>部门名称</th>
                    <th>得分</th>
                    <th>合计</th>
                    <th>考评依据</th>
                    {this.props.permission != 'view' && <th>操作</th>}
                  </tr>
                </thead>
                <tbody>
                  {this.state.performanList.map((item, index) => {
                    return item.allLineList.map((obj, index2) => {
                      return (
                        <tr key={`${index}-${index2}`}>
                          {this.props.permission != 'view' && index2 == 0 && (
                            <td style={{ width: '45px' }} rowSpan={item.allLineList.length}>
                              <Checkbox
                                checked={this.state.selectedRows.includes(item.id || item.cacheId)}
                                onChange={(e) => this.handleCheckBox(e.target.checked, item.id || item.cacheId)}
                              ></Checkbox>
                            </td>
                          )}

                          {index2 == 0 && (
                            <td style={{ width: '45px' }} rowSpan={item.allLineList.length}>
                              {index + 1}
                            </td>
                          )}
                          {index2 == 0 && this.props.permission != 'view' && (
                            <td style={{ width: '70px' }} rowSpan={item.allLineList.length}>
                              <div className="table-operate">
                                <div
                                  className={`${!this.props.isVisible && 'disabled-table-div'}`}
                                  onClick={() => this.handleMove(index, 'up')}
                                >
                                  <Iconfont title="上移" type="icon-shangyimian" style={{ fontSize: '16px' }} />
                                </div>
                                <div
                                  style={{ paddingLeft: '3px' }}
                                  className={`${!this.props.isVisible && 'disabled-table-div'}`}
                                  onClick={() => this.handleMove(index, 'down')}
                                >
                                  <Iconfont title="下移" type="icon-xiayimian" style={{ fontSize: '16px' }} />
                                </div>
                                <div
                                  style={{ paddingLeft: '3px' }}
                                  className={`${!this.props.isVisible && 'disabled-table-div'}`}
                                  onClick={() => this.handleMove(index, 'top')}
                                >
                                  <Iconfont title="置顶" type="icon-zhidingmian" style={{ fontSize: '16px' }} />
                                </div>
                              </div>
                            </td>
                          )}
                          {index2 == 0 && (
                            <td style={{ width: '140px' }} rowSpan={item.allLineList.length}>
                              {item.isEdit && this.props.isVisible ? (
                                <Select
                                  style={{ width: '130px' }}
                                  placeholder="请选择指标名称"
                                  value={item.indicatorName}
                                  onChange={(selected) => {
                                    this.handleContent(selected, index, 'indicatorName')
                                  }}
                                  showSearch
                                  optionFilterProp="children"
                                  filterOption={(input: any, option: any) => option.children.indexOf(input) >= 0}
                                >
                                  {this.state.zbmcList.map((dict, dictIndex) => (
                                    <Select.Option
                                      // disabled={this.state.selectedZb.includes(dict.dictKey)}
                                      key={dict.dictKey}
                                      value={dict.dictKey}
                                    >
                                      {dict.value}
                                    </Select.Option>
                                  ))}
                                </Select>
                              ) : (
                                this.state.zbmcList?.find((dict) => dict.dictKey === item.indicatorName)?.value
                              )}
                            </td>
                          )}
                          {index2 == 0 && (
                            <td rowSpan={item.allLineList.length}>
                              {item.isEdit && this.props.isVisible ? (
                                <div style={{ display: 'flex', alignItems: 'center' }}>
                                  <TextArea
                                    autoSize
                                    maxLength={1000}
                                    // showCount
                                    value={item.assessContent}
                                    placeholder="请填写考评内容"
                                    onChange={(obj) => this.handleContent(obj.target.value, index, 'assessContent')}
                                  />
                                  <div className="row-opera" onClick={() => this.handleSaveParent(index)}>
                                    <Iconfont type="icon-icon_duihao-mian" style={{ fontSize: '16px' }} />
                                    <span className="operate-text">保存</span>
                                  </div>
                                </div>
                              ) : this.props.isVisible ? (
                                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                                  <div className="row-text">{item.assessContent}</div>
                                  <div className="row-opera" onClick={() => this.handleEditParent(index)}>
                                    <Iconfont type="icon-bianji3" style={{ fontSize: '16px' }} />
                                    <span className="operate-text">编辑</span>
                                  </div>
                                </div>
                              ) : (
                                item.assessContent
                              )}
                            </td>
                          )}
                          <td style={{ width: '130px' }}>
                            {obj.bmmc}
                            {obj.isSmallEdit && (
                              <div
                                style={{ cursor: 'pointer', color: '#1890ff' }}
                                onClick={() => this.openModalDepartMent(index, index2)}
                              >
                                点击选择部门名称
                              </div>
                            )}
                          </td>
                          <td style={{ width: '50px' }}>
                            {obj.isSmallEdit ? (
                              <InputNumber
                                style={{ width: '50px' }}
                                controls={false}
                                precision={decimalPlaces}
                                // max={0.5}
                                value={obj.score}
                                onChange={(value) => {
                                  if (!validateNumber(value, '请输入<=0.5的数值！', 0.5, '')) return
                                  this.handleContent(value, index, 'score', index2)
                                }}
                              />
                            ) : (
                              obj.score
                            )}
                          </td>
                          {index2 == 0 && (
                            <td style={{ width: '50px' }} rowSpan={item.allLineList.length}>
                              {item.hj}
                            </td>
                          )}
                          <td style={{ width: '290px' }}>
                            {obj.isSmallEdit ? (
                              obj.reason ? (
                                <div
                                  style={{
                                    border: '1px solid #95bcec',
                                    cursor: 'pointer',
                                    borderRadius: '5px',
                                    backgroundColor: '#fff',
                                    textAlign: 'left'
                                  }}
                                  onClick={() =>
                                    this.openModalTable(
                                      this.state.kpyjList.get(item.indicatorName),
                                      obj.reason,
                                      index,
                                      index2
                                    )
                                  }
                                >
                                  <span style={{ padding: '0 5px' }}>
                                    {
                                      this.state.kpyjList
                                        .get(item.indicatorName)
                                        ?.find((dict) => dict.dictKey === obj.reason)?.value
                                    }
                                  </span>
                                </div>
                              ) : (
                                <div
                                  style={{
                                    display: 'flex',
                                    alignItems: 'center',
                                    cursor: 'pointer',
                                    justifyContent: 'center',
                                    color: '#1890ff'
                                  }}
                                  onClick={() =>
                                    this.openModalTable(
                                      this.state.kpyjList.get(item.indicatorName),
                                      obj.reason,
                                      index,
                                      index2
                                    )
                                  }
                                >
                                  <span>点击选择考评依据</span>
                                </div>
                              )
                            ) : (
                              <div
                                style={{
                                  textAlign: 'start'
                                }}
                              >
                                {
                                  this.state.kpyjList
                                    .get(item.indicatorName)
                                    ?.find((dict) => dict.dictKey === obj.reason)?.value
                                }
                              </div>
                            )}
                          </td>
                          {this.props.permission != 'view' && (
                            <td style={{ width: '150px' }}>
                              <div className="table-operate" style={{ justifyContent: 'left' }}>
                                {obj.isSmallEdit ? (
                                  <div
                                    className={`${!this.props.isVisible && 'disabled-table-div'}`}
                                    onClick={() => this.handleSaveSmall(index, index2)}
                                  >
                                    <Iconfont type="icon-icon_duihao-mian" style={{ fontSize: '16px' }} />
                                    <span className="operate-text">保存</span>
                                  </div>
                                ) : (
                                  <div
                                    className={`${!this.props.isVisible && 'disabled-table-div'}`}
                                    onClick={() => this.handleEditSmall(index, index2)}
                                  >
                                    <Iconfont type="icon-bianji3" style={{ fontSize: '16px' }} />
                                    <span className="operate-text">编辑</span>
                                  </div>
                                )}

                                {index2 != 0 && (
                                  <div
                                    className={`${!this.props.isVisible && 'disabled-table-div'}`}
                                    style={{ paddingLeft: '10px' }}
                                    onClick={() => this.handleDeleteSmall(index, index2)}
                                  >
                                    <Iconfont type="icon-shanchu" style={{ fontSize: '16px' }} />
                                    <span className="operate-text">删除</span>
                                  </div>
                                )}

                                {index2 == 0 && (
                                  <div
                                    className={`${!this.props.isVisible && 'disabled-table-div'}`}
                                    style={{ paddingLeft: '10px' }}
                                    onClick={() => this.handleAddSmall(index)}
                                  >
                                    <Iconfont type="icon-xinzeng" style={{ fontSize: '16px' }} />
                                    <span className="operate-text">新增一行</span>
                                  </div>
                                )}
                              </div>
                              <div className="table-operate" style={{ justifyContent: 'center', paddingTop: 5 }}>
                                <div
                                  className={`${!this.props.isVisible && 'disabled-table-div'}`}
                                  onClick={() => this.handleMoveSmall(index, index2, 'up')}
                                >
                                  <Iconfont title="上移" type="icon-shangyimian" style={{ fontSize: '16px' }} />
                                </div>
                                <div
                                  style={{ paddingLeft: '3px' }}
                                  className={`${!this.props.isVisible && 'disabled-table-div'}`}
                                  onClick={() => this.handleMoveSmall(index, index2, 'down')}
                                >
                                  <Iconfont title="下移" type="icon-xiayimian" style={{ fontSize: '16px' }} />
                                </div>
                                <div
                                  style={{ paddingLeft: '3px' }}
                                  className={`${!this.props.isVisible && 'disabled-table-div'}`}
                                  onClick={() => this.handleMoveSmall(index, index2, 'top')}
                                >
                                  <Iconfont title="置顶" type="icon-zhidingmian" style={{ fontSize: '16px' }} />
                                </div>
                              </div>
                            </td>
                          )}
                        </tr>
                      )
                    })
                  })}
                </tbody>
              </table>
            </div>
          </div>
        </div>
        <ModalInfo
          title="选择考评依据"
          visible={this.state.isModalVisible}
          width="1150px"
          maxHeight="800px"
          onClose={() => this.setState({ isModalVisible: false })}
        >
          <div>
            <div style={{ display: 'flex', justifyContent: 'start', marginBottom: '10px', position: 'sticky', top: 0 }}>
              <Input
                style={{ width: '200px' }}
                placeholder="请输入关键字"
                onChange={debounce((e) => {
                  if (!e.target.value) {
                    this.setState({
                      reasonItem: { list: this.state.RowReasonItem.list, select: this.state.RowReasonItem.select }
                    })
                    return
                  }
                  let list = this.state.RowReasonItem.list
                  list = list.filter((item) => item.value.indexOf(e.target.value) > -1)
                  this.setState({ reasonItem: { list, select: this.state.reasonItem.select } })
                }, 200)}
              />
            </div>
            <div style={{ maxHeight: '600px', overflow: 'auto' }}>
              <table className="common-table">
                <thead>
                  <tr>
                    <th>选择</th>
                    <th>考评依据</th>
                  </tr>
                </thead>
                <tbody>
                  {this.state.reasonItem.list.map((item, index) => {
                    return (
                      <tr
                        key={index}
                        onClick={() => {
                          this.setState({ reasonItem: { list: this.state.reasonItem.list, select: item.dictKey } })
                        }}
                        onDoubleClick={() => {
                          this.setState({ reasonItem: { list: this.state.reasonItem.list, select: item.dictKey } })
                          this.handleOk()
                        }}
                        style={{
                          cursor: 'pointer',
                          backgroundColor: this.state.reasonItem.select === item.dictKey && '#f0f0f0'
                        }}
                      >
                        <td style={{ width: '45px' }}>
                          <Checkbox
                            checked={this.state.reasonItem.select === item.dictKey}
                            onChange={(e) => {
                              if (e.target.checked) {
                                let list = [...this.state.reasonItem.list]
                                list.map((obj) => {
                                  obj.dictKey === item.dictKey ? (obj.checked = true) : (obj.checked = false)
                                })
                                this.setState({
                                  reasonItem: {
                                    list: list,
                                    select: item.dictKey
                                  }
                                })
                              }
                            }}
                          ></Checkbox>
                        </td>
                        <td>
                          <div
                            style={{
                              cursor: 'pointer',
                              textAlign: 'start'
                            }}
                          >
                            {item.value}
                          </div>
                        </td>
                      </tr>
                    )
                  })}
                </tbody>
              </table>
            </div>
          </div>
          <div style={{ display: 'flex', justifyContent: 'end', marginTop: '20px' }}>
            <Button
              className="tr-btn"
              style={{ marginRight: '12px' }}
              onClick={() => this.setState({ isModalVisible: false })}
            >
              取消
            </Button>
            <Button
              type="primary"
              className="tr-btn"
              onClick={() => {
                this.handleOk()
              }}
            >
              确定
            </Button>
          </div>
        </ModalInfo>

        {this.state.departMent.isDepartVisible && (
          <Department
            isModalVisible={this.state.departMent.isDepartVisible}
            list={this.state.bmList}
            selectedCode={this.state.departMent.selectedCode}
            closeModal={() => this.setState({ departMent: { isDepartVisible: false } })}
            determine={(selectedList) => this.determine(selectedList)}
          />
        )}
      </>
    )
  }
}
