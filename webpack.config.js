const HtmlWebpackPlugin = require('html-webpack-plugin')
const CopyPlugin = require('copy-webpack-plugin')
const path = require('path')
const webpack = require('webpack')
const dotenv = require('dotenv')
dotenv.config()

function getDevMode() {
  for (let i = 0; i < process.argv.length; i++) {
    if (process.argv[i].includes('production') || process.argv[i].includes('development')) {
      return process.argv[i].includes('development') 
    }
  }
  return true
}

module.exports = {
  entry: './src/index.tsx',
  output: {
    filename: 'js/[name].[fullhash:8].js',
    publicPath: getDevMode() ? '/' : './',
    clean: true,
    assetModuleFilename: 'assets/[name].[hash:8][ext][query]'
  },
  resolve: {
    extensions: ['.ts', '.tsx', '.js', '.json'],
    alias: {
      '@': path.resolve(__dirname, 'src')
    }
  },
  plugins: [
    new HtmlWebpackPlugin({
      template: './public/index.ejs'
    }),
    new CopyPlugin({
      patterns: [{ 
        from: path.resolve(__dirname, 'public'), 
        to: './'
      }]
    }),
    new webpack.DefinePlugin({
      'process.env': {
        VITE_APP_VERSION: JSON.stringify(process.env.VITE_APP_VERSION),
        VITE_APP_COMMIT_ID: JSON.stringify(process.env.VITE_APP_COMMIT_ID)
      }
    })
  ],
  module: {
    rules: [
      {
        test: /\.tsx?$/,
        loader: 'babel-loader',
        options: {
          presets: [['@babel/preset-env', {
            "targets": {
              "chrome": "58",
              "ie": "11"
            }
          }],'@babel/preset-react', '@babel/preset-typescript'],
          plugins: [['@babel/plugin-proposal-decorators', { legacy: true }], '@babel/plugin-transform-runtime']
        }
      },
      {
        test: /\.(png|jpe?g|gif)$/i,
        type: 'asset/resource'
      },
      {
        test: /\.s?css$/,
        use: ['style-loader', 'css-loader', 'sass-loader']
      },
      {
        test: /\.less$/,
        use: ['style-loader', 'css-loader', {
          loader: 'less-loader',
          options: {
            lessOptions: {
              javascriptEnabled: true
            }
          }
          
        }]
      },

    ]
  },
  devServer: {
    open: true,
    historyApiFallback: true,
    port: 10012,
    client: {
      overlay: false,
    },
    proxy: [
      {
        context: ['/api'],
        changeOrigin: true,
        target: 'http://pdas.test.tuoray.com:10000/'
      },
    ]
  }
}