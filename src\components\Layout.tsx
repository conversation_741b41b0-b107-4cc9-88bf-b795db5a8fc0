import React from 'react'
import { Outlet, NavLink } from 'react-router-dom'
import TrLayout from './TrLayout'
import TrMenu from './TRmenu'
import Header from './Header'
import axios from 'axios'
import { withRouter } from 'tr-cheers'
import router from '@/router'
import mbx from '../assets/images/mianbaoxie.png'
import { LoadingContext } from '@/components/load/loadingProvider'

// @ts-ignore
@withRouter
export default class extends React.Component {
  static contextType = LoadingContext
  context: React.ContextType<typeof LoadingContext> = this.context

  state = {
    menus: [],
    breadcrumb: ''
  }
  componentDidMount(): void {
    this.getMenu()
  }

  getMenu() {
    this.context.showLoading()
    axios
      .get('/api/v1/ca/auth/menu', {
        headers: {
          Application: 'PDAS'
        }
      })
      .then((res: any) => {
        let menus = res.data
        let data = menus.sort((a, b) => a.sort - b.sort)
        this.setState({ menus: data }, () => {
          this.menuClick(null)
        })
        this.context.hideLoading()
      })
      .catch(() => {
        this.context.hideLoading()
      })
  }

  menuClick(menu: any) {
    // 设置面包屑导航
    if (!menu) {
      // 没有菜单的面包屑导航
      let pathname = (this.props as any).location.pathname
      let r: any = router[0].children.find((item) => item.path == pathname)
      if (r) menu = this.state.menus.find((item) => item.menuUrl == r.path)
    }
    if (menu && menu.menuFname) {
      let breadcrumbs = menu.menuFname.split('_')
      if (breadcrumbs.length > 1) {
        //删除第一个元素
        breadcrumbs.shift()
      }
      let breadcrumb = breadcrumbs.join(' > ')

      this.setState({ breadcrumb })
    }
  }

  //监听路由跳转
  componentDidUpdate(prevProps: any) {
    if (prevProps.location.pathname != (this.props as any).location.pathname) {
      if ((this.props as any).location.pathname.includes('/globalSearch')) {
        this.setState({ breadcrumb: '全局搜索' })
        return
      }
      //根据pathname获取菜单
      let clickMenu = this.state.menus.filter((item) => item.menuUrl == (this.props as any).location.pathname)
      if (clickMenu && clickMenu.length > 0) {
        this.menuClick(clickMenu[0])
      }
    }
  }
  render() {
    return (
      <TrLayout
        title=""
        autoSize
        Menu={<TrMenu menus={this.state.menus as any} Level2 uniqueOpened />}
        Header={<Header />}
      >
        <div className="layout-main">
          <div className="breadcrumb F4">
            <img
              src={mbx}
              alt=""
              style={{
                verticalAlign: 'top',
                marginRight: '10px'
              }}
            />
            <div>{this.state.breadcrumb}</div>
          </div>
          <div className="layout-body">
            <Outlet />
          </div>
        </div>
      </TrLayout>
    )
  }
}
