import React from 'react'
import image from './403.png'
import { Button } from 'antd'
import { withRouter } from 'tr-cheers'

// @ts-ignore
@withRouter
export default class extends React.Component {
  render() {
    return (
      <div style={{ textAlign: 'center', height: 'calc(100vh)', position: 'relative' }}>
        <div style={{ position: 'absolute', left: '50%', top: '50%', transform: 'translate(-50%,-50%)' }}>
          <img src={image} />
          <div style={{ marginTop: '30px' }}>
            <Button type="primary" onClick={(value) => (this.props as any).navigate('/')}>
              返回首页
            </Button>
          </div>
        </div>
      </div>
    )
  }
}
