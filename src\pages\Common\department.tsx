import React from 'react'
import ModalInfo from '@/components/Modal/index'
import { Button, Input, Modal } from 'antd'
import { CloseCircleOutlined } from '@ant-design/icons'

interface IProps {
  isModalVisible: boolean
  list: Array<any>
  selectedCode?: Array<any>
  closeModal: () => void
  determine: (selectedList: Array<any>) => void
  limit?: number
}

interface IDepartment {
  id?: string
  orgName?: string
  //排序字段
  remark?: string
}

interface IState {
  showList: Array<any>
  selectedList: Array<IDepartment>
}

export default class extends React.Component<IProps> {
  state: IState = {
    showList: [],
    selectedList: []
  }
  closeModal = () => {
    this.props.closeModal()
  }

  componentDidMount(): void {
    let selectedList = []
    if (this.props.selectedCode.length > 0) {
      for (let i = 0; i < this.props.selectedCode.length; i++) {
        let data = this.props.selectedCode[i]
        for (let item of this.props.list) {
          if (item.id === data) {
            selectedList.push(item)
            break // 可以选择在找到匹配后跳出内层循环
          }
        }
      }
    }
    this.setState({ showList: this.props.list, selectedList: selectedList })
  }

  onChange = (e) => {
    let showList = this.props.list.filter((item) => item.orgName.indexOf(e.target.value) > -1)
    this.setState({ showList })
  }

  handleClick = (item) => {
    if (!item) return
    let selectedList = this.state.selectedList
    let index = selectedList.findIndex((obj) => obj.id === item.id)
    if (index !== -1) {
      selectedList.splice(index, 1)
    } else {
      if (this.props.limit) {
        if (selectedList.length >= this.props.limit) {
          Modal.warning({
            title: '提示',
            content: `最多选择${this.props.limit}个部门！`,
            zIndex: 1100
          })
          return
        }
      }
      selectedList.push(item)
    }
    selectedList.sort((a, b) => parseInt(a.remark) - parseInt(b.remark))
    this.setState({ selectedList })
  }

  //确定
  handleOk = () => {
    this.props.determine(this.state.selectedList)
    this.props.closeModal()
  }

  handleDelete = (item) => {
    let selectedList = this.state.selectedList
    let index = selectedList.findIndex((obj) => obj.id === item.id)
    if (index !== -1) {
      selectedList.splice(index, 1)
    }
    this.setState({ selectedList })
  }

  handleDeleteAll = () => this.setState({ selectedList: [] })

  renderYx = () => {
    // 将 selectedList 映射为包含名称和图标的数组，并使用逗号分隔连接起来
    return this.state.selectedList.map((item, index) => (
      <span key={item.id}>
        {item.orgName} <CloseCircleOutlined onClick={() => this.handleDelete(item)} />
        {index !== this.state.selectedList.length - 1 && ', '}
      </span>
    ))
  }

  render() {
    return (
      <div>
        <ModalInfo
          title="选择部门"
          visible={this.props.isModalVisible}
          width="1150px"
          maxHeight="800px"
          onClose={this.closeModal}
        >
          <div>
            <div style={{ fontWeight: 'bold' }}>
              已选({this.state.selectedList.length})
              <CloseCircleOutlined style={{ paddingLeft: '3px' }} onClick={() => this.handleDeleteAll()} />：
              {this.renderYx()}
            </div>
            <div
              style={{
                marginTop: '10px',
                display: 'flex',
                justifyContent: 'space-between',
                marginBottom: '10px',
                position: 'sticky',
                top: 0
              }}
            >
              <Input style={{ width: '200px' }} placeholder="请输入部门名称" onChange={(e) => this.onChange(e)} />
              <Button
                type="primary"
                className="tr-btn"
                onClick={() => {
                  this.handleOk()
                }}
              >
                确定
              </Button>
            </div>
            <div style={{ maxHeight: '500px', overflow: 'auto' }}>
              <table className="common-table">
                <thead>
                  <tr>
                    <th>序号</th>
                    <th>部门名称</th>
                    <th>序号</th>
                    <th>部门名称</th>
                  </tr>
                </thead>
                <tbody>
                  {this.state.showList.map((item, index) => {
                    let item2 = this.state.showList[index + 1]
                    if (index % 2 === 0) {
                      return (
                        <tr key={index + 1}>
                          <td
                            style={{
                              width: '10%',
                              cursor: 'pointer',
                              backgroundColor: this.state.selectedList.some((obj) => obj.id === item?.id) && '#e5fff2'
                            }}
                            onClick={() => this.handleClick(item)}
                          >
                            {index + 1}
                          </td>
                          <td
                            style={{
                              width: '40%',
                              cursor: 'pointer',
                              backgroundColor: this.state.selectedList.some((obj) => obj.id === item.id) && '#e5fff2'
                            }}
                            onClick={() => this.handleClick(item)}
                          >
                            <div>{item.orgName}</div>
                          </td>
                          <td
                            style={{
                              width: '10%',
                              cursor: 'pointer',
                              backgroundColor: this.state.selectedList.some((obj) => obj.id === item2?.id) && '#e5fff2'
                            }}
                            onClick={() => this.handleClick(item2)}
                          >
                            {item2 && index + 2}
                          </td>
                          <td
                            style={{
                              width: '40%',
                              cursor: 'pointer',
                              backgroundColor: this.state.selectedList.some((obj) => obj.id === item2?.id) && '#e5fff2'
                            }}
                            onClick={() => this.handleClick(item2)}
                          >
                            <div>{item2?.orgName}</div>
                          </td>
                        </tr>
                      )
                    }
                  })}
                </tbody>
              </table>
            </div>
          </div>
          <div style={{ display: 'flex', justifyContent: 'end', marginTop: '20px' }}>
            <Button className="tr-btn" style={{ marginRight: '12px' }} onClick={() => this.closeModal()}>
              取消
            </Button>
            <Button
              type="primary"
              className="tr-btn"
              onClick={() => {
                this.handleOk()
              }}
            >
              确定
            </Button>
          </div>
        </ModalInfo>
      </div>
    )
  }
}
