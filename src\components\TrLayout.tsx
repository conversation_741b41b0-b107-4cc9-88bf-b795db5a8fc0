import React from 'react'
import { Layout } from 'antd'
import { MenuUnfoldOutlined, MenuFoldOutlined } from '@ant-design/icons'

interface IProps {
  /** 左侧菜单的宽度 */
  menuWidth?: string
  /** 左侧是否收起 */
  collapsed?: boolean
  /** 菜单组件 */
  Menu?: React.ReactNode
  /** header高度 */
  headerHeight?: string
  /** header组件 */
  Header?: React.ReactNode
  /** 是否自动收起左侧菜单 */
  autoSize?: boolean
  /** 系统名称 */
  title?: React.ReactNode
  /** 左侧菜单收起的LOGO图片路径 */
  LogoMin?: string
  /** 左侧菜单展开的LOGO图片路径 */
  LogoMax?: string
  onClick?: () => void
  children?: React.ReactNode
}
export default class TrLayout extends React.Component<IProps> {
  state = {
    collapsed: false
  }
  TitleStyles = {
    fontSize: '18px',
    paddingLeft: '20px',
    verticalAlign: 'bottom'
  }
  LogoStyles = {
    height: '100%',
    width: '100%',
    backgroundRepeat: 'no-repeat',
    backgroundPosition: 'center center',
    backgroundSize: 'auto'
  }
  componentDidMount() {
    // window.addEventListener('resize', this.setCollapsed.bind(this), false)
  }
  componentWillUnmount() {
    window.removeEventListener('resize', this.setCollapsed)
  }
  ComponentDidUpdate(prePropes: IProps) {
    if (prePropes.collapsed !== this.props.collapsed) {
      this.setCollapsed()
    }
  }
  setCollapsed() {
    if (this.props.autoSize) {
      if (window.innerWidth < 1000 && !this.state.collapsed) this.setState({ collapsed: true })
      if (window.innerWidth >= 1000 && this.state.collapsed) this.setState({ collapsed: false })
    } else {
      if (this.props.collapsed !== this.state.collapsed) this.setState({ collapsed: this.props?.collapsed })
    }
  }
  manualCollapsed() {
    this.setState({ collapsed: !this.state.collapsed })
  }
  render() {
    return (
      <Layout style={{ height: '100%' }}>
        <Layout.Header
          style={{ height: this.props.headerHeight || '76px', display: 'flex', alignItems: 'center', padding: '0' }}
        >
          <span style={{ flex: 1 }} className="F5">
            {this.props.Header}
          </span>
        </Layout.Header>

        <Layout
          style={{
            padding: '20px',
            background: '#fff'
          }}
        >
          <Layout.Sider width={this.props.menuWidth || '160px'} collapsed={this.state.collapsed}>
            <div style={{ height: `calc(100% - ${this.props.headerHeight || '0px'})`, overflow: 'auto' }}>
              {this.props.Menu}
            </div>
          </Layout.Sider>
          <Layout.Content
            style={{
              background: '#fff',
              marginLeft: '20px',
              borderRadius: '10px'
            }}
          >
            {this.props.children}
          </Layout.Content>
        </Layout>
      </Layout>
    )
  }
}
