import React from 'react'
import { getNumber, debounce, validateNumber } from '@/utils/utils'
import { Iconfont } from 'tr-cheers'
import { Input, InputNumber, Button, Checkbox, Modal } from 'antd'
import MessageSelf from '@/components/message'
import { getDictListByName, getProfEvalReportCountyList, saveCounty, getOrgList, deleteCounty } from '@/api'
import ModalInfo from '@/components/Modal/index'
import { LoadingContext } from '@/components/load/loadingProvider'
import Decimal from 'decimal.js'
const { TextArea } = Input

interface IProps {
  //选中的是哪个节点
  leafKey: string
  //是否可见，true可见 false不可见
  isVisible?: boolean
  //权限 view 只能查看
  permission?: string
  // 部门名称，如果没传，代表是填报，如果传了，代表是审批，
  // 如果是审批，需要传入orgId， 当没有数据时展示暂无待审批的数据
  // 如果有数据，就只展示有数据的行
  orgId?: string
}

interface BasisList {
  negativeScore: number | null // 负面清单的得分
  evalContent: string // 考评内容
  evalBasis?: string // 考评依据对应的key
  evalBasisVal?: string // 考评依据对应的value
  evalInput?: string // 自己填写的考评依据
  isEdit: boolean
}

interface DataList {
  ppercId?: string
  pprId?: string
  positiveScore: number | null // 任务清单的得分
  orgId?: string
  orgName: string
  hj: number | null // 合计
  basisList: BasisList[] // 评价依据列表
}

interface IState {
  dateId: string
  menuType: string
  //表格list
  countyList: DataList[]
  // 所有的县公司
  allCountyList: { orgName: string; id: string }[]
  //考评依据list
  kpList: { dictKey: string; content: string }[]
  isModalVisible: boolean
  reasonItem: {
    list: { dictKey: string; value: string; checked?: boolean }[]
    select: string
  }
  index?: number
  innerIndex?: number
}

// 接口返回数据evalInput有值,文本框就展示该值,如果没有值,就展示evalBasisVal
// 自己填写的时候,直接赋值给evalInput, evalBasis为空
// 选择的时候,evalBasis有值,evalBasisVal有值,evalInput为空
// 原来是只有选择的,所以保存的时候只需要传evalBasis就可以了,保存key, 返回的时候会返回evalBasisVal,直接展示就好
// 现在变成了在选择基础上支持自己填写, 后端添加了个字段,用来存储自己填写的,所以就需要判断一下,如果evalInput有值,就传evalInput,如果没有值,就传evalBasis
export default class extends React.Component<IProps, IState> {
  static contextType = LoadingContext
  context: React.ContextType<typeof LoadingContext>
  intervalId = null // 在这里定义 intervalId
  //保存前数据
  preKeyList: any = []

  state = {
    dateId: '',
    menuType: '',
    //表格list
    countyList: [],
    // 所有的县公司
    allCountyList: [],
    //考评依据list
    kpList: [],
    isModalVisible: false,
    reasonItem: {
      list: [],
      select: ''
    },
    index: null,
    innerIndex: null
  }

  componentDidMount(): void {
    if (this.props.leafKey.includes('-')) {
      this.initializeData()
    }
    // 每隔2分钟（122000毫秒）执行
    this.intervalId = setInterval(this.saveAll, 124000)
  }

  componentWillUnmount() {
    // 清除定时器
    clearInterval(this.intervalId)
  }

  //定时保存
  saveAll = () => {
    let list = [...this.state.countyList]
    for (let i = 0; i < list.length; i++) {
      let item = list[i]
      let smallList = item.basisList
      let smallEdit = false
      for (let j = 0; j < smallList.length; j++) {
        let smallItem = smallList[j]
        if (smallItem.isEdit) {
          smallEdit = true
          break
        }
      }
      if (smallEdit && JSON.stringify(this.preKeyList[i]) !== JSON.stringify(item)) {
        this.handleSave(i, 0, 'regular')
      }
    }
    //记录当前数据
    this.preKeyList = JSON.parse(JSON.stringify(list))
  }

  componentDidUpdate(prevProps: Readonly<IProps>): void {
    if (prevProps.leafKey != this.props.leafKey) {
      if (this.props.leafKey.includes('-')) {
        let datas = this.props.leafKey.split('-')
        // 如果是审批
        if (prevProps.orgId) {
          this.setState({ dateId: datas[0], menuType: datas[1] }, () => {
            // 获取列表数据
            this.getApproveData()
          })
        } else {
          this.setState({ dateId: datas[0], menuType: datas[1] }, () => {
            // 获取列表数据
            this.getList(this.state.allCountyList)
          })
        }
      }
    }
  }

  // 初始化页面
  initializeData = () => {
    //将其拆分
    let time = this.props.leafKey.split('-')
    this.setState({ dateId: time[0], menuType: time[1] })
    if (this.props.orgId) {
      this.getApproveData()
    } else {
      getOrgList('03', '1').then((res) => {
        //获取列表
        this.getList(res.data.list)
      })
    }

    // 获取考评依据
    this.getKpList()
  }

  getApproveData() {
    let data = {
      date: this.props.leafKey.split('-')[0],
      orgId: this.props.orgId,
      status: '1',
      source: 'audit'
    }
    this.context.showLoading()
    getProfEvalReportCountyList(data)
      .then((res) => {
        let data = res.data?.list
        if (data && data.length > 0) {
          data.map((item, index) => {
            item.hj = this.getHj(index, data)
            // 如果basisList的长度为0，需要初始化传入一项
            if (item.basisList.length === 0) {
              item.basisList.push({
                evalBasis: '',
                evalBasisVal: '',
                evalInput: '',
                negativeScore: null,
                evalContent: '',
                isEdit: false
              })
            }
          })
        }
        this.setState({ countyList: data })
        this.context.hideLoading()
      })
      .catch(() => {
        this.context.hideLoading()
      })
  }

  //获取考评依据
  getKpList = () => {
    getDictListByName({
      categoryType: '专业评价县公司',
      type: '考评项目',
      orgId: this.props.orgId
    }).then((res) => {
      this.setState({ kpList: res.data.list })
    })
  }

  //提交前校验
  submitYz = () => {
    //默认填写完成
    let flag = true
    //判断是否有正在填写的内容
    let list = this.state.countyList
    //合计中最高得分与最低得分的分差需要大于等于0.5
    //获取列表中的最大合计，去除空值我，。值为null的情况
    let back = list.map((item) => item.hj).filter((item) => item !== null)

    if (back.length) {
      let maxHj = Math.max(...back)
      let minHj = Math.min(...back)

      // 使用 Decimal 进行精确计算
      let scoreSpace = new Decimal(maxHj).minus(new Decimal(minHj)).toNumber()

      //判断分差是否大于等于0.5
      if (scoreSpace && scoreSpace < 0.5) {
        flag = false
        Modal.warning({
          title: '提示',
          content: `【专业评价填报--县公司评价】中，合计中最高分与最低分的分差需要≥0.5分`,
          zIndex: 1100
        })
      }
    }
    return flag
  }

  getList = (allCountyList) => {
    let list: DataList[] = []
    allCountyList.map((item) => {
      list.push({
        //公司名称
        orgName: item.orgName,
        orgId: item.id,
        //任务清单得分
        positiveScore: null,
        //合计
        hj: null,
        basisList: [
          {
            evalBasis: '',
            evalBasisVal: '',
            evalInput: '',
            negativeScore: null,
            evalContent: '',
            isEdit: false
          }
        ]
      })
    })
    this.context.showLoading()

    getProfEvalReportCountyList({ date: this.props.leafKey.split('-')[0] })
      .then((res) => {
        let data = res.data?.list
        if (data && data.length > 0) {
          data.map((item) => {
            let index = list.findIndex((obj) => obj.orgName === item.orgName)
            if (index > -1) {
              list[index] = {
                ...list[index],
                ...item
              }
              list[index].hj = this.getHj(index, list)
              // 如果basisList的长度为0，需要初始化传入一项
              if (list[index].basisList.length === 0) {
                list[index].basisList.push({
                  evalBasis: '',
                  evalBasisVal: '',
                  negativeScore: null,
                  evalContent: '',
                  isEdit: false
                })
              }
            }
          })
        }
        this.setState({ countyList: list, allCountyList })
        this.context.hideLoading()
      })
      .catch(() => {
        this.context.hideLoading()
      })
  }

  handleContent = (value: number | string, index: number, key: string, innerIndex?: number) => {
    let list = [...this.state.countyList]
    if (innerIndex || innerIndex === 0) {
      list[index].basisList[innerIndex][key] = value
    } else {
      list[index][key] = value
    }
    // 只要改了evalInput，就要把evalBasis清空
    if (key === 'evalInput') {
      list[index].basisList[innerIndex].evalBasis = ''
      list[index].basisList[innerIndex].evalBasisVal = value
    }
    //计算合计
    list[index].hj = this.getHj(index, list)
    this.setState({ countyList: list })
  }

  //计算合计
  getHj = (index, list) => {
    let count = 0
    list[index].basisList.map((item) => {
      count += item.negativeScore ? item.negativeScore : 0
    })
    count += list[index].positiveScore ? list[index].positiveScore : 0
    return getNumber(count, 2)
  }

  //保存
  handleSave = (index, innerIndex, type?: string) => {
    //校验
    let item = this.state.countyList[index]
    if (!item.positiveScore) {
      if (!type) {
        Modal.warning({
          title: '提示',
          content: '请填写任务清单得分！',
          zIndex: 1100
        })
      }
      return
    }
    if (type) {
      let smallList = item.basisList
      for (let i = 0; i < smallList?.length; i++) {
        let itemSmall = smallList[i]
        // 如果填写了负面清单得分，就要校验是否填写了考评内容和考评依据，否则不校验
        if (itemSmall.negativeScore) {
          if (!(itemSmall.evalContent !== '')) {
            return
          }
          if (!(itemSmall.evalBasis !== '') && !(itemSmall.evalInput !== '')) {
            return
          }
        }
        // 如果填写了考评内容或者考评依据，就要校验是否填写了负面清单得分，否则不校验
        if (itemSmall.evalContent !== '' || itemSmall.evalBasis !== '' || itemSmall.evalInput !== '') {
          if (!itemSmall.negativeScore) {
            return
          }
        }
      }
    } else {
      // 如果填写了负面清单得分，就要校验是否填写了考评内容和考评依据，否则不校验
      if (item.basisList[innerIndex].negativeScore) {
        if (!(item.basisList[innerIndex].evalContent !== '')) {
          if (!type) {
            Modal.warning({
              title: '提示',
              content: '请注意，【负面清单得分】、【考评内容】、【考评依据】三者需同时填写或同时为空！',
              zIndex: 1100
            })
          }

          return
        }
        if (!(item.basisList[innerIndex].evalBasis !== '') && !(item.basisList[innerIndex].evalInput !== '')) {
          if (!type) {
            Modal.warning({
              title: '提示',
              content: '请注意，【负面清单得分】、【考评内容】、【考评依据】三者需同时填写或同时为空！',
              zIndex: 1100
            })
          }

          return
        }
      }
      // 如果填写了考评内容或者考评依据，就要校验是否填写了负面清单得分，否则不校验
      if (
        item.basisList[innerIndex].evalContent !== '' ||
        item.basisList[innerIndex].evalBasis !== '' ||
        item.basisList[innerIndex].evalInput !== ''
      ) {
        if (!item.basisList[innerIndex].negativeScore) {
          if (!type) {
            Modal.warning({
              title: '提示',
              content: '请注意，【负面清单得分】、【考评内容】、【考评依据】三者需同时填写或同时为空！',
              zIndex: 1100
            })
          }

          return
        }
      }
    }
    //调用保存接口
    let data = this.getSaveData(index)
    if (!type) {
      this.context.showLoading()
    }

    saveCounty(data)
      .then((res: any) => {
        if (res.code === '0') {
          item.ppercId = res.data.ppercId
          item.pprId = res.data.pprId
          if (!type) {
            MessageSelf('保存成功', 'success')
          }
        }
      })
      .finally(() => {
        if (!type) {
          this.context.hideLoading()
        }
      })
    if (!type) {
      item.basisList[innerIndex].isEdit = false
    }
  }

  //编辑
  handleEdit = (index, innerIndex) => {
    let list = [...this.state.countyList]
    list[index].basisList[innerIndex].isEdit = true
    this.setState({ countyList: list })
  }

  handleOk = () => {
    let list = [...this.state.countyList]
    list[this.state.index].basisList[this.state.innerIndex].evalBasis = this.state.reasonItem.select
    list[this.state.index].basisList[this.state.innerIndex].evalBasisVal = this.state.reasonItem.list.find(
      (item) => item.dictKey === this.state.reasonItem.select
    ).content
    list[this.state.index].basisList[this.state.innerIndex].evalInput = ''
    this.setState({ countyList: list, isModalVisible: false })
  }

  openModalTable = (select, index, innerIndex) => {
    let list = this.state.kpList
    this.setState({
      isModalVisible: true,
      reasonItem: {
        list: list,
        select: select
      },
      index: index,
      innerIndex: innerIndex
    })
  }

  // 删除小行
  handleDeleteSmall = (index, innerIndex, type) => {
    let list = [...this.state.countyList]
    Modal.confirm({
      title: '提示',
      content: `确定${type}吗？`,
      onOk: () => {
        this.context.showLoading()
        if (list[index].basisList.length === 1) {
          if (!list[index].ppercId) {
            list[index].positiveScore = null
            list[index].hj = null
            list[index].ppercId = ''
            list[index].pprId = ''
            list[index].basisList = [
              {
                evalBasis: '',
                evalBasisVal: '',
                evalInput: '',
                negativeScore: null,
                evalContent: '',
                isEdit: false
              }
            ]
            MessageSelf('清空成功', 'success')
            this.setState({ countyList: list })
            this.context.hideLoading()
            return
          }
          deleteCounty({
            ids: list[index].ppercId
          })
            .then((res: any) => {
              if (res.code === '0') {
                MessageSelf('清空成功', 'success')
                list[index].positiveScore = null
                list[index].hj = null
                list[index].ppercId = ''
                list[index].pprId = ''
                list[index].basisList = [
                  {
                    evalBasis: '',
                    evalBasisVal: '',
                    evalInput: '',
                    negativeScore: null,
                    evalContent: '',
                    isEdit: false
                  }
                ]
                this.setState({ countyList: list })
                this.context.hideLoading()
              }
            })
            .catch(() => {
              this.context.hideLoading()
            })
          return
        }
        list[index].basisList.splice(innerIndex, 1)
        let data = this.getSaveData(index, list)
        saveCounty(data).then((res: any) => {
          this.context.hideLoading()
          if (res.code === '0') {
            MessageSelf('删除成功', 'success')
            list[index].hj = this.getHj(index, list)
            this.setState({ countyList: list })
          }
        })
      }
    })
  }

  // 保存接口，传参index，将countryList[index]传给后台
  getSaveData = (index, list?) => {
    if (!list) {
      list = this.state.countyList
    }
    let item = list[index]
    let params = {
      date: this.state.dateId,
      orgId: item.orgId,
      ppercId: item.ppercId,
      pprId: item.pprId,
      positiveScore: item.positiveScore,
      basisList: []
    }
    item.basisList.map((obj) => {
      params.basisList.push({
        negativeScore: obj.negativeScore,
        evalContent: obj.evalContent,
        evalBasis: obj.evalBasis,
        evalInput: obj.evalInput || ''
      })
    })
    return params
  }

  handleAddSmall = (index) => {
    // 如果当前这一大行内的小行有负面清单没有数据的，就不让新增
    let flag = false
    this.state.countyList[index].basisList.map((item) => {
      if (!item.negativeScore) {
        flag = true
      }
    })
    if (flag) {
      Modal.warning({
        title: '提示',
        content: '请先填写完当前行数据！',
        zIndex: 1100
      })
      return
    }
    let list = [...this.state.countyList]
    list[index].basisList.push({
      evalBasis: '',
      evalBasisVal: '',
      evalInput: '',
      negativeScore: null,
      evalContent: '',
      isEdit: true
    })
    this.setState({ countyList: list })
  }

  haveData(data: DataList): boolean {
    // 判断是否有数据
    let flag = false
    if (data.positiveScore) {
      flag = true
    }
    data.basisList.map((item) => {
      if (item.negativeScore || item.evalContent || item.evalBasis) {
        flag = true
      }
    })
    return flag
  }

  render() {
    return (
      <>
        <div style={{ height: '100%' }}>
          <div style={{ height: '100%' }}>
            <div className="div-table">
              <table className="common-table">
                <thead>
                  <tr>
                    <th rowSpan={2}>序号</th>
                    <th rowSpan={2}>公司名称</th>
                    <th rowSpan={2}>合计</th>
                    <th>任务清单</th>
                    <th colSpan={2}>负面清单</th>
                    <th rowSpan={2}>负面得分合计</th>
                    <th rowSpan={2}>考评依据</th>
                    {this.props.permission != 'view' && <th rowSpan={2}>操作</th>}
                  </tr>
                  <tr>
                    <th>得分</th>
                    <th>得分</th>
                    <th>考评内容</th>
                  </tr>
                </thead>
                <tbody>
                  {this.state.countyList.map((item: DataList, index) => {
                    return item.basisList.map((innerItem: BasisList, innerIndex) => {
                      return (
                        <tr key={`${index}-${innerIndex}`}>
                          {innerIndex == 0 && (
                            <td rowSpan={item.basisList.length} style={{ width: '45px' }}>
                              {index + 1}
                            </td>
                          )}
                          {innerIndex == 0 && (
                            <td rowSpan={item.basisList.length} style={{ width: '120px' }}>
                              {item.orgName}
                            </td>
                          )}
                          {innerIndex == 0 && (
                            <td rowSpan={item.basisList.length} style={{ width: '55px' }}>
                              {item.hj}
                            </td>
                          )}
                          {innerIndex == 0 && (
                            <td rowSpan={item.basisList.length} style={{ width: '90px' }}>
                              {innerItem.isEdit && this.props.isVisible ? (
                                <InputNumber
                                  controls={false}
                                  precision={2}
                                  // min={0}
                                  // max={5}
                                  value={item.positiveScore}
                                  onChange={(value) => {
                                    if (!validateNumber(value, '请输入>0，<=5的数值！', 5, 0)) return
                                    this.handleContent(value, index, 'positiveScore')
                                  }}
                                />
                              ) : (
                                item.positiveScore
                              )}
                            </td>
                          )}
                          <td style={{ width: '90px' }}>
                            {innerItem.isEdit && this.props.isVisible ? (
                              <InputNumber
                                controls={false}
                                precision={2}
                                // max={0}
                                value={innerItem.negativeScore}
                                onChange={(value) => {
                                  if (!validateNumber(value, '请输入<0的数值！', 0, '')) return
                                  this.handleContent(value, index, 'negativeScore', innerIndex)
                                }}
                              />
                            ) : (
                              innerItem.negativeScore
                            )}
                          </td>
                          <td>
                            {innerItem.isEdit && this.props.isVisible ? (
                              <TextArea
                                autoSize
                                maxLength={1000}
                                // showCount
                                value={innerItem.evalContent}
                                placeholder="请填写考评内容"
                                onChange={(obj) =>
                                  this.handleContent(obj.target.value, index, 'evalContent', innerIndex)
                                }
                              />
                            ) : (
                              innerItem.evalContent
                            )}
                          </td>
                          {innerIndex == 0 && (
                            <td rowSpan={item.basisList.length} style={{ width: '75px' }}>
                              {/* 负面清单得分总和 */}
                              {getNumber(
                                item.basisList.reduce((prev, cur) => {
                                  return prev + (cur.negativeScore ? cur.negativeScore : 0)
                                }, 0),
                                2
                              ) || ''}
                            </td>
                          )}
                          <td style={{ width: '400px' }}>
                            {innerItem.isEdit && this.props.isVisible ? (
                              // 如果evalBasisVal存在代表之前选择的考评依据
                              // 如果evalInput存在代表之前自己填写的考评依据
                              innerItem.evalBasisVal ? (
                                <div style={{ display: 'flex' }}>
                                  <TextArea
                                    autoSize
                                    maxLength={1000}
                                    value={innerItem.evalBasisVal}
                                    placeholder="请填写考评依据"
                                    onChange={(e) => this.handleContent(e.target.value, index, 'evalInput', innerIndex)}
                                  />
                                  <div
                                    style={{
                                      display: 'flex',
                                      alignItems: 'center',
                                      cursor: 'pointer',
                                      justifyContent: 'center',
                                      color: '#1890ff',
                                      textAlign: 'start',
                                      marginLeft: '10px',
                                      width: '80px'
                                    }}
                                    onClick={() => this.openModalTable(innerItem.evalBasis, index, innerIndex)}
                                  >
                                    点击选择
                                  </div>
                                </div>
                              ) : (
                                <div style={{ display: 'flex' }}>
                                  <TextArea
                                    autoSize
                                    maxLength={1000}
                                    value={innerItem.evalInput}
                                    placeholder="请填写考评依据"
                                    onChange={(e) => this.handleContent(e.target.value, index, 'evalInput', innerIndex)}
                                  />
                                  <div
                                    style={{
                                      display: 'flex',
                                      alignItems: 'center',
                                      cursor: 'pointer',
                                      justifyContent: 'center',
                                      color: '#1890ff',
                                      textAlign: 'start',
                                      marginLeft: '10px',
                                      width: '80px'
                                    }}
                                    onClick={() => this.openModalTable(innerItem.evalBasis, index, innerIndex)}
                                  >
                                    点击选择
                                  </div>
                                </div>
                              )
                            ) : (
                              <div
                                style={{
                                  textAlign: 'start'
                                }}
                              >
                                {innerItem.evalBasisVal || innerItem.evalInput}
                              </div>
                            )}
                          </td>
                          {this.props.permission != 'view' && (
                            <td style={{ width: '200px' }}>
                              <div className="table-operate" style={{ justifyContent: 'start' }}>
                                {innerItem.isEdit ? (
                                  <div
                                    className={`${!this.props.isVisible && 'disabled-table-div'}`}
                                    onClick={() => this.handleSave(index, innerIndex)}
                                  >
                                    <Iconfont type="icon-icon_duihao-mian" style={{ fontSize: '16px' }} />
                                    <span className="operate-text">保存</span>
                                  </div>
                                ) : (
                                  <div
                                    className={`${!this.props.isVisible && 'disabled-table-div'}`}
                                    onClick={() => this.handleEdit(index, innerIndex)}
                                  >
                                    <Iconfont type="icon-bianji3" style={{ fontSize: '16px' }} />
                                    <span className="operate-text">编辑</span>
                                  </div>
                                )}
                                {innerIndex === 0 && item.basisList.length == 1 && this.haveData(item) ? (
                                  <div
                                    className={`${!this.props.isVisible && 'disabled-table-div'}`}
                                    style={{ paddingLeft: '10px' }}
                                    onClick={() => this.handleDeleteSmall(index, innerIndex, '清空')}
                                  >
                                    <Iconfont type="icon-shanchu" style={{ fontSize: '16px' }} />
                                    <span className="operate-text">清空</span>
                                  </div>
                                ) : (
                                  innerIndex !== 0 && (
                                    <div
                                      className={`${!this.props.isVisible && 'disabled-table-div'}`}
                                      style={{ paddingLeft: '10px' }}
                                      onClick={() => this.handleDeleteSmall(index, innerIndex, '删除')}
                                    >
                                      <Iconfont type="icon-shanchu" style={{ fontSize: '16px' }} />
                                      <span className="operate-text">删除</span>
                                    </div>
                                  )
                                )}

                                {innerIndex == 0 && (
                                  <div
                                    className={`${!this.props.isVisible && 'disabled-table-div'}`}
                                    style={{ paddingLeft: '10px' }}
                                    onClick={() => this.handleAddSmall(index)}
                                  >
                                    <Iconfont type="icon-xinzeng" style={{ fontSize: '16px' }} />
                                    <span className="operate-text">新增一行</span>
                                  </div>
                                )}
                              </div>
                            </td>
                          )}
                        </tr>
                      )
                    })
                  })}
                </tbody>
              </table>
            </div>
          </div>
        </div>
        <ModalInfo
          title="选择考评依据"
          visible={this.state.isModalVisible}
          width="1150px"
          maxHeight="800px"
          onClose={() => this.setState({ isModalVisible: false })}
        >
          <div>
            <div style={{ display: 'flex', justifyContent: 'start', marginBottom: '10px', position: 'sticky', top: 0 }}>
              <Input
                style={{ width: '200px' }}
                placeholder="请输入考评依据关键字"
                onChange={debounce((e) => {
                  if (!e.target.value) {
                    this.setState({
                      reasonItem: { list: this.state.kpList, select: this.state.reasonItem.select }
                    })
                    return
                  }
                  let list = this.state.kpList
                  list = list.filter((item) => item.content.indexOf(e.target.value) > -1)
                  this.setState({ reasonItem: { list, select: this.state.reasonItem.select } })
                }, 200)}
              />
            </div>
            <div style={{ maxHeight: '600px', overflow: 'auto' }}>
              <table className="common-table">
                <thead>
                  <tr>
                    <th>选择</th>
                    <th>考评依据</th>
                  </tr>
                </thead>
                <tbody>
                  {this.state.reasonItem.list.map((item, index) => {
                    return (
                      <tr
                        key={index}
                        onClick={() => {
                          this.setState({ reasonItem: { list: this.state.reasonItem.list, select: item.dictKey } })
                        }}
                        onDoubleClick={() => {
                          this.setState({ reasonItem: { list: this.state.reasonItem.list, select: item.dictKey } })
                          this.handleOk()
                        }}
                        style={{
                          cursor: 'pointer',
                          backgroundColor: this.state.reasonItem.select === item.dictKey && '#f0f0f0'
                        }}
                      >
                        <td style={{ width: '45px' }}>
                          <Checkbox
                            checked={this.state.reasonItem.select === item.dictKey}
                            onChange={(e) => {
                              if (e.target.checked) {
                                let list = [...this.state.reasonItem.list]
                                list.map((obj) => {
                                  obj.dictKey === item.dictKey ? (obj.checked = true) : (obj.checked = false)
                                })
                                this.setState({
                                  reasonItem: {
                                    list: list,
                                    select: item.dictKey
                                  }
                                })
                              }
                            }}
                          ></Checkbox>
                        </td>
                        <td>
                          <div
                            style={{
                              cursor: 'pointer',
                              textAlign: 'start'
                            }}
                          >
                            {item.content}
                          </div>
                        </td>
                      </tr>
                    )
                  })}
                </tbody>
              </table>
            </div>
          </div>
          <div style={{ display: 'flex', justifyContent: 'end', marginTop: '20px' }}>
            <Button
              className="tr-btn"
              style={{ marginRight: '12px' }}
              onClick={() => this.setState({ isModalVisible: false })}
            >
              取消
            </Button>
            <Button
              type="primary"
              className="tr-btn"
              onClick={() => {
                this.handleOk()
              }}
            >
              确定
            </Button>
          </div>
        </ModalInfo>
      </>
    )
  }
}
