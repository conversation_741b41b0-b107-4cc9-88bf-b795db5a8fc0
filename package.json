{"name": "jnlgjf", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"dev": "npm-run-all -p prettier start", "start": "npm run version && webpack-dev-server --mode=development", "build": "npm run version && webpack --mode=production", "version": "node tr_version/version.js", "prettier": "onchange \"src/**/*\" -- prettier --write --ignore-unknown {{changed}}"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@ant-design/pro-components": "^2.4.14", "antd": "^4.19.5", "axios": "^1.4.0", "copy-webpack-plugin": "^11.0.0", "crypto-js": "^4.1.1", "decimal.js": "^10.4.3", "echarts": "^5.4.2", "jsencrypt": "^3.3.2", "less": "^4.1.3", "moment": "^2.29.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.11.1", "redux": "^4.2.1", "tr-cheers": "^1.2.14"}, "devDependencies": {"@babel/core": "^7.21.8", "@babel/plugin-proposal-decorators": "^7.21.0", "@babel/plugin-transform-runtime": "^7.21.4", "@babel/preset-env": "^7.21.5", "@babel/preset-react": "^7.18.6", "@babel/preset-typescript": "^7.21.5", "@types/react": "^18.2.5", "@types/react-dom": "^18.2.3", "babel-loader": "^9.1.2", "css-loader": "^6.7.3", "dotenv": "^16.4.5", "html-webpack-plugin": "^5.5.1", "less-loader": "^11.1.3", "npm-run-all": "^4.1.5", "onchange": "^7.1.0", "prettier": "^2.8.8", "sass": "^1.62.1", "sass-loader": "^13.2.2", "style-loader": "^3.3.2", "typescript": "^5.0.4", "webpack": "^5.82.0", "webpack-cli": "^5.0.2", "webpack-dev-server": "^4.13.3"}}