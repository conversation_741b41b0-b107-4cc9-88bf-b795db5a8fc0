.date-menu {
  height: 100%;
  background-image: url('@/assets/images/shujushudibubg.png');
  background-repeat: no-repeat;
  padding: 10px 0px 15px 0px;
  color: var(--main-color11);
  .menu-header {
    display: flex;
    align-items: center;
    position: relative;
    padding-bottom: 8px;
    margin-left: 15px;
    &::after {
      content: '';
      display: block;
      width: 90%;
      height: 1px;
      background: #bedaf0;
      position: absolute;
      bottom: 0px;
      left: 0px;
    }
    img {
      margin-right: 8px;
    }
    span {
      font-size: 16px;

      letter-spacing: 1px;
    }
  }
  .menu-sign {
    display: flex;
    align-items: center;
    padding-top: 8px;
    margin-left: 15px;
    img {
      margin-right: 6px;
    }
  }
  .ant-menu {
    background: transparent !important;
    border: none;
    color: var(--main-color11);
  }
  .ant-menu-inline.ant-menu-root .ant-menu-item > .ant-menu-title-content,
  .ant-menu-inline.ant-menu-root .ant-menu-submenu-title > .ant-menu-title-content {
    font-size: 14px;
  }

  .ant-menu .ant-menu-inline .ant-menu-item {
    padding-left: 0 !important;
    padding-right: 0 !important;
    text-align: center;
  }
  .ant-menu-submenu-title {
    height: 40px !important;
    line-height: 40px !important;
    padding-left: 39px !important;
    padding-right: 0px;
  }
  .ant-menu-sub.ant-menu-inline > .ant-menu-item {
    height: 40px !important;
    line-height: 40px !important;
  }
  .ant-menu-dark .ant-menu-submenu-selected > div {
    background: transparent !important;
    color: var(--main-color11) !important;
    font-weight: bold;
  }
  .ant-menu-submenu-arrow::before {
    position: revert;
  }
  .ant-menu-submenu-arrow::after {
    position: revert;
  }
  .ant-menu-submenu-title .ant-menu-item-icon + span,
  .ant-menu-submenu-title .anticon + span {
    margin-left: 5px;
  }
  .ant-menu .ant-menu-item {
    color: var(--main-color11);
  }
  .child-menu {
    .ant-menu-submenu-title {
      padding-left: 0px !important;
      text-align: center;
    }
    .ant-menu-submenu-arrow::before {
      border: 1px solid #001191;
      position: absolute;
      width: 7px;
      height: 2px;
    }
    .ant-menu-submenu-arrow::after {
      border: 1px solid #001191;
      position: absolute;
      width: 7px;
      height: 2px;
    }
  }
}
