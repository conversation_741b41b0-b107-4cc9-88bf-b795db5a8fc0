import { getServiceRatingDetails, getYearDashboardKeyActions2024Details } from '@/api'
import React from 'react'
import { LoadingContext } from '@/components/load/loadingProvider'

interface IProps {
  //选中的是哪个节点
  leafKey: string
}

// 定义表格数据类型
interface TableDataItem {
  seqNum?: number
  taskName: string
  type: string
  leadDepartment: number
  licheng: number
  zhangqiu: number
  changqing: number
  pingyin: number
  jiyang: number
  shanghe: number
}

export default class extends React.Component<IProps> {
  static contextType = LoadingContext
  context: React.ContextType<typeof LoadingContext>

  state = {
    //table数据
    tableData: [] as TableDataItem[],
    dateId: '',
    keyList: []
  }

  componentDidMount(): void {
    this.setState({ dateId: this.props.leafKey }, () => {
      // 获取列表数据
      this.getKeyList()
    })
  }

  componentDidUpdate(prevProps: Readonly<IProps>, prevState: Readonly<{}>, snapshot?: any): void {
    if (this.props.leafKey != prevProps.leafKey) {
      this.setState({ dateId: this.props.leafKey }, () => {
        // 获取列表数据
        this.getKeyList()
      })
    }
  }

  getKeyList = () => {
    if (this.state.dateId) {
      this.context.showLoading()
      // this.state.dateId.split('-')[0] 是截取年份
      getServiceRatingDetails({ dateYear: this.state.dateId.split('-')[0] })
        .then((res) => {
          // console.log('获取县公司重点工作考核得分情况表', res)
          this.setState({ keyList: res.data.list })
          this.context.hideLoading()
        })
        .catch(() => {
          this.context.hideLoading()
        })
      getYearDashboardKeyActions2024Details({ dateYear: this.state.dateId.split('-')[0] })
        .then((res) => {
          console.log('获取县公司2024年“十大行动”重点', res)
          this.setState({ tableData: res.data.list })
          this.context.hideLoading()
        })
        .catch(() => {
          this.context.hideLoading()
        })
    }
  }

  render(): React.ReactNode {
    const { tableData } = this.state

    return (
      <div style={{ height: 'calc(100% - 10px)' }}>
        <div className="div-table" style={{ height: 'calc(100vh - 250px)', overflow: 'auto' }}>
          <h3 style={{ fontWeight: 700, fontSize: '14px', margin: '10px 0' }}>县公司重点工作考核得分情况表</h3>
          <table className="common-table" style={{ tableLayout: 'auto', minWidth: '100%', marginBottom: '25px' }}>
            <thead>
              <tr>
                <th>单位</th>
                <th>重点工作考核总分</th>
                <th>年度（70%）</th>
                <th>2023年12月-2024年11月月度重点工作考核得分（30%）</th>
              </tr>
            </thead>
            <tbody>
              {this.state.keyList.length > 0 ? (
                this.state.keyList.map((item: any, index: number) => (
                  // 长文本列使用 whiteSpace: 'pre-wrap' 和 wordBreak: 'break-word' 确保文本换行显示
                  // 部门列使用 whiteSpace: 'nowrap' 保持单行显示
                  <tr key={index}>
                    <td>{item.unit}</td>
                    <td>{Number(item.keyTasksTotalScore).toFixed(2)}</td>
                    <td>{Number(item.annualScore).toFixed(2)}</td>
                    <td>{Number(item.monthlyScore).toFixed(2)}</td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={4} style={{ textAlign: 'center', padding: '20px' }}>
                    暂无数据
                  </td>
                </tr>
              )}
            </tbody>
          </table>
          <h3 style={{ fontWeight: 700, fontSize: '14px', margin: '20px 0 10px 0' }}>
            县公司2024年“十大行动”重点工作评价得分情况表
          </h3>
          <table className="common-table" style={{ tableLayout: 'auto', minWidth: '100%', marginBottom: '55px' }}>
            <thead>
              <tr>
                <th>序号</th>
                <th>任务名称</th>
                <th>类型</th>
                <th>牵头部门</th>
                <th>历城</th>
                <th>章丘</th>
                <th>长清</th>
                <th>平阴</th>
                <th>济阳</th>
                <th>商河</th>
              </tr>
            </thead>
            <tbody>
              {tableData.length > 0 ? (
                <>
                  {/* 动态渲染数据行 */}
                  {tableData.map((item, index) => (
                    <tr key={index}>
                      <td>{index + 1}</td>
                      <td>{item.taskName}</td>
                      <td>{item.type}</td>
                      <td>{item.leadDepartment}</td>
                      <td>{item.licheng}</td>
                      <td>{item.zhangqiu}</td>
                      <td>{item.changqing}</td>
                      <td>{item.pingyin}</td>
                      <td>{item.jiyang}</td>
                      <td>{item.shanghe}</td>
                    </tr>
                  ))}
                </>
              ) : (
                <tr>
                  <td colSpan={10} style={{ textAlign: 'center', padding: '20px' }}>
                    暂无数据
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    )
  }
}
