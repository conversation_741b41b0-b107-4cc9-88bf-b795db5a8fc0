import React from 'react'
import { Menu } from 'antd'
import './DateMenu.scss'
import jf from '@/assets/images/shujuzhouqi.png'
import qb from '@/assets/images/quanbutubiao.png'
import { CaretDownFilled, CaretRightOutlined } from '@ant-design/icons'
// @ts-ignore

interface IProps {
  /** 菜单的点击事件 */
  onClick?: (menu: any) => void
  /** 哪个月份 */
  dateId?: string
  /** 选中的key */
  leafKey?: string
  menuList: Array<any>
}
export default class DateMenu extends React.Component<IProps> {
  state = {
    dateId: '',
    leafKey: '',
    openKeys: []
  }
  componentDidMount() {
    //获取展开的节点
    this.getExpendKeys(this.props.menuList, this.props.leafKey)
    this.setState({ dateId: this.props.dateId, leafKey: this.props.leafKey })
  }

  componentDidUpdate(prevProps: Readonly<IProps>, prevState: Readonly<{}>, snapshot?: any): void {
    if (prevProps.leafKey !== this.props.leafKey) {
      //获取展开的节点
      this.getExpendKeys(this.props.menuList, this.props.leafKey)
      this.setState({ dateId: this.props.dateId, leafKey: this.props.leafKey })
    }
  }

  getExpendKeys = (list: Array<any>, childKey) => {
    let parentKeys = this.findParentKeys(list, childKey)
    this.setState({ openKeys: parentKeys })
  }

  findParentKeys = (tree, targetKey, parentKeys = []) => {
    for (let node of tree) {
      if (node.dateId === targetKey) {
        return parentKeys
      }
      if (node.subMenuVoList && node.subMenuVoList.length > 0) {
        const newParentKeys = [...parentKeys, node.dateId]
        const result = this.findParentKeys(node.subMenuVoList, targetKey, newParentKeys)
        if (result) {
          return result
        }
      }
    }
    return null
  }

  handleClick(e: any) {
    this.setState(
      {
        dateId: e.key,
        leafKey: e.key
      },
      () => {
        this.props.onClick && this.props.onClick(e)
      }
    )
  }
  GenSubMenu(menus: Array<any>) {
    return menus.map((menu: any) => {
      let hasChild = Array.isArray(menu.subMenuVoList) && menu.subMenuVoList.length > 0
      if (hasChild) {
        if (menu.type == 'year') {
          return (
            <Menu.SubMenu
              icon={
                this.state.openKeys.includes(menu.dateId) ? (
                  <CaretDownFilled style={{ fontSize: '14px' }} />
                ) : (
                  <CaretRightOutlined style={{ fontSize: '14px' }} />
                )
              }
              key={menu.dateId}
              title={menu.title}
              className={
                this.state.dateId && this.state.dateId.slice(0, 4) == menu.dateId ? 'ant-menu-submenu-selected' : ''
              }
            >
              {this.GenSubMenu(menu.subMenuVoList)}
            </Menu.SubMenu>
          )
        } else {
          return (
            <Menu.SubMenu
              key={menu.dateId}
              title={menu.title}
              className={
                this.state.dateId && this.state.dateId.slice(0, 4) == menu.dateId
                  ? 'child-menu ant-menu-submenu-selected'
                  : 'child-menu'
              }
            >
              {this.GenSubMenu(menu.subMenuVoList)}
            </Menu.SubMenu>
          )
        }
      } else {
        return (
          <Menu.Item key={menu.dateId} title={menu.title}>
            <span>{menu.title}</span>
          </Menu.Item>
        )
      }
    })
  }
  onOpenChange(openKeys) {
    this.setState({ openKeys: openKeys || [] })
  }
  render(): React.ReactNode {
    return (
      <div className="date-menu">
        <div className="menu-header">
          <img src={jf} alt="" />
          <span>数据周期</span>
        </div>
        <div className="menu-sign">
          <img src={qb} alt="" />
          <span>全部</span>
        </div>

        <div style={{ overflow: 'auto', height: 'calc(100% - 60px)', scrollbarWidth: 'none' }}>
          <Menu
            mode="inline"
            theme="dark"
            onClick={this.handleClick.bind(this)}
            selectedKeys={[this.state.leafKey]}
            openKeys={this.state.openKeys}
            onOpenChange={this.onOpenChange.bind(this)}
          >
            {this.GenSubMenu(this.props.menuList)}
          </Menu>
        </div>
      </div>
    )
  }
}
