import { getSpecialAssessmentDetails } from '@/api'
import React from 'react'
import { LoadingContext } from '@/components/load/loadingProvider'

interface IProps {
  //选中的是哪个节点
  leafKey: string
}

export default class extends React.Component<IProps> {
  static contextType = LoadingContext
  context: React.ContextType<typeof LoadingContext>

  state = {
    //table数据
    keyList: [],
    dateId: ''
  }

  componentDidMount(): void {
    this.setState({ dateId: this.props.leafKey }, () => {
      // 获取列表数据
      this.getKeyList()
    })
  }

  componentDidUpdate(prevProps: Readonly<IProps>, prevState: Readonly<{}>, snapshot?: any): void {
    if (this.props.leafKey != prevProps.leafKey) {
      this.setState({ dateId: this.props.leafKey }, () => {
        // 获取列表数据
        this.getKeyList()
      })
    }
  }

  getKeyList = () => {
    if (this.state.dateId) {
      this.context.showLoading()
      // this.state.dateId.split('-')[0] 是截取年份
      getSpecialAssessmentDetails({ dateYear: this.state.dateId.split('-')[0] })
        .then((res) => {
          // console.log('获取“十大行动”重点工作考核情况数据', res)
          this.setState({ keyList: res.data.list })
          this.context.hideLoading()
        })
        .catch(() => {
          this.context.hideLoading()
        })
    }
  }
  render(): React.ReactNode {
    // console.log('当前dateId值', this.state.dateId.split('-')[0])

    return (
      <div style={{ height: 'calc(100% - 10px)' }}>
        <div className="div-table" style={{ height: 'calc(100vh - 250px)', overflow: 'auto' }}>
          <table className="common-table" style={{ tableLayout: 'auto', minWidth: '100%', marginBottom: '25px' }}>
            <thead>
              <tr>
                <th>专项考核</th>
                <th>历城</th>
                <th>章丘</th>
                <th>长清</th>
                <th>平阴</th>
                <th>济阳</th>
                <th>商河</th>
              </tr>
            </thead>
            <tbody>
              {this.state.keyList.length > 0 ? (
                this.state.keyList.map((item: any, index: number) => (
                  // 长文本列使用 whiteSpace: 'pre-wrap' 和 wordBreak: 'break-word' 确保文本换行显示
                  // 部门列使用 whiteSpace: 'nowrap' 保持单行显示
                  <tr key={index}>
                    <td>{item.specialProjectItem}</td>
                    <td>{item.licheng}</td>
                    <td>{item.zhangqiu}</td>
                    <td>{item.changqing}</td>
                    <td>{item.pingyin}</td>
                    <td>{item.jiyang}</td>
                    <td>{item.shanghe}</td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={8} style={{ textAlign: 'center', padding: '20px' }}>
                    暂无数据
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    )
  }
}
