import React from 'react'
import { EncryptRSA } from '@/utils'
import { with<PERSON><PERSON><PERSON> } from 'tr-cheers'
import './index.scss'
import { getCode, login } from '@/api'
import { Input, Modal, Space } from 'antd'
import { LoadingContext } from '@/components/load/loadingProvider'
import { getKeys } from '@/axios'

// @ts-ignore
@withRouter
export default class Login extends React.Component {
  static contextType = LoadingContext
  context: React.ContextType<typeof LoadingContext>

  state = {
    loginName: '',
    password: '',
    captchaSrc: '',
    code: '',
    captchaId: '',
    isEncrypted: false,
    remember: false,
    disabled: false
  }

  componentDidMount(): void {
    getKeys().then(() => {
      //获取验证码
      this.getCaptcha()
    })
  }

  getCaptcha = () => {
    getCode().then((res: any) => {
      if (res?.data?.captchaId) {
        this.setState({
          captchaId: res.data.captchaId,
          captchaSrc: res.data.imgData
        })
      }
    })
  }

  loginNameInput(e: any) {
    this.setState({ loginName: e.target.value })
  }

  passwordInput(e: any) {
    this.setState({ password: e.target.value, isEncrypted: false })
  }

  codeInput(e: any) {
    this.setState({ code: e.target.value })
  }

  check() {
    if (!this.state.loginName) {
      Modal.warning({
        title: '提示',
        content: '请输入用户名！',
        zIndex: 1100
      })
      return false
    }
    if (!this.state.password) {
      Modal.warning({
        title: '提示',
        content: '请输入密码！',
        zIndex: 1100
      })
      return false
    }
    if (!this.state.code) {
      Modal.warning({
        title: '提示',
        content: '请输入验证码！',
        zIndex: 1100
      })
      return false
    }

    return true
  }
  getPWD() {
    if (this.state.password.length === 172) {
      return this.state.password
    } else {
      try {
        return EncryptRSA(this.state.password)
      } catch (error) {
        return this.state.password
      }
    }
  }
  login() {
    if (!this.check()) return
    this.context.showLoading()
    let password =
      (this.state.remember && this.state.isEncrypted) || (!this.state.remember && this.state.isEncrypted)
        ? this.state.password
        : (this.state.remember && !this.state.isEncrypted) || (!this.state.remember && !this.state.isEncrypted)
        ? this.getPWD()
        : ''
    let params = {
      loginName: this.state.loginName,
      password: password,
      captchaId: this.state.captchaId,
      code: this.state.code
    }
    login(params)
      .then((res: any) => {
        if (res.code === '0') {
          localStorage.setItem('token', 'Bearer ' + res.data.token)
          localStorage.setItem('loginName', this.state.loginName)
          localStorage.setItem('userId', res.data.userId)
          localStorage.setItem('userName', res.data.userName)
          ;(this.props as any).navigate('/')
        } else {
          this.getCaptcha()
        }
        this.context.hideLoading()
      })
      .catch(() => {
        this.getCaptcha()
        this.context.hideLoading()
      })
  }

  render(): React.ReactNode {
    return (
      <div className="login-page">
        <div className="login-group">
          <div className="login-left"></div>
          <div className="login-right">
            <div className="login-main">
              <div className="form-head">
                <div className="left-title">
                  <div className="top-title">欢迎来到</div>
                  <div className="btm-title">{Global.title}</div>
                </div>
                <div className="right-img"></div>
              </div>
              <div className="login-type">
                <div className="tag"></div>
                用户登录
              </div>
              <div className="form-item">
                <Space.Compact block size="large">
                  <div className="self-input">
                    <div className="user"></div>
                    <Input
                      placeholder="请您输入用户名"
                      style={{ border: 'none', borderRadius: '0px 5px 5px 0px' }}
                      value={this.state.loginName}
                      onInput={this.loginNameInput.bind(this)}
                      onKeyDown={(e) => e.key == 'Enter' && this.login()}
                    />
                  </div>
                </Space.Compact>
              </div>
              <div className="form-item">
                <Space.Compact block size="large">
                  <div className="self-input">
                    <div className="pwd"></div>
                    <Input.Password
                      style={{ border: 'none', borderRadius: '0px 5px 5px 0px' }}
                      className="pwd-input"
                      placeholder="请您输入密码"
                      value={this.state.password}
                      onInput={this.passwordInput.bind(this)}
                      onKeyDown={(e) => e.key == 'Enter' && this.login()}
                    />
                  </div>
                </Space.Compact>
              </div>
              <div className="form-item">
                <Space.Compact block size="large">
                  <div className="self-input" style={{ position: 'relative' }}>
                    <div className="code"></div>
                    <Input
                      placeholder="请您输入验证码"
                      style={{ border: 'none', borderRadius: '0px 5px 5px 0px' }}
                      value={this.state.code}
                      onInput={this.codeInput.bind(this)}
                      onKeyDown={(e) => e.key == 'Enter' && this.login()}
                    />
                    <img src={this.state.captchaSrc} onClick={this.getCaptcha} />
                  </div>
                </Space.Compact>
              </div>
              <div
                className="common-button"
                style={{ marginTop: '58px', height: '53px', lineHeight: '53px' }}
                onClick={this.login.bind(this)}
              >
                登&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;录
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }
}
