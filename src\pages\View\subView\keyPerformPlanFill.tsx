import { getPrefPlanReportViewList } from '@/api'
import React from 'react'
import { LoadingContext } from '@/components/load/loadingProvider'

interface IProps {
  //选中的是哪个节点
  leafKey: string
}

export default class extends React.Component<IProps> {
  static contextType = LoadingContext
  context: React.ContextType<typeof LoadingContext>

  state = {
    //table数据
    keyList: [],
    dateId: ''
  }

  componentDidMount(): void {
    this.setState({ dateId: this.props.leafKey }, () => {
      // 获取列表数据
      this.getKeyList()
    })
  }

  componentDidUpdate(prevProps: Readonly<IProps>, prevState: Readonly<{}>, snapshot?: any): void {
    if (this.props.leafKey != prevProps.leafKey) {
      this.setState({ dateId: this.props.leafKey }, () => {
        // 获取列表数据
        this.getKeyList()
      })
    }
  }

  getKeyList = () => {
    if (this.state.dateId) {
      this.context.showLoading()
      getPrefPlanReportViewList({ date: this.state.dateId })
        .then((res) => {
          if (res?.data?.list?.length > 0) {
            for (let i = 0; i < res.data.list.length; i++) {
              let item = res.data.list[i]
              //设置牵头部门
              let znbms = []
              if (item.orgName1) {
                znbms.push(item.orgName1)
              }
              if (item.orgName2) {
                znbms.push(item.orgName2)
              }
              let znbm = znbms.join('、')
              item.znbm = znbm
              for (let j = 0; j < item?.allLineList?.length; j++) {
                let obj = item?.allLineList[j]
                let orgNames = []
                for (let k = 0; k < obj?.lineList?.length; k++) {
                  orgNames.push(obj.lineList[k].orgName)
                }
                obj.orgNames = orgNames.join('、')
              }
            }
            this.setState({ keyList: res.data.list })
          } else {
            this.setState({ keyList: [] })
          }
          this.context.hideLoading()
        })
        .catch(() => {
          this.context.hideLoading()
        })
    }
  }
  render(): React.ReactNode {
    return (
      <div style={{ height: 'calc(100% - 10px)' }}>
        <div className="div-table">
          <table className="common-table">
            <thead>
              <tr>
                <th rowSpan={2}>序号</th>
                <th rowSpan={2}>计划名称</th>
                <th rowSpan={2}>类型</th>
                <th rowSpan={2}>完成情况</th>
                <th colSpan={3}>考评结果</th>
              </tr>
              <tr>
                <th>牵头部门</th>
                <th>配合部门名称</th>
                <th>得分</th>
              </tr>
            </thead>
            <tbody>
              {this.state.keyList.map((item, index) => {
                return item.allLineList.map((obj, index2) => {
                  return (
                    <tr key={`${index}-${index2}`}>
                      {index2 == 0 && (
                        <td style={{ width: '45px' }} rowSpan={item.allLineList.length}>
                          {index + 1}
                        </td>
                      )}
                      {index2 == 0 && (
                        <td style={{ width: '340px' }} rowSpan={item.allLineList.length}>
                          {item.planName}
                        </td>
                      )}
                      {index2 == 0 && (
                        <td style={{ width: '45px' }} rowSpan={item.allLineList.length}>
                          {item.workTypeVal}
                        </td>
                      )}
                      {index2 == 0 && <td rowSpan={item.allLineList.length}>{item.completionStatus}</td>}
                      {index2 == 0 && (
                        <td style={{ width: '90px' }} rowSpan={item.allLineList.length}>
                          <div>{item.znbm}</div>
                          <div>({item.workTypeScore}分)</div>
                        </td>
                      )}
                      <td style={{ width: '130px' }}>{obj.orgNames}</td>
                      <td style={{ width: '90px' }}>{obj.ratio}</td>
                    </tr>
                  )
                })
              })}
            </tbody>
          </table>
        </div>
      </div>
    )
  }
}
