import { getSpecialRewardViewList } from '@/api'
import React from 'react'
import { LoadingContext } from '@/components/load/loadingProvider'

interface PropsOption {
  //选中的是哪个节点
  leafKey: string
}

export default class extends React.Component<PropsOption> {
  static contextType = LoadingContext
  context: React.ContextType<typeof LoadingContext>

  state = {
    dateId: '', //时间
    dataList: []
  }

  componentDidMount(): void {
    this.setState({ dateId: this.props.leafKey }, () => {
      this.getTableList()
    })
  }

  componentDidUpdate(prevProps: Readonly<PropsOption>, prevState: Readonly<{}>, snapshot?: any): void {
    if (this.props.leafKey != prevProps.leafKey) {
      this.setState({ dateId: this.props.leafKey }, () => {
        this.getTableList()
      })
    }
  }
  getTableList() {
    if (this.state.dateId) {
      this.context.showLoading()
      getSpecialRewardViewList({ date: this.state.dateId })
        .then((res) => {
          if (res?.data?.list?.length > 0) {
            let list = res.data.list
            for (let i = 0; i < list.length; i++) {
              let item = list[i]
              for (let j = 0; j < item?.list?.length; j++) {
                let obj = item.list[j]
                for (let k = 0; k < obj?.allLineList?.length; k++) {
                  let line = obj?.allLineList[k]
                  let orgNames = []
                  for (let m = 0; m < line?.lineList?.length; m++) {
                    orgNames.push(line.lineList[m].orgName)
                  }
                  line.orgNames = orgNames.join('、')
                }
              }
            }
            this.setState({ dataList: res.data.list })
          } else {
            this.setState({
              dataList: []
            })
          }
          this.context.hideLoading()
        })
        .catch(() => {
          this.context.hideLoading()
        })
    }
  }

  render() {
    return (
      <div className="content-center" style={{ height: '100%' }}>
        <div className="div-table">
          <table className="common-table">
            <thead>
              <tr>
                <th>序号</th>
                <th>提报部门</th>
                <th>奖励内容</th>
                <th>奖励部门</th>
                <th>奖励金额</th>
              </tr>
            </thead>
            <tbody>
              {this.state.dataList.map((outItem, outIndex) => {
                //计算rowspan
                let rowSpan = 0
                outItem.list.map((row) => {
                  rowSpan = rowSpan + row?.allLineList?.length
                })
                return outItem.list.map((centerItem, centerIndex) => {
                  return centerItem.allLineList.map((item, index) => {
                    return (
                      <tr key={`${outIndex} + ${centerIndex} + ${index}`}>
                        {centerIndex == 0 && index == 0 && (
                          <td style={{ width: '45px' }} rowSpan={rowSpan}>
                            {outIndex + 1}
                          </td>
                        )}
                        {centerIndex == 0 && index == 0 && (
                          <td style={{ minWidth: '100px' }} rowSpan={rowSpan}>
                            {outItem.orgName}
                          </td>
                        )}
                        {index == 0 && <td rowSpan={centerItem.allLineList.length}>{centerItem.rewardDetails}</td>}
                        <td style={{ minWidth: '120px' }}>{item.orgNames}</td>
                        <td style={{ width: '100px' }}>{item.amount}</td>
                      </tr>
                    )
                  })
                })
              })}
            </tbody>
          </table>
        </div>
      </div>
    )
  }
}
