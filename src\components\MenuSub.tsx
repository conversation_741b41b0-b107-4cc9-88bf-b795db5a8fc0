import { getSubMenu } from '@/api'
import axios from 'axios'

let GLOBALPERMISSION = new Map()

export async function getMenuSub() {
  let hashs = window.location.hash.split('/')
  let currentUrl = '/' + hashs[hashs.length - 1]
  if (GLOBALPERMISSION.size == 0) {
    await getMeun()
  }
  if (GLOBALPERMISSION.has(currentUrl)) {
    let tabs = await getSubMenu({ menuId: GLOBALPERMISSION.get(currentUrl) })
    return tabs
  }
}

async function getMeun() {
  //调用接口
  let res = await axios.get('/api/v1/ca/auth/menu', {
    headers: {
      Application: 'PDAS'
    }
  })
  if (res?.data) {
    GLOBALPERMISSION.clear()
    let menus = res.data
    menus.forEach((menu: any) => {
      if (menu.menuUrl) {
        GLOBALPERMISSION.set(menu.menuUrl, menu.id)
      }
    })
  }
}
