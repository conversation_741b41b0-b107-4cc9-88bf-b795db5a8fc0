{"include": ["src/**/*"], "exclude": ["node_modules", "**/dist/*", "src/assets/icons/**"], "compilerOptions": {"jsx": "react", "target": "ES2018", "allowJs": true, "module": "commonjs", "moduleResolution": "node", "esModuleInterop": true, "experimentalDecorators": true, "sourceMap": true, "baseUrl": ".", "paths": {"@/*": ["src/*"]}, "types": ["@types/node"], "typeRoots": ["../node_modules/@types", "../@types"]}}