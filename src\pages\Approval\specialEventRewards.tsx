import React from 'react'
import DateMenu from '@/components/DateMenu'
import { Iconfont } from 'tr-cheers'
import { getLeafKey, getUUID, validateNumber } from '@/utils/utils'
import { Checkbox, Input, InputNumber, Modal, Select } from 'antd'
import { delPdasSpecialReward, getMenuDate, getOrgList, pdasSpecialRewardList, postPdasSpecialReward } from '@/api'
import { LoadingContext } from '@/components/load/loadingProvider'
import Department from '../Common/department'
const { TextArea } = Input

export default class extends React.Component {
  static contextType = LoadingContext
  context: React.ContextType<typeof LoadingContext>

  state = {
    dateId: '', //时间
    //叶子节点
    leafKey: '',
    leftMenu: [],
    dataList: [],
    //职能部门列表
    znbmList: [],
    //有效的部门列表
    bmActiveList: [],
    checkboxes: [], // 多选框
    departMent: {
      isDepartVisible: false, //弹出部门选择
      indexBig: '',
      indexSmall: '',
      selectedCode: []
    }
  }

  componentDidMount(): void {
    //获取职能部门列表
    this.getZnbmList()
    //获取左侧菜单
    this.getLeftMenu()
  }

  getZnbmList() {
    getOrgList('01,02,03').then((res) => {
      let bmActiveList = []
      for (let i = 0; i < res?.data?.list?.length; i++) {
        let item = res.data.list[i]
        if (item.isActive == '1') {
          bmActiveList.push(item)
        }
      }
      this.setState({ znbmList: res.data.list, bmActiveList: bmActiveList })
    })
  }

  getLeftMenu() {
    this.context.showLoading()
    getMenuDate()
      .then((res: any) => {
        if (res.data && res.data.length > 0) {
          let leftMenu = res.data
          //设置dateId和叶子节点
          let leaf = null
          for (let i = 0; i < leftMenu.length; i++) {
            if (!leaf) {
              leaf = getLeafKey(leftMenu[i], true)
            } else {
              break
            }
          }
          this.setState({ leftMenu: leftMenu, leafKey: leaf.dateId, dateId: leaf.dateId }, () => {
            //获取表格内容
            this.getTableList()
          })
        }
      })
      .catch(() => {
        this.context.hideLoading()
      })
  }

  //菜单树点击
  handleMenu = (e) => {
    this.setState({ dateId: e.key, leafKey: e.key }, () => {
      //获取表格内容
      this.getTableList()
    })
  }
  getTableList() {
    this.context.showLoading()
    pdasSpecialRewardList(this.state.dateId)
      .then((res) => {
        res.data.list.forEach((item) => {
          if (item.allLineList.length == 0) item.allLineList = [{ amount: null, lineList: [] }]
        })
        this.setState({ dataList: res.data.list })
        this.context.hideLoading()
      })
      .catch(() => {
        this.context.hideLoading()
      })
  }

  //新增一行
  handleAdd = () => {
    //判断有无正在编辑的内容，如果有，不能进行编辑，提示：有正在编辑的内容，请完成后，再新增一行
    let flag = false //默认没有编辑的
    for (let i = 0; i < this.state.dataList.length; i++) {
      let obj = this.state.dataList[i]
      if (obj.isEdit) {
        flag = true
        break
      }
    }
    if (flag) {
      Modal.warning({
        title: '提示',
        content: '有正在编辑的内容，请完成后，再新增一行！',
        zIndex: 1100
      })
      return
    }
    let list = [...this.state.dataList]
    list.push({
      psrId: 'uuid' + getUUID(),
      repoOrgId: '',
      rewardDetails: '',
      allLineList: [{ amount: null, lineList: [], isSmallEdit: true }],
      isEdit: true
    })
    this.setState({ dataList: list })
  }

  handleContentChange = (value: any, index: number, fieldName: string, indexSmall?: number) => {
    const dataList = [...this.state.dataList]
    if (indexSmall !== undefined) {
      if (fieldName == 'lineList') {
        dataList[index].allLineList[indexSmall][fieldName] = value.map((item) => {
          const org = this.state.znbmList.find((org) => org.id == item)
          return { orgId: item, orgName: org?.orgName }
        })
      } else dataList[index].allLineList[indexSmall][fieldName] = value
    } else {
      dataList[index][fieldName] = value
    }
    this.setState({ dataList })
  }

  handleEditAwardContent = (index: number) => {
    const dataList = [...this.state.dataList]
    dataList[index].isEdit = true
    this.setState({ dataList })
  }

  handleSaveAwardContent = (index: number) => {
    // 提报部门不能为空
    if (!this.state.dataList[index].repoOrgId) {
      Modal.warning({
        title: '提示',
        content: '提报部门不能为空！',
        zIndex: 1100
      })
      return
    }
    if (!this.state.dataList[index].rewardDetails) {
      Modal.warning({
        title: '提示',
        content: '奖励内容不能为空！',
        zIndex: 1100
      })
      return
    }
    this.handlePostData(this.state.dataList[index])
    const dataList = [...this.state.dataList]
    dataList[index].isEdit = false
    this.setState({ dataList })
  }

  handleEditSmall = (index: number, indexSmall: number) => {
    const dataList = [...this.state.dataList]
    dataList[index].allLineList[indexSmall].isSmallEdit = true
    this.setState({ dataList })
  }

  handleDeleteSmall = (index: number, indexSmall: number) => {
    Modal.confirm({
      title: '删除提示',
      content: '确定删除吗?',
      onOk: () => {
        const dataList = [...this.state.dataList]
        dataList[index].allLineList.splice(indexSmall, 1)
        this.handlePostData(dataList[index])
        this.setState({ dataList })
      }
    })
  }

  handleSaveSmall = (index: number, indexSmall: number) => {
    const list = this.state.dataList[index].allLineList
    for (const item of list) {
      if (!item.lineList.length) {
        Modal.warning({
          title: '提示',
          content: '请选择奖励部门名称！',
          zIndex: 1100
        })
        return
      }
      if (!item.amount) {
        Modal.warning({
          title: '提示',
          content: '请输入奖励金额！',
          zIndex: 1100
        })
        return
      }
    }
    this.handlePostData(this.state.dataList[index])
    const dataList = [...this.state.dataList]
    dataList[index].allLineList[indexSmall].isSmallEdit = false
    this.setState({ dataList })
  }

  handlePostData(data: any) {
    const temp = JSON.parse(JSON.stringify(data))
    delete temp.isEdit
    if (temp.psrId.startsWith('uuid')) delete temp.psrId
    temp.date = this.state.dateId
    this.context.showLoading()
    postPdasSpecialReward(temp)
      .then((res) => {
        if (data.psrId.startsWith('uuid')) {
          data.psrId = res.data.psrId
          this.setState({ dataList: [...this.state.dataList] })
        }
        this.context.hideLoading()
      })
      .catch(() => {
        this.context.hideLoading()
      })
  }

  handleAddRow = (index: number) => {
    const dataList = [...this.state.dataList]
    const currentRow = dataList[index]
    if (currentRow.allLineList.some((item) => item.isSmallEdit)) {
      Modal.warning({
        title: '提示',
        content: '有正在编辑的内容，请完成后，再新增一行！',
        zIndex: 1100
      })
      return
    }
    currentRow.allLineList.push({
      lineList: [],
      amount: null,
      isSmallEdit: true
    })
    this.setState({ dataList })
  }

  //全选，全不选
  onCheckAllChange = () => {
    const { dataList, checkboxes } = this.state
    if (checkboxes.length === dataList.length) {
      this.setState({ checkboxes: [] })
    } else {
      this.setState({ checkboxes: dataList.map((item) => item.psrId) })
    }
  }

  handleCheckboxChange = (psrId, event) => {
    const { checkboxes } = this.state
    if (event.target.checked) {
      checkboxes.push(psrId)
    } else {
      checkboxes.splice(checkboxes.indexOf(psrId), 1)
    }
    this.setState({ checkboxes })
  }

  handleDeleteRow = () => {
    const { checkboxes, dataList } = this.state
    if (checkboxes.length === 0) {
      Modal.warning({
        title: '提示',
        content: '请选择要删除的行！',
        zIndex: 1100
      })
      return
    }
    Modal.confirm({
      title: '删除提示',
      content: '确定删除吗?',
      onOk: () => {
        const newDataList = dataList.filter((item) => !checkboxes.includes(item.psrId))
        const removeList = dataList.filter((item) => checkboxes.includes(item.psrId))
        const ids = removeList.filter((item) => !item.psrId.startsWith('uuid'))
        this.context.showLoading()
        if (ids.length === 0) {
          this.setState({ dataList: newDataList, checkboxes: [] })
          this.context.hideLoading()
          return
        }
        delPdasSpecialReward(ids.map((item) => item.psrId))
          .then((res) => {
            this.setState({ dataList: newDataList, checkboxes: [] })
            this.context.hideLoading()
          })
          .catch(() => {
            this.context.hideLoading()
          })
      }
    })
  }

  //选择部门名称
  openModalDepartMent = (indexBig, indexSmall) => {
    let list = [...this.state.dataList]
    let selectedCode = []
    list[indexBig].allLineList[indexSmall]?.lineList?.map((item) => {
      selectedCode.push(item.orgId)
    })
    this.setState({ departMent: { isDepartVisible: true, indexBig, indexSmall, selectedCode } })
  }

  //部门选择确定
  determine = (selectedList) => {
    let list = [...this.state.dataList]
    let indexBig = this.state.departMent.indexBig
    let indexSmall = this.state.departMent.indexSmall
    let bmmcCode = []
    selectedList?.map((item) => bmmcCode.push({ orgName: item.orgName, orgId: item.id }))
    list[indexBig].allLineList[indexSmall].lineList = bmmcCode
    this.setState({ dataList: list })
  }

  render() {
    return (
      <div className="main-common">
        <div className="main-left">
          {this.state.dateId && (
            <DateMenu
              menuList={this.state.leftMenu}
              dateId={this.state.dateId}
              leafKey={this.state.leafKey}
              onClick={(e) => this.handleMenu(e)}
            />
          )}
        </div>
        <div className="main-right">
          <div className="content-center">
            <div style={{ display: 'flex', justifyContent: 'end' }}>
              <div style={{ marginLeft: 'auto', display: 'flex' }}>
                <div className="common-button2 common-button3" onClick={() => this.handleAdd()}>
                  <Iconfont type="icon-xinzeng" style={{ fontSize: '20px' }} />
                  <div className="common-text2">新增一行</div>
                </div>
              </div>
              <div
                className={`common-button2 common-button3 ${this.state.checkboxes.length == 0 && 'disabled-div'}`}
                onClick={this.handleDeleteRow}
                style={{ marginLeft: '10px' }}
              >
                <Iconfont type="icon-shanchu" style={{ fontSize: '20px' }} />
                <div className="common-text">删除</div>
              </div>
            </div>

            <div className="div-table">
              <table className="common-table">
                <thead>
                  <tr>
                    <th>
                      <Checkbox
                        onChange={() => this.onCheckAllChange()}
                        checked={
                          this.state.checkboxes.length === this.state.dataList.length &&
                          this.state.dataList.length !== 0
                        }
                      ></Checkbox>
                    </th>
                    <th>序号</th>
                    <th>提报部门</th>
                    <th>奖励内容</th>
                    <th>奖励部门</th>
                    <th>奖励金额</th>
                    <th>操作</th>
                  </tr>
                </thead>
                <tbody>
                  {this.state.dataList.map((item, index) =>
                    item.allLineList.map((phbmItem, phbmIndex) => (
                      <tr key={`${index}-${phbmIndex}`}>
                        {phbmIndex === 0 && (
                          <td style={{ width: '45px' }} rowSpan={item.allLineList.length}>
                            <Checkbox
                              type="checkbox"
                              checked={this.state.checkboxes.includes(item.psrId)}
                              onChange={this.handleCheckboxChange.bind(this, item.psrId)}
                            />
                          </td>
                        )}
                        {phbmIndex === 0 && (
                          <td style={{ width: '45px' }} rowSpan={item.allLineList.length}>
                            {index + 1}
                          </td>
                        )}
                        {phbmIndex === 0 && (
                          <td style={{ width: '140px' }} rowSpan={item.allLineList.length}>
                            {item.isEdit ? (
                              <Select
                                allowClear
                                value={item.repoOrgId}
                                placeholder="请选择"
                                onChange={(value) => this.handleContentChange(value, index, 'repoOrgId')}
                                showSearch
                                optionFilterProp="children"
                                filterOption={(input: any, option: any) => option.children.indexOf(input) >= 0}
                              >
                                {this.state.bmActiveList.map((zditem) => (
                                  <Select.Option key={zditem.id} value={zditem.id}>
                                    {zditem.orgName}
                                  </Select.Option>
                                ))}
                              </Select>
                            ) : (
                              this.state.znbmList.find((zditem) => zditem.id === item.repoOrgId)?.orgName
                            )}
                          </td>
                        )}
                        {phbmIndex === 0 && (
                          <td style={{ width: '460px' }} rowSpan={item.allLineList.length}>
                            {item.isEdit ? (
                              <div style={{ display: 'flex', alignItems: 'center' }}>
                                <TextArea
                                  autoSize
                                  maxLength={1000}
                                  value={item.rewardDetails}
                                  placeholder="请填写奖励内容"
                                  onChange={(e) => this.handleContentChange(e.target.value, index, 'rewardDetails')}
                                />
                                <div className="row-opera" onClick={() => this.handleSaveAwardContent(index)}>
                                  <Iconfont type="icon-icon_duihao-mian" style={{ fontSize: '16px' }} />
                                  <span className="operate-text">保存</span>
                                </div>
                              </div>
                            ) : (
                              <div
                                style={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'space-between',
                                  width: '100%'
                                }}
                              >
                                <div className="row-text">{item.rewardDetails}</div>
                                <div className="row-opera" onClick={() => this.handleEditAwardContent(index)}>
                                  <Iconfont type="icon-bianji3" style={{ fontSize: '16px' }} />
                                  <span className="operate-text">编辑</span>
                                </div>
                              </div>
                            )}
                          </td>
                        )}
                        <td style={{ width: '140px' }}>
                          {phbmItem.lineList.map((item) => item.orgName).join('，')}
                          {phbmItem.isSmallEdit && (
                            <div
                              style={{ cursor: 'pointer', color: '#1890ff' }}
                              onClick={() => this.openModalDepartMent(index, phbmIndex)}
                            >
                              点击选择奖励部门
                            </div>
                          )}
                        </td>
                        <td style={{ width: '105px' }}>
                          {phbmItem.isSmallEdit ? (
                            <InputNumber
                              controls={false}
                              precision={0}
                              // min={0}
                              value={phbmItem.amount}
                              onChange={(value) => {
                                if (!validateNumber(value, '请输入>0的整数！', '', 0)) return
                                this.handleContentChange(value, index, 'amount', phbmIndex)
                              }}
                            />
                          ) : (
                            phbmItem.amount
                          )}
                        </td>
                        <td style={{ width: '150px' }}>
                          <div className="table-operate" style={{ justifyContent: 'left' }}>
                            {phbmItem.isSmallEdit ? (
                              <div onClick={() => this.handleSaveSmall(index, phbmIndex)}>
                                <Iconfont type="icon-icon_duihao-mian" style={{ fontSize: '16px' }} />
                                <span className="operate-text">保存</span>
                              </div>
                            ) : (
                              <div onClick={() => this.handleEditSmall(index, phbmIndex)}>
                                <Iconfont type="icon-bianji3" style={{ fontSize: '16px' }} />
                                <span className="operate-text">编辑</span>
                              </div>
                            )}
                            {phbmIndex !== 0 && (
                              <div
                                onClick={() => this.handleDeleteSmall(index, phbmIndex)}
                                style={{ paddingLeft: '10px' }}
                              >
                                <Iconfont type="icon-shanchu" style={{ fontSize: '16px' }} />
                                <span className="operate-text">删除</span>
                              </div>
                            )}
                            {phbmIndex === 0 && (
                              <div onClick={() => this.handleAddRow(index)} style={{ paddingLeft: '10px' }}>
                                <Iconfont type="icon-xinzeng" style={{ fontSize: '16px' }} />
                                <span className="operate-text">新增一行</span>
                              </div>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </div>
        {this.state.departMent.isDepartVisible && (
          <Department
            isModalVisible={this.state.departMent.isDepartVisible}
            list={this.state.bmActiveList}
            selectedCode={this.state.departMent.selectedCode}
            closeModal={() => this.setState({ departMent: { isDepartVisible: false } })}
            determine={(selectedList) => this.determine(selectedList)}
          />
        )}
      </div>
    )
  }
}
