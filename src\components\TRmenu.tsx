import React from 'react'
import { Menu } from 'antd'
import { NavLink } from 'react-router-dom'
import { withRouter, ComposeTree } from 'tr-cheers'
import './menu.scss'
import { setMenuSub } from './MenuSub'

type IMenu = any

interface IProps {
  /** 菜单数据，CA列表结构 */
  menus: Array<IMenu>
  /** 是否使用二级数据，兼容CA菜单 */
  Level2?: boolean
  /** 菜单的点击事件 */
  onClick?: (menu: IMenu) => void
  /** 默认进入的菜单 */
  indexPath?: string
  /**
   * 是否只有一个菜单展开
   * @default fasle
   */
  uniqueOpened?: boolean
  /** location, withRouter附带，不可编辑 */
  location?: any
}

// @ts-ignore
@withRouter
export default class TrMenus extends React.Component<IProps> {
  state = {
    CaMenu: [],
    current: '',
    openKeys: []
  }
  menuMap = new Map()
  componentDidMount() {
    this.setMenu()
  }
  componentDidUpdate(preProps: IProps) {
    if (preProps.menus !== this.props.menus) {
      this.setMenu()
    }
    if (this.props.location.pathname !== preProps.location.pathname) {
      if (this.props.location.pathname.includes('/globalSearch')) {
        this.setState({ current: '' })
        return
      }
      // 默认展开菜单
      let data = this.props.menus
      if (!data || data.length == 0) return
      let openKey = ''
      let pathname = (this.props as any).location.pathname
      if (this.menuMap.has(pathname)) {
        let openKeyPid = this.menuMap.get(pathname).pMenuId
        let openKeyMenu = data.find((item) => item.id == openKeyPid)
        if (openKeyMenu) openKey = (openKeyMenu && openKeyMenu.menuUrl) || openKeyMenu.id
        this.setState({ current: pathname, openKeys: openKey ? [openKey] : [] })
      } else {
        for (let [key, value] of this.menuMap) {
          if (key.startsWith('/')) {
            pathname = key
            ;(this.props as any).navigate(key)
            break
          }
        }
      }
    }
  }

  GenSubMenu(menus: Array<any>) {
    return menus.map((menu: any) => {
      let hasChild = Array.isArray(menu.children) && menu.children.length > 0
      if (hasChild) {
        return (
          <Menu.SubMenu
            key={menu.menuUrl || menu.id}
            title={menu.menuName}
            icon={
              <img
                style={{
                  marginTop: '15px'
                }}
                src={menu.icon}
              />
            }
          >
            {this.GenSubMenu(menu.children)}
          </Menu.SubMenu>
        )
      } else {
        return (
          <Menu.Item key={menu.menuUrl || menu.id}>
            <NavLink to={menu.menuUrl}>{menu.menuName}</NavLink>
          </Menu.Item>
        )
      }
    })
  }
  setMenu() {
    this.menuMap.clear()
    let data = this.props.menus
    if (!data || data.length == 0) return
    data.forEach((item: any) => {
      this.menuMap.set(item.menuUrl || item.id, item)
    })
    // 后端的数据可能重复，这里进行去重
    let menus = []
    this.menuMap.forEach((item) => {
      menus.push(item)
    })
    // 设置菜单
    let tree = ComposeTree(menus, 'pMenuId')
    tree = this.props.Level2 ? tree[0].children : tree
    // 设置激活菜单
    let pathname = this.props?.location.pathname === '/' ? this.props.indexPath : this.props?.location.pathname
    // 默认展开菜单
    let openKey = ''
    if (this.menuMap.has(pathname)) {
      let openKeyPid = this.menuMap.get(pathname).pMenuId
      let openKeyMenu = data.find((item) => item.id == openKeyPid)
      if (openKeyMenu) openKey = (openKeyMenu && openKeyMenu.menuUrl) || openKeyMenu.id
    } else {
      for (let [key, value] of this.menuMap) {
        if (key.startsWith('/')) {
          pathname = key
          ;(this.props as any).navigate(key)
          break
        }
      }
    }
    this.setState({ CaMenu: tree, current: pathname, openKeys: openKey ? [openKey] : [] }, () =>
      this.handleClick({ key: pathname })
    )
  }
  handleClick(e: any) {
    this.setState({ current: e.key }, () => this.props?.onClick?.(this.menuMap.get(this.state.current)))
  }
  onOpenChange(openKeys) {
    if (!this.props.uniqueOpened) {
      this.setState({ openKeys: openKeys })
    } else {
      // 只展开一个菜单
      let openKey = openKeys[openKeys.length - 1]
      this.setState({ openKeys: openKey ? [openKey] : [] })
    }
  }
  render() {
    return (
      <Menu
        mode="inline"
        theme="dark"
        onClick={this.handleClick.bind(this)}
        selectedKeys={[this.state.current]}
        openKeys={this.state.openKeys}
        onOpenChange={this.onOpenChange.bind(this)}
        className="tr-menu"
      >
        {this.GenSubMenu(this.state.CaMenu)}
      </Menu>
    )
  }
}
