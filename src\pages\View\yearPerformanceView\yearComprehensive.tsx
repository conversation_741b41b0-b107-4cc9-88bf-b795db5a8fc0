import { getYearComprehensiveDetails } from '@/api'
import React from 'react'
import { LoadingContext } from '@/components/load/loadingProvider'

interface IProps {
  //选中的是哪个节点
  leafKey: string
}

export default class extends React.Component<IProps> {
  static contextType = LoadingContext
  context: React.ContextType<typeof LoadingContext>

  state = {
    //table数据
    keyList: [],
    dateId: ''
  }

  componentDidMount(): void {
    this.setState({ dateId: this.props.leafKey }, () => {
      // 获取列表数据
      this.getKeyList()
    })
  }

  componentDidUpdate(prevProps: Readonly<IProps>, prevState: Readonly<{}>, snapshot?: any): void {
    if (this.props.leafKey != prevProps.leafKey) {
      this.setState({ dateId: this.props.leafKey }, () => {
        // 获取列表数据
        this.getKeyList()
      })
    }
  }

  getKeyList = () => {
    if (this.state.dateId) {
      this.context.showLoading()
      // this.state.dateId.split('-')[0] 是截取年份
      getYearComprehensiveDetails({ dateYear: this.state.dateId.split('-')[0] })
        .then((res) => {
          // console.log('获取“十大行动”重点工作考核情况数据', res)

          this.setState({ keyList: res.data.list })
          this.context.hideLoading()
        })
        .catch(() => {
          this.context.hideLoading()
        })
    }
  }
  render(): React.ReactNode {
    // console.log('当前dateId值', this.state.dateId.split('-')[0])

    return (
      <div style={{ height: 'calc(100% - 10px)' }}>
        <div className="div-table" style={{ height: 'calc(100vh - 250px)', overflow: 'auto' }}>
          <table className="common-table" style={{ tableLayout: 'auto', minWidth: '100%', marginBottom: '25px' }}>
            <thead>
              <tr>
                <th>排名</th>
                <th>单位</th>
                <th>关键业绩指标得分</th>
                <th>专业评价得分</th>
                <th>合计</th>
                <th>考核分档</th>
              </tr>
            </thead>
            <tbody>
              {this.state.keyList.length > 0 ? (
                this.state.keyList.map((item: any, index: number) => (
                  // 长文本列使用 whiteSpace: 'pre-wrap' 和 wordBreak: 'break-word' 确保文本换行显示
                  // 部门列使用 whiteSpace: 'nowrap' 保持单行显示
                  <tr key={item.ranking}>
                    <td style={{ minWidth: '50px', whiteSpace: 'nowrap' }}>{item.ranking}</td>
                    <td style={{ minWidth: '120px', whiteSpace: 'nowrap' }}>{item.unit}</td>
                    <td style={{ minWidth: '50px', whiteSpace: 'pre-wrap', wordBreak: 'break-word' }}>
                      {item.keyPerformanceScore}
                    </td>
                    <td style={{ minWidth: '120px', whiteSpace: 'pre-wrap', wordBreak: 'break-word' }}>
                      {item.professionalEvaluationScore}
                    </td>
                    <td style={{ minWidth: '120px', whiteSpace: 'pre-wrap', wordBreak: 'break-word' }}>
                      {item.totalScore}
                    </td>
                    <td style={{ minWidth: '250px', whiteSpace: 'pre-wrap', wordBreak: 'break-word' }}>
                      {item.evaluationLevel}
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={7} style={{ textAlign: 'center', padding: '20px' }}>
                    暂无数据
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    )
  }
}
