import React from 'react'
import { Iconfont } from 'tr-cheers'
import { Input, Modal, Select, InputNumber, Checkbox } from 'antd'
import { getNumber, getUUID, decimalPlaces, getOrgNames, getStatusValue, validateNumber } from '@/utils/utils'
import MessageSelf from '@/components/message'
import {
  deleteTempNonRoutineWork,
  getDeptRelationsList,
  getOrgList,
  getTempNonRoutineWorkList,
  getUserInfo,
  pdasTempNonRoutineWorkReportPinned,
  pdasTempNonRoutineWorkReportSwap,
  pdasTempNonRoutineWorkScoreOrgPinned,
  pdasTempNonRoutineWorkScoreOrgSwap,
  resetTempNonRoutineWork,
  saveTempNonRoutineWork,
  submitTempNonRoutineWork
} from '@/api'
import { LoadingContext } from '@/components/load/loadingProvider'
import Department from '../Common/department'

const { TextArea } = Input

interface PropsOption {
  dateId: string
}

interface State {
  keyList: any[]
  bmList: any[]
  bmActiveList: any[]
  gkglyList: any[]
  repoOrgId: string
  selectedRows: any[]
  departMent: any
}

export default class ComponentName extends React.Component<PropsOption, State> {
  static contextType = LoadingContext
  context: React.ContextType<typeof LoadingContext>
  intervalId = null // 在这里定义 intervalId
  //保存前数据
  preKeyList: any = []

  state: State = {
    keyList: [], // table值
    bmList: [], // 部门列表
    //有效的部门列表
    bmActiveList: [],
    gkglyList: [], // 归口管理部门
    repoOrgId: '', // 当前部门id
    selectedRows: [], // 选中的行
    departMent: {
      isDepartVisible: false, //弹出部门选择
      indexBig: '',
      indexSmall: '',
      selectedCode: []
    }
  }

  componentDidMount() {
    this.getBmList()
    this.getGkglyList() // 获取当前部门的归口管理部门
    //获取当前用户信息
    this.getCurrentUserInfo()
    this.initializeData()
    // 每隔2分钟（120000毫秒）执行
    this.intervalId = setInterval(this.saveAll, 120000)
  }
  componentWillUnmount() {
    // 清除定时器
    clearInterval(this.intervalId)
  }

  //定时保存
  saveAll = () => {
    let list = [...this.state.keyList]
    for (let i = 0; i < list.length; i++) {
      let item = list[i]
      if (item.isEdit) {
        if (JSON.stringify(this.preKeyList[i]) !== JSON.stringify(item)) {
          //保存大行
          this.handleSaveEvaluation(i, 'regular')
        }
      } else {
        let smallList = item.allLineList
        let smallEdit = false
        for (let j = 0; j < smallList.length; j++) {
          let smallItem = smallList[j]
          if (smallItem.isSmallEdit) {
            smallEdit = true
            break
          }
        }

        if (smallEdit && JSON.stringify(this.preKeyList[i]) !== JSON.stringify(item)) {
          this.handleSaveEvaluation(i, 'regular')
        }
      }
    }
    //记录当前数据
    this.preKeyList = JSON.parse(JSON.stringify(list))
  }

  componentDidUpdate(prevProps: PropsOption) {
    if (this.props.dateId !== prevProps.dateId) {
      this.initializeData()
    }
  }

  getCurrentUserInfo = () => {
    let userId = localStorage.getItem('userId')
    if (userId) {
      getUserInfo(userId).then((res) => {
        if (res?.data) {
          this.setState({
            repoOrgId: res.data.orgId
          })
        }
      })
    } else {
      window.location.hash = '#/login'
    }
  }

  initializeData = () => {
    this.getKeyList() // 临时性非常规工作填报table数据，分为不同角色，
  }

  getBmList = () => {
    getOrgList('01,02,03').then((res: any) => {
      if (res?.data?.list?.length > 0) {
        //根据返回数据是否设置有效的部门列表 isActive:"1" 为有效
        let bmActiveList = []
        for (let i = 0; i < res?.data?.list?.length; i++) {
          let item = res.data.list[i]
          if (item.isActive == '1') {
            bmActiveList.push(item)
          }
        }
        this.setState({ bmList: res.data.list, bmActiveList: bmActiveList })
      }
    })
  }

  getGkglyList = () => {
    getDeptRelationsList().then((res) => {
      if (res?.data?.list?.length > 0) {
        this.setState({ gkglyList: res.data.list })
      }
    })
  }

  getKeyList = () => {
    let params = {
      date: this.props.dateId
    }
    this.context.showLoading()
    getTempNonRoutineWorkList(params)
      .then((res) => {
        if (res?.data?.list?.length > 0) {
          for (let i = 0; i < res.data.list.length; i++) {
            let obj = res.data.list[i]
            if (obj.mgmtOrgId && obj.assessContent) {
              obj.isEdit = false
            } else {
              obj.isEdit = true
            }
            if (!obj.allLineList || obj.allLineList.length == 0) {
              //初始化一行
              obj.allLineList = [
                {
                  orgIds: [],
                  score: '',
                  isSmallEdit: true
                }
              ]
              obj.hj = 0
            } else {
              for (let j = 0; j < obj.allLineList.length; j++) {
                let orgIds = []

                let lineList = obj.allLineList[j].lineList
                for (let k = 0; k < lineList.length; k++) {
                  let line = lineList[k]
                  orgIds.push(line.orgId)
                }
                obj.allLineList[j].orgIds = orgIds
                obj.allLineList[j].isSmallEdit = obj.allLineList[j].score ? false : true
              }
              //设置合计
              let count = this.calculateTotal(i, res.data.list)
              obj.hj = count
            }
          }

          this.setState({ keyList: res.data.list })
        } else {
          this.setState({ keyList: [] })
        }
        this.context.hideLoading()
      })
      .catch(() => {
        this.context.hideLoading()
      })
  }

  validateBeforeSubmit = () => {
    // 如果没有值，就弹窗提示
    if (this.state.keyList.length == 0) {
      Modal.warning({
        title: '提示',
        content: '请先填写数据！',
        zIndex: 1100
      })
      return false
    }
    for (let i = 0; i < this.state.keyList.length; i++) {
      const item = this.state.keyList[i]
      if (item.isEdit) {
        Modal.warning({
          title: '提示',
          content: `第${i + 1}行“归口管理部门和考评内容”需填写完成，并保存！`,
          zIndex: 1100
        })
        return false
      }
      for (let j = 0; j < item.allLineList.length; j++) {
        const phbmItem = item.allLineList[j]
        if (phbmItem.isSmallEdit) {
          Modal.warning({
            title: '提示',
            content: `第${i + 1}行"部门名称和得分"需填写完成，并保存！`,
            zIndex: 1100
          })
          return false
        }
      }
      if (this.calculateTotal(i, this.state.keyList) > 5) {
        return false
      }
    }
    return true
  }

  handleEditSmall = (index: number, indexSmall: number) => {
    const keyList = [...this.state.keyList]
    keyList[index].allLineList[indexSmall].isSmallEdit = true
    this.setState({ keyList })
  }

  handleDeleteSmall = (index: number, indexSmall: number) => {
    let _this = this
    Modal.confirm({
      title: '删除提示',
      content: '确定删除吗?',
      onOk: () => {
        let list = [..._this.state.keyList]
        list[index]['allLineList'].splice(indexSmall, 1)
        let data = _this.setSaveParam(list[index], index)
        _this.context.showLoading()
        //接口返回数据
        saveTempNonRoutineWork(data)
          .then((res) => {
            if (res?.data) {
              MessageSelf('删除成功！', 'success')

              //重新计算合计
              let count = _this.calculateTotal(index, list)
              list[index].hj = count
              _this.setState({ keyList: list })
            } else {
              _this.getKeyList()
            }
            _this.context.hideLoading()
          })
          .catch(() => {
            _this.context.hideLoading()
          })
      }
    })
  }

  handleSaveSmall = (index: number, indexSmall: number) => {
    const item = this.state.keyList[index].allLineList[indexSmall]
    if (!item.orgIds.length) {
      Modal.warning({
        title: '提示',
        content: '请选择部门名称！',
        zIndex: 1100
      })
      return
    }
    if (!item.score) {
      Modal.warning({
        title: '提示',
        content: '请输入得分！',
        zIndex: 1100
      })
      return
    }
    if (this.calculateTotal(index, this.state.keyList) > 5) {
      return
    }

    //调用保存接口
    let data = this.setSaveParam(this.state.keyList[index], index)
    this.context.showLoading()
    saveTempNonRoutineWork(data)
      .then((res) => {
        if (res?.data) {
          MessageSelf('保存成功！', 'success')
          let list = [...this.state.keyList]
          list[index].tnrwrId = res.data.tnrwrId
          list[index].sort = res.data.sort
          let smallList = list[index].allLineList
          for (let i = 0; i < smallList.length; i++) {
            smallList[i].rowIdx = res.data.allLineList[i]?.rowIdx
          }
          list[index].allLineList[indexSmall].isSmallEdit = false
          this.setState({ keyList: list })
        } else {
          this.getKeyList()
        }
        this.context.hideLoading()
      })
      .catch(() => {
        this.context.hideLoading()
      })
  }

  handleAddRow = (index: number) => {
    const keyList = [...this.state.keyList]
    const currentRow = keyList[index]
    if (currentRow.allLineList.some((item) => item.isSmallEdit)) {
      Modal.warning({
        title: '提示',
        content: '有正在编辑的内容，请完成后，再新增一行！',
        zIndex: 1100
      })
      return
    }
    currentRow.allLineList.push({ phbmmc: '', orgIds: [], score: '', isSmallEdit: true })
    this.setState({ keyList })
  }

  handleContentChange = (value: any, index: number, fieldName: string, indexSmall?: number) => {
    const keyList = [...this.state.keyList]
    if (fieldName === 'orgIds' && value.length > 20) {
      value = value.slice(0, 20)
      // 提醒最多选20个
      Modal.warning({
        title: '提示',
        content: '最多选择20个部门！',
        zIndex: 1100
      })
    }
    if (indexSmall !== undefined) {
      keyList[index].allLineList[indexSmall][fieldName] = value
    } else {
      keyList[index][fieldName] = value
    }
    if (['orgIds', 'score'].includes(fieldName)) {
      keyList[index].hj = this.calculateTotal(index, keyList)
    }
    this.setState({ keyList })
  }

  calculateTotal = (index: number, keyList: any[], type?: string) => {
    const total = keyList[index].allLineList.reduce(
      (sum: number, item: any) => sum + item.orgIds.length * item.score,
      0
    )
    if (total > 5) {
      if (!type) {
        Modal.warning({
          title: '提示',
          content: `第${index + 1}行，合计不能超过5！`,
          zIndex: 1100
        })
      }
    }
    return getNumber(total)
  }

  //设置保存时的参数
  setSaveParam = (obj, index) => {
    let allLineList = obj.allLineList
    let saveList = []
    for (let i = 0; i < allLineList.length; i++) {
      let item = allLineList[i]
      let lineList = []
      item.orgIds.forEach((org) => {
        lineList.push({
          orgId: org
        })
      })
      saveList.push({
        score: item.score,
        lineList: lineList
      })
    }
    let data = {
      date: this.props.dateId,
      tnrwrId: obj.tnrwrId,
      mgmtOrgId: obj.mgmtOrgId,
      repoOrgId: this.state.repoOrgId,
      assessContent: obj.assessContent,
      sort: index == 0 ? obj.sort || 1 : parseInt(this.state.keyList[index - 1].sort + 1),
      allLineList: saveList
    }
    return data
  }

  handleSaveEvaluation = (index: number, type?: string) => {
    if (!this.state.keyList[index].assessContent) {
      if (!type) {
        Modal.warning({
          title: '提示',
          content: '考评内容不能为空！',
          zIndex: 1100
        })
      }

      return
    }
    // 归口管理部门不能为空
    if (!this.state.keyList[index].mgmtOrgId) {
      if (!type) {
        Modal.warning({
          title: '提示',
          content: '归口管理部门不能为空！',
          zIndex: 1100
        })
      }

      return
    }
    if (type) {
      let smallList = this.state.keyList[index].allLineList
      for (let i = 0; i < smallList.length; i++) {
        let item = smallList[i]
        //部门名称
        if (!item.orgIds.length) {
          return
        }
        //得分
        if (!item.score) {
          return
        }
        if (this.calculateTotal(index, this.state.keyList, 'regular') > 5) {
          return
        }
      }
    }
    //调用保存接口
    let data = this.setSaveParam(this.state.keyList[index], index)
    if (!type) {
      this.context.showLoading()
    }

    saveTempNonRoutineWork(data)
      .then((res) => {
        if (res?.data) {
          if (!type) {
            MessageSelf('保存成功！', 'success')
          }

          let list = [...this.state.keyList]
          list[index].tnrwrId = res.data.tnrwrId
          list[index].sort = res.data.sort
          let smallList = list[index].allLineList
          for (let i = 0; i < smallList.length; i++) {
            smallList[i].rowIdx = res.data.allLineList[i]?.rowIdx
          }
          if (!type) {
            list[index].isEdit = false
          }

          this.setState({ keyList: list })
        } else {
          if (!type) {
            this.getKeyList()
          }
        }
      })
      .finally(() => {
        if (!type) {
          this.context.hideLoading()
        }
      })
    const keyList = [...this.state.keyList]
    if (!type) {
      keyList[index].isEdit = false
    }

    this.setState({ keyList })
  }

  handleEditEvaluation = (index: number) => {
    const keyList = [...this.state.keyList]
    keyList[index].isEdit = true
    this.setState({ keyList })
  }

  handleSubmit = () => {
    if (this.validateBeforeSubmit()) {
      Modal.confirm({
        title: '提交',
        content: '确定提交吗?',
        onOk: () => {
          this.context.showLoading()
          submitTempNonRoutineWork({ date: this.props.dateId })
            .then((res: any) => {
              if (res?.code == '0') {
                MessageSelf('提交成功！', 'success')
                this.getKeyList()
              }
              this.context.hideLoading()
            })
            .catch(() => {
              this.context.hideLoading()
            })
        }
      })
    }
  }

  handleRevoke = () => {
    Modal.confirm({
      title: '撤回',
      content: '确定撤回吗?',
      onOk: () => {
        this.context.showLoading()
        //调用撤回接口
        resetTempNonRoutineWork({ date: this.props.dateId })
          .then((res: any) => {
            if (res?.code == '0') {
              MessageSelf('撤回成功！', 'success')
              //接口返回后
              this.getKeyList()
            }
            this.context.hideLoading()
          })
          .catch(() => {
            this.context.hideLoading()
          })
      }
    })
  }

  addNewRow = () => {
    const keyList = [...this.state.keyList]
    for (let i = 0; i < keyList.length; i++) {
      let obj = keyList[i]
      if (obj.isEdit) {
        Modal.warning({
          title: '提示',
          content: '有正在编辑的内容，请完成后，再新增一行！',
          zIndex: 1100
        })
        return
      }
    }
    keyList.push({
      cacheId: getUUID(),
      mgmtOrgId: '',
      repoOrgId: this.state.repoOrgId,
      assessContent: '',
      allLineList: [{ phbmmc: '', orgIds: [], score: '', isSmallEdit: true }],
      hj: 0,
      isEdit: true,
      status: '10'
    })
    this.setState({ keyList })
  }

  handleDeleteRow = () => {
    let _this = this
    // 选中的行
    const selectedIndexes = this.state.selectedRows
    if (!selectedIndexes.length) {
      Modal.warning({
        title: '提示',
        content: '请选择要删除的行！',
        zIndex: 1100
      })
      return
    }
    Modal.confirm({
      title: '删除提示',
      content: '确定删除吗?',
      onOk: () => {
        _this.context.showLoading()
        //调用删除接口
        let ids = this.state.selectedRows.join(',')
        deleteTempNonRoutineWork(ids)
          .then((res: any) => {
            MessageSelf('删除成功！', 'success')
            //删除成功后，刷新列表
            if (res?.code == '0') {
              _this.getKeyList()
              _this.setState({ selectedRows: [] })
            }
            _this.context.hideLoading()
          })
          .catch(() => {
            _this.context.hideLoading()
          })
      }
    })
  }

  handleCheckboxChange = (id, event) => {
    const isChecked = event.target.checked
    this.setState((prevState) => {
      const newCheckboxes = [...prevState.selectedRows]
      if (isChecked) {
        newCheckboxes.push(id)
      } else {
        const indexInCheckboxes = newCheckboxes.indexOf(id)
        newCheckboxes.splice(indexInCheckboxes, 1)
      }
      return { selectedRows: newCheckboxes }
    })
  }

  //全选，全不选
  onCheckAllChange = () => {
    let selectedRows = []
    if (this.state.keyList.length > 0) {
      if (this.state.keyList.length != this.state.selectedRows.length) {
        //全选
        let list = this.state.keyList
        list.map((item) => {
          selectedRows.push(item.tnrwrId || item.cacheId)
        })
      }
    }
    this.setState({
      selectedRows: selectedRows
    })
  }

  //复选框
  handleCheckBox = (value, key) => {
    if (value) {
      //true
      let list = [...this.state.selectedRows]
      list.push(key)
      this.setState({ selectedRows: list })
    } else {
      //false
      let list = [...this.state.selectedRows]
      let index = list.indexOf(key)
      if (index > -1) {
        list.splice(index, 1)
      }
      this.setState({ selectedRows: list })
    }
  }

  //选择部门名称
  openModalDepartMent = (indexBig, indexSmall) => {
    let list = [...this.state.keyList]
    let selectedCode = list[indexBig].allLineList[indexSmall].orgIds
    this.setState({ departMent: { isDepartVisible: true, indexBig, indexSmall, selectedCode } })
  }

  //部门选择确定
  determine = (selectedList) => {
    let list = [...this.state.keyList]
    let indexBig = this.state.departMent.indexBig
    let indexSmall = this.state.departMent.indexSmall
    let bmmcCode = []
    selectedList?.map((item) => bmmcCode.push(item.id))
    list[indexBig].allLineList[indexSmall].orgIds = bmmcCode
    list[indexBig].hj = this.calculateTotal(indexBig, list)
    this.setState({ keyList: list })
  }

  //移动
  handleMove = (index, type) => {
    let list = [...this.state.keyList]
    if (type == 'up') {
      if (index == 0) {
        return
      }
      let temp = list[index]
      let temp2 = list[index - 1]
      if (!temp2?.tnrwrId || !temp?.tnrwrId) {
        return
      }
      list[index] = list[index - 1]
      list[index - 1] = temp
      this.saveSwap(temp2.tnrwrId, temp.tnrwrId)
    } else if (type == 'down') {
      if (index == list.length - 1) {
        return
      }
      let temp = list[index]
      let temp2 = list[index + 1]
      if (!temp2?.tnrwrId || !temp?.tnrwrId) {
        return
      }
      list[index] = list[index + 1]
      list[index + 1] = temp
      this.saveSwap(temp2.tnrwrId, temp.tnrwrId)
    } else if (type == 'top') {
      if (index == 0) {
        return
      }
      //置顶
      let temp = list[index]
      if (!temp?.tnrwrId) {
        return
      }
      list.splice(index, 1)
      list.unshift(temp)
      this.savePinned(temp.tnrwrId)
    } else if (type == 'bottom') {
      //暂时无置底功能
      if (index == list.length - 1) {
        return
      }
      //置底
      let temp = list[index]
      list.splice(index, 1)
      list.push(temp)
      // this.savePinned(temp.id, '2')
    }
    this.setState({ keyList: list })
  }

  saveSwap = (id1, id2) => {
    this.context.showLoading()
    pdasTempNonRoutineWorkReportSwap({ id1: id1, id2: id2 })
      .then((res) => {
        this.getKeyList()
      })
      .finally(() => {
        this.context.hideLoading()
      })
  }

  savePinned = (id) => {
    this.context.showLoading()
    pdasTempNonRoutineWorkReportPinned({ tnrwrId: id })
      .then((res) => {
        this.getKeyList()
      })
      .finally(() => {
        this.context.hideLoading()
      })
  }

  //移动小行
  handleMoveSmall = (index, indexSmall, type) => {
    let list = [...this.state.keyList]
    let listSmall = list[index].allLineList
    if (type == 'up') {
      if (indexSmall == 0) {
        return
      }
      let temp = listSmall[indexSmall]
      let temp2 = listSmall[indexSmall - 1]
      if (!list[index]?.tnrwrId || !temp?.rowIdx?.toString() || !temp2?.rowIdx?.toString()) {
        return
      }
      listSmall[indexSmall] = listSmall[indexSmall - 1]
      listSmall[indexSmall - 1] = temp
      this.saveSwapSmall(list[index].tnrwrId, temp.rowIdx, temp2.rowIdx)
    } else if (type == 'down') {
      if (indexSmall == listSmall.length - 1) {
        return
      }
      let temp = listSmall[indexSmall]
      let temp2 = listSmall[indexSmall + 1]
      if (!list[index]?.tnrwrId || !temp?.rowIdx?.toString() || !temp2?.rowIdx?.toString()) {
        return
      }
      listSmall[indexSmall] = listSmall[indexSmall + 1]
      listSmall[indexSmall + 1] = temp
      this.saveSwapSmall(list[index].tnrwrId, temp.rowIdx, temp2.rowIdx)
    } else if (type == 'top') {
      if (indexSmall == 0) {
        return
      }
      //置顶
      let temp = listSmall[indexSmall]
      if (!list[index]?.tnrwrId || !temp?.rowIdx?.toString()) {
        return
      }
      listSmall.splice(indexSmall, 1)
      listSmall.unshift(temp)
      this.savePinnedSmall(list[index].tnrwrId, temp.rowIdx)
    } else if (type == 'bottom') {
      //暂时无置底功能
      if (indexSmall == listSmall.length - 1) {
        return
      }
      //置底
      let temp = listSmall[indexSmall]
      listSmall.splice(indexSmall, 1)
      listSmall.push(temp)
    }
    this.setState({ keyList: list })
  }

  saveSwapSmall = (tnrwrId: string, rowIdx1: number, rowIdx2: number) => {
    this.context.showLoading()
    pdasTempNonRoutineWorkScoreOrgSwap({ tnrwrId, rowIdx1, rowIdx2 })
      .then((res) => {
        this.getKeyList()
      })
      .finally(() => {
        this.context.hideLoading()
      })
  }

  savePinnedSmall = (tnrwrId: string, rowIdx: number) => {
    this.context.showLoading()
    pdasTempNonRoutineWorkScoreOrgPinned({ tnrwrId, rowIdx })
      .then((res) => {
        this.getKeyList()
      })
      .finally(() => {
        this.context.hideLoading()
      })
  }

  render() {
    return (
      <div className="main-right">
        <div className="content-top" style={{ marginLeft: 'auto' }}>
          <div style={{ display: 'flex' }}>
            <div
              className={`common-button2 ${this.state.keyList.length == 0 && 'disabled-div'}`}
              onClick={this.handleSubmit}
            >
              <Iconfont type="icon-wodetijiao" style={{ fontSize: '28px' }} />
              <div className="common-text">提交</div>
            </div>
            <div
              className={`common-button2 ${this.state.keyList.length == 0 && 'disabled-div'}`}
              style={{ marginLeft: '10px' }}
              onClick={this.handleRevoke}
            >
              <Iconfont type="icon-chehui" style={{ fontSize: '20px' }} />
              <div className="common-text">撤回</div>
            </div>
          </div>
        </div>
        <div className="content-center">
          <div style={{ marginLeft: 'auto', display: 'flex' }}>
            <div className={`common-button2 common-button3 `} onClick={this.addNewRow}>
              <Iconfont type="icon-xinzeng" style={{ fontSize: '20px' }} />
              <div className="common-text2">新增一行</div>
            </div>
            <div
              className={`common-button2 common-button3 ${this.state.selectedRows.length == 0 && 'disabled-div'}`}
              onClick={this.handleDeleteRow}
              style={{ marginLeft: '10px' }}
            >
              <Iconfont type="icon-shanchu" style={{ fontSize: '20px' }} />
              <div className="common-text">删除</div>
            </div>
          </div>
          <div className="div-table">
            <table className="common-table">
              <thead>
                <tr>
                  <th>
                    {/* <Checkbox
                      onChange={() => this.onCheckAllChange()}
                      checked={
                        this.state.keyList.length > 0 && this.state.selectedRows.length == this.state.keyList.length
                          ? true
                          : false
                      }
                    ></Checkbox> */}
                  </th>
                  <th>序号</th>
                  <th>顺序调整</th>
                  <th>归口管理部门</th>
                  <th>提报部门</th>
                  <th>考评内容</th>
                  <th>部门名称</th>
                  <th>得分</th>
                  <th>合计</th>
                  <th>数据状态</th>
                  <th>驳回原因</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody>
                {this.state.keyList.map((item, index) =>
                  item.allLineList.map((phbmItem, phbmIndex) => (
                    <tr key={`${index}-${phbmIndex}`}>
                      {phbmIndex === 0 && (
                        <td style={{ width: '35px' }} rowSpan={item.allLineList.length}>
                          <Checkbox
                            disabled={item.status == '10' ? false : true}
                            checked={this.state.selectedRows.includes(item.tnrwrId || item.cacheId)}
                            onChange={(e) => this.handleCheckBox(e.target.checked, item.tnrwrId || item.cacheId)}
                          ></Checkbox>
                        </td>
                      )}
                      {phbmIndex === 0 && (
                        <td style={{ width: '45px' }} rowSpan={item.allLineList.length}>
                          {index + 1}
                        </td>
                      )}
                      {phbmIndex == 0 && (
                        <td style={{ width: '68px' }} rowSpan={item.allLineList.length}>
                          <div className="table-operate">
                            <div
                              className={`${item.status != '10' && 'disabled-table-div'}`}
                              onClick={() => this.handleMove(index, 'up')}
                            >
                              <Iconfont title="上移" type="icon-shangyimian" style={{ fontSize: '16px' }} />
                            </div>
                            <div
                              style={{ paddingLeft: '3px' }}
                              className={`${item.status != '10' && 'disabled-table-div'}`}
                              onClick={() => this.handleMove(index, 'down')}
                            >
                              <Iconfont title="下移" type="icon-xiayimian" style={{ fontSize: '16px' }} />
                            </div>
                            <div
                              style={{ paddingLeft: '3px' }}
                              className={`${item.status != '10' && 'disabled-table-div'}`}
                              onClick={() => this.handleMove(index, 'top')}
                            >
                              <Iconfont title="置顶" type="icon-zhidingmian" style={{ fontSize: '16px' }} />
                            </div>
                          </div>
                        </td>
                      )}
                      {phbmIndex === 0 && (
                        <td style={{ width: '110px' }} rowSpan={item.allLineList.length}>
                          {item.isEdit ? (
                            <Select
                              allowClear
                              value={item.mgmtOrgId}
                              placeholder="请选择"
                              style={{ width: '130px' }}
                              onChange={(value) => this.handleContentChange(value, index, 'mgmtOrgId')}
                              showSearch
                              optionFilterProp="children"
                              filterOption={(input: any, option: any) => option.children.indexOf(input) >= 0}
                            >
                              {this.state.gkglyList.map((zditem) => (
                                <Select.Option key={zditem.mgmtDep} value={zditem.mgmtDep}>
                                  {zditem.mgmtDepName}
                                </Select.Option>
                              ))}
                            </Select>
                          ) : (
                            this.state.gkglyList.find((zditem) => zditem.mgmtDep === item.mgmtOrgId)?.mgmtDepName
                          )}
                        </td>
                      )}
                      {phbmIndex === 0 && (
                        <td style={{ width: '130px' }} rowSpan={item.allLineList.length}>
                          {getOrgNames([item.repoOrgId], this.state.bmList)}
                        </td>
                      )}
                      {phbmIndex === 0 && (
                        <td style={{ width: '450px' }} rowSpan={item.allLineList.length}>
                          {item.isEdit && item.status == '10' ? (
                            <div style={{ display: 'flex', alignItems: 'center' }}>
                              <TextArea
                                autoSize
                                maxLength={1000}
                                value={item.assessContent}
                                placeholder="请填写考评内容"
                                onChange={(e) => this.handleContentChange(e.target.value, index, 'assessContent')}
                              />
                              <div className="row-opera" onClick={() => this.handleSaveEvaluation(index)}>
                                <Iconfont type="icon-icon_duihao-mian" style={{ fontSize: '16px' }} />
                                <span className="operate-text">保存</span>
                              </div>
                            </div>
                          ) : item.status == '10' ? (
                            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                              <div className="row-text">{item.assessContent}</div>
                              <div className="row-opera" onClick={() => this.handleEditEvaluation(index)}>
                                <Iconfont type="icon-bianji3" style={{ fontSize: '16px' }} />
                                <span className="operate-text">编辑</span>
                              </div>
                            </div>
                          ) : (
                            item.assessContent
                          )}
                        </td>
                      )}
                      <td style={{ width: '140px' }}>
                        {getOrgNames(phbmItem.orgIds, this.state.bmList)}
                        {phbmItem.isSmallEdit && (
                          <div
                            style={{ cursor: 'pointer', color: '#1890ff' }}
                            onClick={() => this.openModalDepartMent(index, phbmIndex)}
                          >
                            点击选择部门名称
                          </div>
                        )}
                      </td>
                      <td style={{ width: '80px' }}>
                        {phbmItem.isSmallEdit ? (
                          <InputNumber
                            style={{ width: '65px' }}
                            controls={false}
                            precision={decimalPlaces}
                            // min={0.1}
                            // max={1.3}
                            value={phbmItem.score}
                            onChange={(value) => {
                              if (!validateNumber(value, '请输入>=0.1，<=1.3的数值！', 1.3, 0.1)) return
                              this.handleContentChange(value, index, 'score', phbmIndex)
                            }}
                          />
                        ) : (
                          phbmItem.score
                        )}
                      </td>
                      {phbmIndex === 0 && (
                        <td style={{ width: '45px' }} rowSpan={item.allLineList.length}>
                          {item.hj}
                        </td>
                      )}
                      {phbmIndex === 0 && (
                        <td style={{ width: '90px' }} rowSpan={item.allLineList.length}>
                          {getStatusValue(item.status)}
                        </td>
                      )}
                      {phbmIndex === 0 && (
                        <td style={{ width: '100px' }} rowSpan={item.allLineList.length}>
                          {item.rejectReason}
                        </td>
                      )}
                      <td style={{ width: '150px' }}>
                        <div className="table-operate" style={{ justifyContent: 'left' }}>
                          {phbmItem.isSmallEdit ? (
                            <div
                              className={`${item.status != '10' && 'disabled-table-div'}`}
                              onClick={() => this.handleSaveSmall(index, phbmIndex)}
                            >
                              <Iconfont type="icon-icon_duihao-mian" style={{ fontSize: '16px' }} />
                              <span className="operate-text">保存</span>
                            </div>
                          ) : (
                            <div
                              className={`${item.status != '10' && 'disabled-table-div'}`}
                              onClick={() => this.handleEditSmall(index, phbmIndex)}
                            >
                              <Iconfont type="icon-bianji3" style={{ fontSize: '16px' }} />
                              <span className="operate-text">编辑</span>
                            </div>
                          )}
                          {phbmIndex !== 0 && (
                            <div
                              className={`${item.status != '10' && 'disabled-table-div'}`}
                              onClick={() => this.handleDeleteSmall(index, phbmIndex)}
                              style={{ paddingLeft: '10px' }}
                            >
                              <Iconfont type="icon-shanchu" style={{ fontSize: '16px' }} />
                              <span className="operate-text">删除</span>
                            </div>
                          )}
                          {phbmIndex === 0 && (
                            <div
                              className={`${item.status != '10' && 'disabled-table-div'}`}
                              onClick={() => this.handleAddRow(index)}
                              style={{ paddingLeft: '10px' }}
                            >
                              <Iconfont type="icon-xinzeng" style={{ fontSize: '16px' }} />
                              <span className="operate-text">新增一行</span>
                            </div>
                          )}
                        </div>
                        <div className="table-operate" style={{ justifyContent: 'center', paddingTop: 5 }}>
                          <div
                            className={`${item.status != '10' && 'disabled-table-div'}`}
                            onClick={() => this.handleMoveSmall(index, phbmIndex, 'up')}
                          >
                            <Iconfont title="上移" type="icon-shangyimian" style={{ fontSize: '16px' }} />
                          </div>
                          <div
                            style={{ paddingLeft: '3px' }}
                            className={`${item.status != '10' && 'disabled-table-div'}`}
                            onClick={() => this.handleMoveSmall(index, phbmIndex, 'down')}
                          >
                            <Iconfont title="下移" type="icon-xiayimian" style={{ fontSize: '16px' }} />
                          </div>
                          <div
                            style={{ paddingLeft: '3px' }}
                            className={`${item.status != '10' && 'disabled-table-div'}`}
                            onClick={() => this.handleMoveSmall(index, phbmIndex, 'top')}
                          >
                            <Iconfont title="置顶" type="icon-zhidingmian" style={{ fontSize: '16px' }} />
                          </div>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>
        {this.state.departMent.isDepartVisible && (
          <Department
            isModalVisible={this.state.departMent.isDepartVisible}
            list={this.state.bmActiveList}
            selectedCode={this.state.departMent.selectedCode}
            closeModal={() => this.setState({ departMent: { isDepartVisible: false } })}
            determine={(selectedList) => this.determine(selectedList)}
            limit={20}
          />
        )}
      </div>
    )
  }
}
