import React from 'react'
import './Modal.css'

interface ModalProps {
  children: React.ReactNode
  visible: boolean
  title: React.ReactNode
  onClose: () => void
  width?: string
  height?: string
  maxHeight?: string
}

const Modal: React.FC<ModalProps> = ({ children, visible, title, onClose, width, height, maxHeight }) => {
  if (!visible) return null

  const handleBackdropClick = (event: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
    if (event.target === event.currentTarget) {
      onClose()
    }
  }

  return (
    <div className="modal-backdrop" onClick={handleBackdropClick}>
      <div className="modal-content" style={{ width, height, maxHeight }}>
        <div className="modal-header">
          <div className="header-title">{title}</div>
          <button onClick={onClose} className="close-button">
            &times;
          </button>
        </div>
        <div className="modal-body">{children}</div>
      </div>
    </div>
  )
}

export default Modal
