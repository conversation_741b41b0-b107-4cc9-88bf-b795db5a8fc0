import React from 'react'
import LichengCompany from './lichengCompany'
import ZhangqiuCompany from './zhangqiuCompany'
import ChangqingCompany from './changqingCompany'
import PingyinCompany from './pingyinCompany'
import JiyanCompany from './jiyanCompany'
import ShangheCompany from './shangheCompany'
import { getTabByKey } from '@/utils/utils'

interface IProps {
  //选中的是哪个节点
  leafKey: string
  //全局搜索参数
  globalSearch?: any
}

export default class extends React.Component<IProps> {
  state = {
    childTabs: [
      {
        code: '01',
        value: '历城公司'
      },
      {
        code: '02',
        value: '章丘公司'
      },
      {
        code: '03',
        value: '长清公司'
      },
      {
        code: '04',
        value: '平阴公司'
      },
      {
        code: '05',
        value: '济阳公司'
      },
      {
        code: '06',
        value: '商河公司'
      }
    ],
    //已选子tab
    selectedChildTab: '01'
  }

  componentDidMount(): void {
    //获取tabType
    let tab = getTabByKey(this.props.globalSearch?.tab)
    if (tab?.id == '3') {
      this.setState({ selectedChildTab: tab?.childId })
    }
  }
  handleChildTab = (e) => {
    this.setState({ selectedChildTab: e.code })
  }
  render(): React.ReactNode {
    return (
      <div style={{ position: 'relative', height: '100%' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <div style={{ display: 'flex' }}>
            {this.state.childTabs.map((item) => (
              <div
                key={item.code}
                className={`common-tab-button common-tab-button-small ${
                  this.state.selectedChildTab == item.code && 'button-small-selected'
                }`}
                style={{ marginRight: '10px' }}
                onClick={() => this.handleChildTab(item)}
              >
                {item.value}
              </div>
            ))}
          </div>
        </div>
        <div style={{ height: 'calc(100% - 45px)' }}>
          <div style={{ height: '100%', display: this.state.selectedChildTab == '01' ? 'block' : 'none' }}>
            {/* 历城公司 */}
            <LichengCompany leafKey={this.props.leafKey} />
          </div>
          <div style={{ height: '100%', display: this.state.selectedChildTab == '02' ? 'block' : 'none' }}>
            {/* 章丘公司 */}
            <ZhangqiuCompany leafKey={this.props.leafKey} />
          </div>
          <div style={{ height: '100%', display: this.state.selectedChildTab == '03' ? 'block' : 'none' }}>
            {/* 长清公司 */}
            <ChangqingCompany leafKey={this.props.leafKey} />
          </div>
          <div style={{ height: '100%', display: this.state.selectedChildTab == '04' ? 'block' : 'none' }}>
            {/* 平阴公司 */}
            <PingyinCompany leafKey={this.props.leafKey} />
          </div>
          <div style={{ height: '100%', display: this.state.selectedChildTab == '05' ? 'block' : 'none' }}>
            {/* 济阳公司 */}
            <JiyanCompany leafKey={this.props.leafKey} />
          </div>
          <div style={{ height: '100%', display: this.state.selectedChildTab == '06' ? 'block' : 'none' }}>
            {/* 商河公司 */}
            <ShangheCompany leafKey={this.props.leafKey} />
          </div>
        </div>
      </div>
    )
  }
}
