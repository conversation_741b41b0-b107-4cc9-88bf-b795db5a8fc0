.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1050;
}

.modal-content {
  background: white;
  border-radius: 5px;
  overflow: auto;
}

.modal-content-body {
  display: flex;
  justify-content: center;
  align-items: center;
}

.modal-header {
  padding: 10px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #dee2e6;
}
.modal-header .header-title {
  font-size: 18px;
  font-weight: 600;
}

.modal-body {
  padding: 20px;
}

.close-button {
  cursor: pointer;
  border: none;
  background: none;
  font-size: 24px;
}
