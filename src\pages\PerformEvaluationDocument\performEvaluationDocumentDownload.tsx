import React from 'react'
import { Iconfont } from 'tr-cheers'
import ModalInfo from '@/components/Modal/index'
import Pagination from '@/components/pagination'
import { <PERSON><PERSON>, DatePicker, Modal } from 'antd'
import { LoadingContext } from '@/components/load/loadingProvider'
import {
  deletePerfPublish,
  downLoadEvidenceMaterial,
  getPerfPublish,
  getPerfPublishList,
  getPerfPublishRetract,
  uploadPerfPublish
} from '@/api'
import MessageSelf from '@/components/message'
import moment from 'moment'

export default class extends React.Component {
  static contextType = LoadingContext
  context: React.ContextType<typeof LoadingContext>
  state = {
    total: 0,
    pageNum: 1,
    pageSize: 10,
    keyList: []
  }

  componentDidMount(): void {
    this.getKeyList(1)
  }

  getKeyList = (pageNum: number) => {
    let params: any = {
      status: '1',
      pageNum: pageNum,
      pageSize: this.state.pageSize
    }
    this.context.showLoading()
    getPerfPublishList(params)
      .then((res) => {
        if (res?.data?.list?.length > 0) {
          this.setState({ keyList: res.data.list, total: res.data.total, pageNum })
        } else {
          this.setState({ keyList: [], total: 0, pageNum })
        }
      })
      .finally(() => {
        this.context.hideLoading()
      })
  }

  handleDownload = (pfId: string) => {
    downLoadEvidenceMaterial(pfId)
  }

  render() {
    return (
      <div className="whole" style={{ height: '100%' }}>
        <div className="div-table" style={{ height: 'calc(100% - 45px)' }}>
          <table className="common-table">
            <thead>
              <tr>
                <th>序号</th>
                <th>绩效考评周期</th>
                <th>文件</th>
                <th>发布时间</th>
                <th>数据状态</th>
              </tr>
            </thead>
            <tbody>
              {this.state.keyList.map((item, index) => {
                return (
                  <tr key={index}>
                    <td style={{ width: '45px' }}>{index + 1}</td>
                    <td>{item.evaluationDate}</td>
                    <td>
                      <a onClick={() => this.handleDownload(item.pfId)}>{item.fileName}</a>
                    </td>
                    <td>{item.publishTime}</td>
                    <td>{item.status == '1' ? '已发布' : '未发布'}</td>
                  </tr>
                )
              })}
            </tbody>
          </table>
          <Pagination
            total={this.state.total}
            pageSize={this.state.pageSize}
            pageNum={this.state.pageNum}
            onChange={this.getKeyList.bind(this)}
          />
        </div>
      </div>
    )
  }
}
