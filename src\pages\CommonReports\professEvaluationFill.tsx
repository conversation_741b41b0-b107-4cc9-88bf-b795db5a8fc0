import React from 'react'
import EvaDeparMunCompany from '@/pages/CommonReports/evaDeparMunCompany'
import EvaDeparCountyCompany from '@/pages/CommonReports/evaDeparCountyCompany'
import { getMenuSub } from '@/components/MenuSub'
import { getTabByKey } from '@/utils/utils'

interface IProps {
  //选中的是哪个节点
  leafKey: string
  //是否可见，true可见 false不可见
  isVisible?: boolean
  //权限,view是查看
  permission?: string
  //机构id
  orgId?: string
  //全局搜索参数
  globalSearch?: any
}
export default class extends React.Component<IProps> {
  evaDeparMunCompany = React.createRef<EvaDeparMunCompany>()
  evaDeparCountyCompany = null
  state = {
    childTabs: [],
    //已选子tab
    selectedChildTab: '01'
  }

  componentDidMount(): void {
    //获取tabType
    let tab = getTabByKey(this.props.globalSearch?.tab)
    if (tab?.id == '3') {
      this.setState({ selectedChildTab: tab?.childId })
    }
    this.getChildTabs()
  }

  getChildTabs = async () => {
    let childTabs = []
    this.evaDeparCountyCompany = React.createRef<EvaDeparCountyCompany>()

    if (!this.props.orgId) {
      try {
        const res = await getMenuSub()
        let permission = res?.data
        let per = permission?.find((item) => item.menuName === '县公司评价')
        childTabs = [{ code: '01', value: '市公司部门评价' }]
        per && childTabs.push({ code: '02', value: '县公司评价' })
      } catch (error) {
        childTabs = [{ code: '01', value: '市公司部门评价' }]
      }
    } else {
      // 如果是审核页面，始终显示两个选项
      childTabs = [
        { code: '01', value: '市公司部门评价' },
        { code: '02', value: '县公司评价' }
      ]
    }
    this.setState({ childTabs })
  }

  componentDidUpdate(prevProps: Readonly<IProps>, prevState: Readonly<{}>, snapshot?: any): void {}

  //提交前校验
  submitYz = () => {
    if (this.evaDeparMunCompany?.current && !this.evaDeparMunCompany.current.submitYz()) {
      return false
    }
    if (this.evaDeparCountyCompany?.current && !this.evaDeparCountyCompany.current.submitYz()) {
      return false
    }
    return true
  }

  handleChildTab = (e) => {
    this.setState({ selectedChildTab: e.code })
  }
  render(): React.ReactNode {
    return (
      <div style={{ position: 'relative', height: '100%' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <div style={{ display: 'flex', height: '30px' }}>
            {this.state.childTabs.map((item) => (
              <div
                className={`common-tab-button common-tab-button-small ${
                  this.state.selectedChildTab == item.code && 'button-small-selected'
                }`}
                style={{ marginRight: '10px' }}
                onClick={() => this.handleChildTab(item)}
                key={item.code}
              >
                {item.value}
              </div>
            ))}
          </div>
        </div>
        <div style={{ height: 'calc(100% - 35px)' }}>
          <div style={{ height: '100%', display: this.state.selectedChildTab == '01' ? 'block' : 'none' }}>
            <EvaDeparMunCompany
              ref={this.evaDeparMunCompany}
              permission={this.props.permission}
              isVisible={this.props.isVisible}
              leafKey={this.props.leafKey}
              orgId={this.props.orgId}
            />
          </div>
          <div
            style={{
              height: '100%',
              display: this.state.selectedChildTab == '02' ? 'block' : 'none'
            }}
          >
            {this.evaDeparCountyCompany && this.state.childTabs.find((item) => item.code == '02') && (
              <EvaDeparCountyCompany
                ref={this.evaDeparCountyCompany}
                permission={this.props.permission}
                isVisible={this.props.isVisible}
                leafKey={this.props.leafKey}
                orgId={this.props.orgId}
              />
            )}
          </div>
        </div>
      </div>
    )
  }
}
