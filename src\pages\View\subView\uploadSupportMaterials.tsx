import React from 'react'
import { Iconfont } from 'tr-cheers'
import { Col, Form, FormInstance, Input, Row, Select } from 'antd'
import Pagination from '@/components/pagination'
import { downLoadEvidenceMaterial, getEvidenceMaterialViewList, getOrgList } from '@/api'
import { LoadingContext } from '@/components/load/loadingProvider'
import Department from '@/pages/Common/department'
import { CloseCircleOutlined } from '@ant-design/icons'

interface IProps {
  //选中的是哪个节点
  leafKey: string
}

export default class extends React.Component<IProps> {
  static contextType = LoadingContext
  context: React.ContextType<typeof LoadingContext>

  formRef = React.createRef<FormInstance>()
  state = {
    dateId: '',
    bmList: [], // 部门列表
    //table数据
    keyList: [],
    loading: false,
    //分页数据
    total: 0,
    pageNum: 1,
    pageSize: 10,
    fileName: '',
    orgIds: [],
    departMent: {
      isDepartVisible: false, //弹出部门选择
      indexBig: '',
      indexSmall: '',
      selectedCode: []
    }
  }

  componentDidMount(): void {
    this.setState({ dateId: this.props.leafKey }, () => {
      // 获取列表数据
      this.getKeyList(1)
    })

    // 获取部门列表
    this.getBmList()
  }

  componentDidUpdate(prevProps: Readonly<IProps>, prevState: Readonly<{}>, snapshot?: any): void {
    if (prevProps.leafKey !== this.props.leafKey) {
      this.setState({ dateId: this.props.leafKey }, () => {
        // 获取列表数据
        this.getKeyList(1)
      })
    }
  }

  getBmList = () => {
    getOrgList('01,02,03').then((res: any) => {
      if (res?.data?.list?.length > 0) {
        this.setState({ bmList: res.data.list })
      }
    })
  }

  getKeyList = (pageNum: number) => {
    if (this.state.dateId) {
      this.context.showLoading()
      getEvidenceMaterialViewList({
        date: this.state.dateId,
        orgId: this.state.orgIds.join(','),
        fileName: this.state.fileName,
        pageNum: pageNum,
        pageSize: this.state.pageSize
      })
        .then((res) => {
          if (res?.data?.list?.length > 0) {
            this.setState({
              keyList: res.data.list,
              total: res.data.total,
              pageNum: res.data.pageNum
            })
          } else {
            this.setState({ keyList: [], total: 0, pageNum })
          }
          this.context.hideLoading()
        })
        .catch(() => {
          this.context.hideLoading()
        })
    }
  }

  handleDownload = (pfId: string) => {
    downLoadEvidenceMaterial(pfId)
  }

  handleSearch = () => {
    this.getKeyList(1)
  }

  handleChange = (e) => {
    this.setState({ orgIds: e })
  }

  //选择部门名称
  openModalDepartMent = () => {
    let selectedCode = this.state.orgIds
    this.setState({ departMent: { isDepartVisible: true, selectedCode } })
  }

  //部门选择确定
  determine = (selectedList) => {
    let bmmcCode = []
    selectedList?.map((item) => bmmcCode.push(item.id))
    this.setState({ orgIds: bmmcCode })
  }

  getOrgNamesById = (orgIds, list) => {
    let orgNames = []
    for (let i = 0; i < orgIds.length; i++) {
      let orgId = orgIds[i]
      let dict = list.find((item) => item.id == orgId)
      if (dict) {
        orgNames.push({ id: orgId, orgName: dict.orgName })
      }
    }
    return orgNames.map((item, index) => (
      <span key={item.id}>
        {item.orgName} <CloseCircleOutlined onClick={() => this.handleDeleteOrg(item)} />
        {index !== orgNames.length - 1 && ', '}
      </span>
    ))
  }

  handleDeleteOrg = (item) => {
    let selectedList = this.state.orgIds
    let index = selectedList.findIndex((obj) => obj === item.id)
    if (index !== -1) {
      selectedList.splice(index, 1)
    }
    this.setState({ orgIds: selectedList })
  }

  render(): React.ReactNode {
    return (
      <div style={{ height: '100%' }}>
        <div>
          <Form style={{ width: '100%' }} className="search-form" ref={this.formRef} layout="inline">
            <Row gutter={24} style={{ width: '100%' }}>
              <Col span={6}>
                <Form.Item label="文件名称" name="fileName">
                  <Input
                    placeholder="请输入文件名"
                    value={this.state.fileName}
                    onChange={(e) => this.setState({ fileName: e.target.value })}
                  />
                </Form.Item>
              </Col>
              <Col span={16}>
                <Form.Item label="上传部门" name="uploadDepartment">
                  <div style={{ width: 'calc(100% - 30px)' }}>
                    {this.getOrgNamesById(this.state.orgIds, this.state.bmList)}
                  </div>
                  <div style={{ cursor: 'pointer', color: '#1890ff' }} onClick={() => this.openModalDepartMent()}>
                    点击选择上传部门
                  </div>
                </Form.Item>
              </Col>
              <Col span={2}>
                <div className={`common-button2 common-button3 `} onClick={() => this.handleSearch()}>
                  <Iconfont type="icon-chaxun" style={{ fontSize: '20px' }} />
                  <div className="common-text">查询</div>
                </div>
              </Col>
            </Row>
          </Form>
        </div>

        <div className="div-table">
          <table className="common-table">
            <thead>
              <tr>
                <th>序号</th>
                <th>附件名称</th>
                <th>上传时间</th>
                <th>上传用户</th>
                <th>上传部门</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              {this.state.keyList.map((item, index) => {
                return (
                  <tr key={index}>
                    <td style={{ width: '45px' }}>{index + 1}</td>
                    <td>{item.fileName}</td>
                    <td>{item.createTime}</td>
                    <td>{item.userName}</td>
                    <td>{item.orgName}</td>
                    <td style={{ width: '125px' }}>
                      <div className="table-operate">
                        <div onClick={() => this.handleDownload(item.pfId)}>
                          <Iconfont type="icon-download" style={{ fontSize: '16px' }} />
                          <span className="operate-text">下载</span>
                        </div>
                      </div>
                    </td>
                  </tr>
                )
              })}
            </tbody>
          </table>
          <Pagination
            total={this.state.total}
            pageSize={this.state.pageSize}
            pageNum={this.state.pageNum}
            onChange={this.getKeyList.bind(this)}
          />

          {this.state.departMent.isDepartVisible && (
            <Department
              isModalVisible={this.state.departMent.isDepartVisible}
              list={this.state.bmList}
              selectedCode={this.state.departMent.selectedCode}
              closeModal={() => this.setState({ departMent: { isDepartVisible: false } })}
              determine={(selectedList) => this.determine(selectedList)}
            />
          )}
        </div>
      </div>
    )
  }
}
