import { getGloablSearch } from '@/api'
import { Divider, Empty, FormInstance, Select } from 'antd'
import React from 'react'
import { withRouter } from 'tr-cheers'
import { LoadingContext } from '@/components/load/loadingProvider'
import Pagination from '@/components/pagination'

@withRouter
export default class extends React.Component {
  static contextType = LoadingContext
  context: React.ContextType<typeof LoadingContext>
  formRef = React.createRef<FormInstance>()
  state = {
    keywords: '',
    keyList: [],
    //分页数据
    total: 0,
    pageNum: 1,
    pageSize: 6,
    type: '',
    typeList: [
      {
        dictKey: '内容',
        dictVal: '内容'
      },
      {
        dictKey: '附件',
        dictVal: '附件'
      }
    ],
    perfType: '',
    perfTypeList: [
      {
        dictKey: '重点绩效',
        dictVal: '重点绩效'
      },
      {
        dictKey: '临时性非常规工作',
        dictVal: '临时性非常规工作'
      },
      {
        dictKey: '业绩指标',
        dictVal: '业绩指标'
      },
      {
        dictKey: '专业评价',
        dictVal: '专业评价'
      },
      {
        dictKey: '专项考核',
        dictVal: '专项考核'
      },
      {
        dictKey: '特殊事项奖励',
        dictVal: '特殊事项奖励'
      },
      {
        dictKey: '佐证材料',
        dictVal: '佐证材料'
      }
    ]
  }
  componentDidMount(): void {
    let keywords = (this.props as any).location.search
    keywords = keywords.substring(keywords.indexOf('keywords=') + 9)
    const decodedName = decodeURIComponent(keywords)
    this.setState({ keywords: decodedName }, () => {
      this.getList(1)
    })
  }
  componentDidUpdate(prevProps: Readonly<{}>, prevState: Readonly<{}>, snapshot?: any): void {
    let keywords = (this.props as any).location.search
    keywords = keywords.substring(keywords.indexOf('keywords=') + 9)
    const decodedName = decodeURIComponent(keywords)
    if (decodedName != this.state.keywords) {
      this.setState({ keywords: decodedName }, () => {
        this.getList(1)
      })
    }
  }

  getList = (pageNum: number) => {
    this.context.showLoading()
    let params = {
      content: this.state.keywords,
      type: this.state.type,
      perfType: this.state.perfType,
      pageNum: pageNum,
      pageSize: this.state.pageSize
    }
    getGloablSearch(params)
      .then((res) => {
        if (res?.data?.list?.length > 0) {
          this.setState({ keyList: res.data.list, total: res.data.total, pageNum })
        } else {
          this.setState({ keyList: [] })
        }
      })
      .finally(() => {
        this.context.hideLoading()
      })
  }
  handleTitle = (item) => {
    ;(this.props as any).navigate(item.router, {
      state: { dateId: item.date, tab: item.tabTag }
    })
  }
  render() {
    return (
      <div className="whole" style={{ height: '100%' }}>
        <div style={{ marginTop: '10px' }}>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <div
              style={{
                paddingRight: 20,
                maxWidth: '450px',
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis'
              }}
            >
              关键字：{this.state.keywords}
            </div>
            <div style={{ paddingRight: 20 }}>
              结果类型：
              <Select
                placeholder="全部"
                allowClear
                style={{ width: '165px' }}
                onChange={(selected) => {
                  this.setState({ type: selected }, () => {
                    this.getList(1)
                  })
                }}
                showSearch
                optionFilterProp="children"
                filterOption={(input: any, option: any) => option.children.indexOf(input) >= 0}
              >
                {this.state.typeList.map((item, index) => (
                  <Select.Option key={index} value={item.dictKey}>
                    {item.dictVal}
                  </Select.Option>
                ))}
              </Select>
            </div>
            <div>
              绩效类型：
              <Select
                placeholder="全部"
                allowClear
                style={{ width: '165px' }}
                onChange={(selected) => {
                  this.setState({ perfType: selected }, () => {
                    this.getList(1)
                  })
                }}
                showSearch
                optionFilterProp="children"
                filterOption={(input: any, option: any) => option.children.indexOf(input) >= 0}
              >
                {this.state.perfTypeList.map((item, index) => (
                  <Select.Option key={index} value={item.dictKey}>
                    {item.dictVal}
                  </Select.Option>
                ))}
              </Select>
            </div>
          </div>
        </div>
        <Divider style={{ marginTop: '15px', marginBottom: '15px' }} />
        <div>
          {this.state.keyList.map((item, index) => {
            return (
              <span key={index}>
                <div className="search-result-item">
                  <div className="title" onClick={() => this.handleTitle(item)}>
                    {item.title}
                  </div>
                  <div className="desc" style={{ marginTop: 5 }}>
                    <HighlightText text={item.content} keyword={this.state.keywords} />
                  </div>
                  <div className="other" style={{ marginTop: 5 }}>
                    <div style={{ paddingRight: 30 }}>提报单位：{item.orgName}</div>
                    <div style={{ paddingRight: 30 }}>创建时间：{item.createTime}</div>
                    <div style={{ paddingRight: 30 }}>结果类型：{item.type}</div>
                    <div>绩效类型：{item.perfType}</div>
                  </div>
                </div>
                <Divider style={{ marginTop: '10px', marginBottom: '10px' }} />
              </span>
            )
          })}

          {this.state.keyList.length === 0 && (
            <div>
              <Empty />
            </div>
          )}

          <Pagination
            total={this.state.total}
            pageSize={this.state.pageSize}
            pageNum={this.state.pageNum}
            onChange={this.getList.bind(this)}
          />
        </div>
      </div>
    )
  }
}

const HighlightText = ({ text, keyword }) => {
  // Split the text into parts around the keyword
  const parts = text.split(new RegExp(`(${keyword})`, 'gi'))

  return (
    <span>
      {parts.map((part, index) =>
        part.toLowerCase() === keyword.toLowerCase() ? (
          <span key={index} className="highlight">
            {part}
          </span>
        ) : (
          part
        )
      )}
    </span>
  )
}
