import React from 'react'
import KeyPerformanceIndicators from './keyPerformanceIndicators'
import PartyBuilding from './partyBuilding'
import SafetyManagement from './safetyManagement'
import LeadershipEvaluation from './leadershipEvaluation'
import ProfessionalEvaluation from './professionalEvaluation'
import ServiceRating from './serviceRating'
import SpecialMatter from './specialMatter'
import SpecialAssessment from './specialAssessment'

import { getTabByKey } from '@/utils/utils'

interface IProps {
  //选中的是哪个节点
  leafKey: string
  //全局搜索参数
  globalSearch?: any
}

export default class extends React.Component<IProps> {
  state = {
    childTabs: [
      {
        code: '01',
        value: '关键业务指标'
      },
      {
        code: '02',
        value: '党建工作考核'
      },
      {
        code: '03',
        value: '安全管理工作考核'
      },
      {
        code: '04',
        value: '"红线"指标'
      },
      {
        code: '05',
        value: '公司领导评价'
      },
      {
        code: '06',
        value: '专业评价'
      },
      {
        code: '07',
        value: '重点工作考核'
      },
      {
        code: '08',
        value: '专项考核'
      },
      {
        code: '09',
        value: '特殊事项奖励'
      }
    ],
    //已选子tab
    selectedChildTab: '01'
  }

  componentDidMount(): void {
    //获取tabType
    let tab = getTabByKey(this.props.globalSearch?.tab)
    if (tab?.id == '3') {
      this.setState({ selectedChildTab: tab?.childId })
    }
  }
  handleChildTab = (e) => {
    this.setState({ selectedChildTab: e.code })
  }
  render(): React.ReactNode {
    return (
      <div style={{ position: 'relative', height: '100%' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <div style={{ display: 'flex' }}>
            {this.state.childTabs.map((item) => (
              <div
                key={item.code}
                className={`common-tab-button common-tab-button-small ${
                  this.state.selectedChildTab == item.code && 'button-small-selected'
                }`}
                style={{ marginRight: '10px' }}
                onClick={() => this.handleChildTab(item)}
              >
                {item.value}
              </div>
            ))}
          </div>
        </div>
        <div style={{ height: 'calc(100% - 45px)' }}>
          <div style={{ height: '100%', display: this.state.selectedChildTab == '01' ? 'block' : 'none' }}>
            {/* 关键业务指标 */}
            <KeyPerformanceIndicators leafKey={this.props.leafKey} />
          </div>
          <div style={{ height: '100%', display: this.state.selectedChildTab == '02' ? 'block' : 'none' }}>
            {/* 党建工作考核 */}
            <PartyBuilding leafKey={this.props.leafKey} />
          </div>
          <div style={{ height: '100%', display: this.state.selectedChildTab == '03' ? 'block' : 'none' }}>
            {/* 安全管理工作考核 */}
            <SafetyManagement leafKey={this.props.leafKey} />
          </div>
          <div style={{ height: '100%', display: this.state.selectedChildTab == '04' ? 'block' : 'none' }}>
            {/* "红线"指标 */}
            {/* 2024年没有"红线"指标  */}
            {this.props.leafKey.split('-')[0] === '2024' && (
              <h3 style={{ fontWeight: 700, fontSize: '14px', margin: '20px 10px' }}>
                各县公司均未发生"红线"指标扣分事项。
              </h3>
            )}
          </div>
          <div style={{ height: '100%', display: this.state.selectedChildTab == '05' ? 'block' : 'none' }}>
            {/* 公司领导评价 */}
            <LeadershipEvaluation leafKey={this.props.leafKey} />
          </div>
          <div style={{ height: '100%', display: this.state.selectedChildTab == '06' ? 'block' : 'none' }}>
            {/* 专业评价 */}
            <ProfessionalEvaluation leafKey={this.props.leafKey} />
          </div>
          <div style={{ height: '100%', display: this.state.selectedChildTab == '07' ? 'block' : 'none' }}>
            {/* 重点工作考核 */}
            <ServiceRating leafKey={this.props.leafKey} />
          </div>
          <div style={{ height: '100%', display: this.state.selectedChildTab == '08' ? 'block' : 'none' }}>
            {/* 专项考核 */}
            <SpecialAssessment leafKey={this.props.leafKey} />
          </div>
          <div style={{ height: '100%', display: this.state.selectedChildTab == '09' ? 'block' : 'none' }}>
            {/* 特殊事项奖励 */}
            <SpecialMatter leafKey={this.props.leafKey} />
          </div>
        </div>
      </div>
    )
  }
}
