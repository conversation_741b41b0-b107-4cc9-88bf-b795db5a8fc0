const isIp = true
const hostDomain = location.host.replace('www.', '')
export default class Cookie {
  static get(name: string): string {
    let cookies = document.cookie.split(';')
    for (let i = 0; i < cookies.length; i++) {
      var strs = cookies[i].split('=')
      if (strs[0].trim() === name) {
        return decodeURIComponent(strs[1])
      }
    }
    return ''
  }
  static set(name: string, value: string, days?: number) {
    var expires = ''
    if (days) {
      var d = new Date()
      d.setTime(d.getTime() + days * 24 * 60 * 60 * 1000)
      expires = 'expires=' + d.toUTCString()
    }
    document.cookie = `${name.trim()}=${encodeURIComponent(value)};${
      isIp ? '' : 'domain=' + hostDomain + ';'
    };path=/;${expires}`
  }
  static forEach(cb?: (key: string, value: string) => void) {
    let cookies = document.cookie.split(';')
    cookies.forEach((item) => {
      let arr = item.split('=')
      cb && cb(arr[0].trim(), arr[1])
    })
  }
  static clear() {
    this.forEach((key) => {
      this.set(key, '')
    })
  }
  static clearCms() {
    this.forEach((key) => {
      if (key.includes('cms-')) {
        this.set(key, '')
      }
    })
  }
  static clearDop() {
    this.forEach((key) => {
      if (!key.includes('cms-')) {
        this.set(key, '')
      }
    })
  }
}
