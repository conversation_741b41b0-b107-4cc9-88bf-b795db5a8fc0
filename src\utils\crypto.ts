import JSEncrypt from 'jsencrypt'
import SmCrypto from 'sm-crypto'

let publicKey = `
-----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCpmsv7XmmuHyg2EXYtLTDpPsUb
2Jmz932OScTQdWZRpsQ/Y6LrMJ7PYFqI9WrTfO5canfjlSKth8rdonhbeU782Niw
2LBUaNHdjXMAWF6lEyK5QJmZKDQ4XAiwl7EdUd2U3ykBJ89HExTmTm91ipEA3cTJ
NlW5JXCYSVWoNL8uLQIDAQAB
-----END PUBLIC KEY-----
`

export function stringToHex(str: string) {
  const encoder = new TextEncoder()
  const bytes = encoder.encode(str)

  // 将字节数组中的每个字节转换为对应的十六进制字符串
  const hexArray = []
  for (let i = 0; i < bytes.length; i++) {
    hexArray.push(bytes[i].toString(16))
  }
  // 将所有十六进制字符串连接起来
  return hexArray.join('')
}

let sm4key = stringToHex('N0jifMsqytegOt3v')

let sm2PriKey = '44c0986bdc2f98d9cab4f470247371097e4ceb24f4fae612285045cfd670c3f0'

let sm2PubKey =
  '048a92293b5a25dc65cba0a274d25659c02b3aa7fe2944e6edda1773c2de153eb61c6e9758125a5b79e721c3f91219af750f7d578b0e96bdad6f9da05cd9007c91'

export function Encrypt(words: string): string {
  var encryptor = new JSEncrypt()
  encryptor.setPublicKey(publicKey)
  return encryptor.encrypt(words) || ''
}

export function EncryptSm4(words: string) {
  return SmCrypto.sm4.encrypt(words, sm4key)
}

export function DecryptSm4(words: string) {
  return SmCrypto.sm4.decrypt(words, sm4key)
}

export function GetKeypair() {
  return SmCrypto.sm2.generateKeyPairHex()
}

export function EncryptSm2(word: string, pk = sm2PubKey) {
  return SmCrypto.sm2.doEncrypt(word, pk, 1)
}

export function DecryptSm2(word: string, pk = sm2PriKey) {
  return SmCrypto.sm2.doDecrypt(word, pk, 1)
}

export function EncryptSm3(word: string) {
  return SmCrypto.sm3(word)
}

export function generateRandomString(length: number) {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  const charactersLength = characters.length
  let result = ''
  const randomValues = new Uint8Array(length)
  crypto.getRandomValues(randomValues)
  for (let i = 0; i < length; i++) {
    const randomIndex = randomValues[i] % charactersLength
    result += characters.charAt(randomIndex)
  }
  return result
}
