import axios from 'axios'
import MessageSelf from '@/components/message'
import {
  EncryptSm4,
  DecryptSm4,
  EncryptSm2,
  DecryptSm2,
  GetKeypair,
  generateRandomString,
  EncryptSm3
} from '@/utils/crypto'

/** -------------------------------------------------- */
// 这里注释掉，不要打包
// let whiteApiList = [
//   '/api/v1/pdas/pdasEvidenceMaterial/fileDownload',
//   '/api/v1/pdas/commonApi/exportMonthWord',
//   '/api/v1/pdas/pdasEvidenceMaterial/upload',
//   '/api/v1/pdas/commonApi/exportMonthExcel',
//   '/api/v1/pdas/pdasPerfPublish/upload'
// ]
//接口加密后的内容，将内容放到，/public/config/apiConfig.js文件中的api中
// let enList = whiteApiList.map((item) => EncryptSm4(item))
// console.log(JSON.stringify({ api: enList }))

const exceptTokenList = ['/api/v1/ca/nonce', '/api/v1/ca/captcha/getCode', '/api/v1/ca/kr', '/api/v1/ca/session']

function toLogin() {
  window.location.hash = '#/login'
}

export async function getKeys() {
  if ((window as any).Global.nonce) {
    let keys = await axios.get('/api/v1/ca/kr')
    let keyPair = GetKeypair()
    localStorage.setItem('privateKey', keyPair.privateKey)
    localStorage.setItem('publicKey', EncryptSm2(keyPair.publicKey, keys.data))
  }
}

function getWhiteApiList() {
  let con = Global
  let api = []
  if (con?.api) {
    api = [...con.api].map((item) => DecryptSm4(item))
  }
  return api
}

//加密
function encryptRequest(config: any) {
  if (!(window as any).Global.encrypt) return
  let list = getWhiteApiList()
  // 白名单跳过
  if (list.find((item) => config.url.match(item))) return
  if (config.method == 'post' || config.method == 'put') {
    config.data = '04' + EncryptSm2(JSON.stringify(config.data))
  }
  if (config.method == 'get' || config.method == 'delete') {
    let queryParams: any = {}
    const [url, queryString] = config.url.split('?')
    config.url = url
    if (queryString) {
      const searchParams = new URLSearchParams(queryString)
      for (const [key, value] of searchParams.entries()) {
        queryParams[key] = value || ''
      }
    }
    let params = config.params || {}
    let rparams: any = {}
    for (let o in params) {
      rparams[o] = params[o] || ''
    }
    let result = { ...queryParams, ...rparams }
    if (JSON.stringify(result) != '{}') {
      config.params = '04' + EncryptSm2(JSON.stringify(result))
    }
    if (typeof config.params === 'string') {
      config.params = { 0: config.params }
    }
  }
}

//解密
function decryptRequest(response: any) {
  if (!(window as any).Global.encrypt) return
  let list = getWhiteApiList()
  // 白名单跳过
  let url = response.config.url as string
  if (list.find((item) => url.match(item))) return
  else {
    try {
      response.data = JSON.parse(DecryptSm2(response.data.slice(2)))
    } catch (error) {
      response.data = { code: '1111', msg: '非法访问' }
    }
  }
}

function download(res: any) {
  if (res.status === 200) {
    let bold = res.data
    let reader = new FileReader()
    let name = res.headers['content-disposition'].match(/fileName=.*/)[0]
    name = name.replace('fileName=', '')
    reader.readAsDataURL(bold)
    reader.onload = (e: any) => {
      let a = document.createElement('a')
      document.body.appendChild(a)
      a.download = decodeURIComponent(name)
      a.href = e.target.result
      a.click()
      document.body.removeChild(a)
    }
  } else {
    MessageSelf('下载任务出错，请联系平台', 'error')
  }
}

axios.interceptors.request.use(
  (config) => {
    encryptRequest(config)
    if (config.url?.match('/api/v1/ca/nonce')) {
      config.headers['Pb'] = localStorage.getItem('publicKey')
    }
    config.headers.Application = 'PDAS'
    config.headers.RequestSource = 'PDAS'
    config.headers['Cache-Control'] = 'no-cache'

    // 验证是否存在token，存在则附加
    if (localStorage.getItem('token') && !exceptTokenList.includes(config.url)) {
      config.headers.Authorization = localStorage.getItem('token')
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

axios.interceptors.request.use(async (config) => {
  if ((window as any).Global.nonce) {
    if (config.url !== '/api/v1/ca/nonce') {
      if (config.url == '/api/v1/ca/kr') return config
      else {
        let nonce = await axios.get('/api/v1/ca/nonce')
        if (nonce && nonce.data) {
          //nonce成功后执行
          let time = nonce.data.slice(0, 13)
          let sTime = Number(time) - new Date().getTime()
          let uid = DecryptSm2(nonce.data.slice(15), localStorage.getItem('privateKey') || '')
          localStorage.setItem('suid', uid)
          let nonce_client = generateRandomString(16)
          let timestamp = new Date().getTime() + sTime
          let sign_client = EncryptSm3(timestamp + nonce_client + uid)
          config.headers.timestamp = timestamp
          config.headers.nonceclient = nonce_client
          config.headers.signclient = sign_client
        }
      }
    }
  }

  return config
})

axios.interceptors.response.use(
  async (response) => {
    if ((window as any).Global.encrypt) {
      //加密
      decryptRequest(response)
    }
    let code = response.data.code
    let url: string = response.config.url as string
    // 下载接口
    if (response.data instanceof Blob) {
      download(response)
      return
    }
    // 错误提示
    if (code == 0) {
      return response.data
    } else if (code.length === 4 || code == '10009' || code == '15001') {
      MessageSelf(response.data.msg, 'error')
      toLogin()
      return Promise.reject(response.data.msg)
    } else {
      MessageSelf(response.data.msg, 'error')
      return Promise.reject(response.data.msg)
    }
  },
  (error) => {
    return Promise.reject(error)
  }
)
