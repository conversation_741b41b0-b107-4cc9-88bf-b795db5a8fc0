import React, { useEffect, useState } from 'react'
import { createRoot } from 'react-dom/client'
import { <PERSON>hRout<PERSON>, useRoutes } from 'react-router-dom'
import { ConfigProvider, message } from 'antd'
import { initIconfont } from 'tr-cheers'
import routers from './router'
import './axios'
import zhCN from 'antd/lib/locale/zh_CN'
import { getKeys } from './axios/index'
import 'tr-cheers/es/antd.css'
import 'tr-cheers/es/index.css'
import './theme/index.scss'
import { getStaticFile } from './utils/getStatic'
import { ShowVersion } from '../tr_version'
import { LoadingProvider } from '@/components/load/loadingProvider'
import Loading from '@/components/loading'
ShowVersion()
// 注册图标
initIconfont(require('./assets/icons/iconfont.js'))

let config = getStaticFile('./config')
// 设置全局变量
window.Global = { ...JSON.parse(config) }

function App() {
  const [loading, setLoading] = useState(true)
  const router = useRoutes(routers as any)
  useEffect(() => {
    getKeys().then((res) => setLoading(false))
  }, [])

  return loading ? <Loading /> : router
}

message.config({ maxCount: 1 })

createRoot(document.getElementById('root')).render(
  <HashRouter>
    <ConfigProvider locale={zhCN}>
      <LoadingProvider>
        <App />
      </LoadingProvider>
    </ConfigProvider>
  </HashRouter>
)
