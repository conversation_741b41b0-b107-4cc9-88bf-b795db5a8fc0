import { getKeyPerformanceDetails } from '@/api'
import React from 'react'
import { LoadingContext } from '@/components/load/loadingProvider'
import { groupByIndicator } from '@/utils/utils'
interface IProps {
  //选中的是哪个节点
  leafKey: string
}
export default class extends React.Component<IProps> {
  static contextType = LoadingContext
  context: React.ContextType<typeof LoadingContext>

  state = {
    //table数据
    keyList: [],
    dateId: ''
  }

  componentDidMount(): void {
    this.setState({ dateId: this.props.leafKey }, () => {
      // 获取列表数据
      this.getKeyList()
    })
  }

  componentDidUpdate(prevProps: Readonly<IProps>, prevState: Readonly<{}>, snapshot?: any): void {
    if (this.props.leafKey != prevProps.leafKey) {
      this.setState({ dateId: this.props.leafKey }, () => {
        // 获取列表数据
        this.getKeyList()
      })
    }
  }

  getKeyList = () => {
    if (this.state.dateId) {
      this.context.showLoading()
      // this.state.dateId.split('-')[0] 是截取年份
      getKeyPerformanceDetails({ dateYear: this.state.dateId.split('-')[0] })
        .then((res) => {
          // console.log('获取关键业绩指标', res)
          // groupByIndicator函数是处理接口返回数据，最终形成的数据格式
          //[
          // {
          //   indicatorName: "内部模拟利润",
          //     targetScore: 24.96,
          //       seqNum: 1,
          //         results: [
          //           { distributionResult: "财务部", targetScoreDetail: 6.59 },
          //           { distributionResult: "发展部", targetScoreDetail: 3.5 },
          //         ]
          // }]
          const grouped = groupByIndicator(res.data.list)
          this.setState({ keyList: grouped })
          this.context.hideLoading()
        })
        .catch(() => {
          this.context.hideLoading()
        })
    }
  }
  render(): React.ReactNode {
    // console.log('当前dateId值', this.state.dateId.split('-')[0])
    // console.log('当前keyList值', this.state.keyList)

    return (
      <div style={{ height: 'calc(100% - 10px)' }}>
        <div className="div-table" style={{ height: 'calc(100vh - 250px)', overflow: 'auto' }}>
          <table className="common-table" style={{ tableLayout: 'auto', minWidth: '100%', marginBottom: '60px' }}>
            <thead>
              <tr>
                <th>序号</th>
                <th>指标名称</th>
                <th style={{ whiteSpace: 'nowrap' }}>考核分值</th>
                <th colSpan={2}>二次分配结果</th>
              </tr>
            </thead>
            <tbody>
              {this.state.keyList.length > 0 ? (
                this.state.keyList.map((item, i) =>
                  item.results.map((result, j) => (
                    <tr key={i + '-' + j}>
                      {/* 只在每组的第一行渲染合并单元格 */}
                      {j === 0 && (
                        <>
                          <td rowSpan={item.results.length} style={{ minWidth: '50px', whiteSpace: 'nowrap' }}>
                            {item.seqNum}
                          </td>
                          <td rowSpan={item.results.length} style={{ minWidth: '100px', whiteSpace: 'nowrap' }}>
                            {item.indicatorName}
                          </td>
                          <td rowSpan={item.results.length} style={{ minWidth: '50px', whiteSpace: 'nowrap' }}>
                            {item.targetScore}
                          </td>
                        </>
                      )}
                      <td>{result.distributionResult}</td>
                      <td style={{ minWidth: '50px', whiteSpace: 'nowrap' }}>{result.targetScoreDetail}</td>
                    </tr>
                  ))
                )
              ) : (
                <tr>
                  <td colSpan={7} style={{ textAlign: 'center', padding: '20px' }}>
                    暂无数据
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    )
  }
}
