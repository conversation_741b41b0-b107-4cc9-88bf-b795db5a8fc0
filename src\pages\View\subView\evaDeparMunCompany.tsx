import { getOrgList, getProfEvalReportCityList } from '@/api'
import { getNumber, getOrgNames } from '@/utils/utils'
import { Button, Col, Form, FormInstance, Row, Select } from 'antd'
import React from 'react'
import { Iconfont } from 'tr-cheers'
import { LoadingContext } from '@/components/load/loadingProvider'

interface IProps {
  //选中的是哪个节点
  leafKey: string
}

export default class extends React.Component<IProps> {
  static contextType = LoadingContext
  context: React.ContextType<typeof LoadingContext>

  formRef = React.createRef<FormInstance>()
  state = {
    //table数据
    keyList: [],
    // 提报部门
    bmList: [],
    dateId: '',
    evalOrgId: ''
  }

  componentDidMount(): void {
    this.setState({ dateId: this.props.leafKey }, () => {
      // 获取关键字列表
      this.getKeyList()
    })
    // 获取部门列表
    this.getBmList()
  }

  componentDidUpdate(prevProps: Readonly<IProps>, prevState: Readonly<{}>, snapshot?: any): void {
    if (this.props.leafKey !== prevProps.leafKey) {
      this.setState({ dateId: this.props.leafKey }, () => {
        // 获取关键字列表
        this.getKeyList()
      })
    }
  }

  getBmList = () => {
    getOrgList('01,02').then((res: any) => {
      if (res?.data?.list?.length > 0) {
        this.setState({ bmList: res.data.list })
      }
    })
  }

  getKeyList = () => {
    if (this.state.dateId) {
      this.context.showLoading()
      getProfEvalReportCityList({ date: this.state.dateId, orgId: this.state.evalOrgId })
        .then((res) => {
          if (res?.data?.list?.length > 0) {
            let keyList = res.data.list
            keyList.map((outItem) => {
              outItem?.list.map((innerItem) => {
                innerItem?.allLineList.map((item) => {
                  let orgNames = []
                  item?.lineList.map((inner) => {
                    orgNames.push(inner?.orgName)
                  })
                  item.orgNames = orgNames.join('、')
                })
              })
            })
            this.setState({ keyList })
          } else {
            this.setState({ keyList: [] })
          }
          this.context.hideLoading()
        })
        .catch(() => {
          this.context.hideLoading()
        })
    }
  }

  handleChange = (e) => {
    this.setState({ evalOrgId: e })
  }
  handleSearch = () => {
    this.getKeyList()
  }
  render(): React.ReactNode {
    return (
      <div style={{ height: '100%' }}>
        <div style={{ margin: '15px 0' }}>
          <Form style={{ width: '100%' }} className="search-form" ref={this.formRef} layout="inline">
            <Row gutter={24} style={{ width: '100%' }}>
              <Col span={6}>
                <Form.Item label="提报部门" name="uploadDepartment">
                  <Select
                    placeholder="请选择"
                    allowClear
                    value={this.state.evalOrgId}
                    onChange={(e: any) => this.handleChange(e)}
                    showSearch
                    optionFilterProp="children"
                    filterOption={(input: any, option: any) => option.children.indexOf(input) >= 0}
                  >
                    {this.state.bmList.map((item) => (
                      <Select.Option key={item.id} value={item.id}>
                        {item.orgName}
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={2}>
                <div className={`common-button2 common-button3 `} onClick={() => this.handleSearch()}>
                  <Iconfont type="icon-chaxun" style={{ fontSize: '20px' }} />
                  <div className="common-text">查询</div>
                </div>
              </Col>
            </Row>
          </Form>
        </div>
        <div className="div-table" style={{ height: 'calc(100% - 47px)' }}>
          <table className="common-table">
            <thead>
              <tr>
                <th rowSpan={2}>序号</th>
                <th rowSpan={2}>提报部门</th>
                <th rowSpan={2}>考评项目</th>
                <th rowSpan={2}>考评内容</th>
                <th rowSpan={2}>考评依据</th>
                <th colSpan={2}>考评结果</th>
              </tr>
              <tr>
                <th>考评部门</th>
                <th>得分</th>
              </tr>
            </thead>
            <tbody>
              {this.state.keyList.map((outItem, outIndex) => {
                let rowSpan = 0
                outItem.list.map((row) => {
                  rowSpan = rowSpan + row?.allLineList?.length
                })
                return outItem.list.map((centerItem, centerIndex) => {
                  return centerItem.allLineList.map((item, index) => {
                    return (
                      <tr key={`${outIndex} + ${centerIndex} + ${index}`}>
                        {centerIndex == 0 && index == 0 && (
                          <td style={{ width: '45px' }} rowSpan={rowSpan}>
                            {outIndex + 1}
                          </td>
                        )}
                        {centerIndex == 0 && index == 0 && (
                          <td style={{ minWidth: '100px' }} rowSpan={rowSpan}>
                            {outItem.orgName}
                          </td>
                        )}
                        {index == 0 && (
                          <td style={{ minWidth: '120px' }} rowSpan={centerItem.allLineList.length}>
                            {centerItem.assessItemVal}
                          </td>
                        )}
                        {index == 0 && (
                          <td style={{ minWidth: '120px' }} rowSpan={centerItem.allLineList.length}>
                            {centerItem.assessContent}
                          </td>
                        )}
                        {index == 0 && <td rowSpan={centerItem.allLineList.length}>{centerItem.reasonVal}</td>}
                        <td style={{ minWidth: '120px' }}>{item.orgNames}</td>
                        <td style={{ minWidth: '60px' }}>{item.score}</td>
                      </tr>
                    )
                  })
                })
              })}
            </tbody>
          </table>
        </div>
      </div>
    )
  }
}
