body {
  margin: 0;
}
:root {
  --main-color1: #ebf7ff;
  --main-color2: #c2e6ff;
  --main-color3: #99d1ff;
  --main-color4: #6eb6fa;
  --main-color5: #4295ed;
  --main-color6: #1a73e0;
  --main-color7: #0d55ba;
  --main-color8: #033b94;
  --main-color9: #00266e;
  --main-color10: #001747;
  --main-color11: #1245a2;

  --gray-color1: #ffffff;
  --gray-color2: #fafafa;
  --gray-color3: #f2f2f2;
  --gray-color4: #f0f0f0;
  --gray-color5: #e7e9ec;
  --gray-color6: #999999;
  --gray-color7: #7c7c7c;
  --gray-color8: #666666;
  --gray-color9: #434343;
  --gray-color10: #333333;
  --gray-color11: #1f1f1f;
  --gray-color12: #141414;
  --gray-color13: #000000;
  --C0: var(--main-color6);
  --C1: var(--gray-color10);
  --C2: var(--gray-color8);
  --C3: var(--gray-color6);
  --C4: var(--gray-color1);
  --C5: var(--main-color1);
  --C6: var(--gray-color1);
  --C7: var(--gray-color3);
  --C8: linear-gradient(180deg, var(--main-color7), var(--main-color4));
  --C9: linear-gradient(90deg, var(--main-color7), var(--main-color4));
  --C10: var(--gray-color4);
  --C11: var(--gray-color5);
}
#root {
  height: 100%;
  width: 100%;
}
.ant-layout-sider {
  box-shadow: 0 0 10px #000000a0;
  border-radius: 10px;
}
.ant-layout-header {
  line-height: normal;
}
.ant-layout-content {
  box-shadow: 0 0 10px #000000a0;
  border-radius: 10px;
}
.ant-spin-nested-loading,
.ant-spin-container {
  height: 100%;
  width: 100%;
}

.layout {
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100vw;
}
.layout-main {
  flex: 1;
  padding: 10px 20px 20px 20px;
  height: 100%;
  box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.5);
}
.layout-body {
  height: calc(100% - 30px);
}
.header {
  width: 100%;
  height: 76px;
  background-image: url('@/assets/images/dingbupeitu.jpg');
  background-size: 100% 100%;
  display: flex;
  justify-content: space-between;
  padding: 0px 20px;
  .title {
    font-size: 32px;
    color: #fff;
    letter-spacing: 2px;
    display: flex;
    align-items: center;
  }
  .user {
    color: #fff;
    // line-height: 90px;
  }
  .user-header {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
}
.F4 {
  margin-bottom: 10px;
  display: flex;
  align-items: center;
}
.common-button {
  height: 45px;
  width: 100%;
  border-radius: 5px;
  color: #fff;
  font-size: 16px;
  text-align: center;
  line-height: 45px;
  background: linear-gradient(180deg, #1b74e2, #13519e);
  cursor: pointer;
}
.common-button2 {
  position: relative;
  height: 35px;
  width: 115px;
  border-radius: 5px;
  color: #fff;
  font-size: 16px;
  text-align: center;
  line-height: 35px;
  background: linear-gradient(180deg, #1b74e2, #13519e);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 3px 1px rgba(178, 202, 230);
  border-bottom: 1px solid rgba(255, 255, 255, 0.8);
}

.common-text {
  letter-spacing: 10px;
  margin-left: 5px;
}

.common-text2 {
  letter-spacing: 0px;
  margin-left: 5px;
  font-size: 15px;
}

.common-button3 {
  height: 30px;
  line-height: 30px;
}

.main-common {
  height: 100%;
  width: 100%;
  display: flex;
  position: relative;
  .main-left {
    flex: 0.15;
    overflow: hidden;
    margin-right: 10px;
    background-color: #e9f3ff;
    border-radius: 10px;

    // 归口审批，只选中那些没有子节点的节点，之前他们的padding-left是60
    .ant-menu-root > .ant-menu-submenu > ul > li > span {
      padding-left: 50px !important;
    }

    // 这个是能展开的父节点的整个li，里面是div（父节点，包括span和i元素箭头）和li元素，就是嵌套的
    .ant-menu-submenu-title {
      padding-left: 20px !important;

      .ant-menu-title-content {
        text-align: left;
        padding-left: 50px !important;
      }

      .ant-menu-item-icon + .ant-menu-title-content {
        padding-left: 0px !important;
      }
    }

    // 这是叶子节点
    .ant-menu-item {
      text-align: left !important;

      .ant-menu-title-content {
        padding-left: 60px !important;
      }
    }
  }
  .main-right {
    flex: 0.85;
    overflow: hidden;
    background-color: #e9f3ff;
    border-radius: 10px;
    padding: 10px 15px;
    position: relative;
    background-image: url('@/assets/images/biaogeneirongbg.png');
    background-repeat: no-repeat;
  }
}
/* 滚动条整体样式 */
::-webkit-scrollbar {
  width: 12px; /* 纵向滚动条的宽度 */
  height: 12px; /* 横向滚动条的高度 */
}

/* 滚动条轨道 */
::-webkit-scrollbar-track {
  // border-radius: 6px;
}

/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
  background-color: #a3c3f1;
  // border-radius: 6px;
}

/* 鼠标悬浮在滚动条上时 */
::-webkit-scrollbar-thumb:hover {
  background-color: #72a8f4;
}

.content-top {
  height: 50px;
  width: 100%;
  position: relative;
  display: flex;
  justify-content: end;
  &::after {
    position: absolute;
    content: '';
    display: block;
    width: 100%;
    height: 1px;
    background: #bedaf0;
    position: absolute;
    bottom: 0px;
    left: 0px;
  }
}
.content-center {
  padding-top: 10px;
  display: flex;
  flex-direction: column;
  height: calc(100% - 50px);
}

.div-table {
  margin-top: 10px;
  height: 100%;
  overflow: auto;
  position: relative;
}

.common-table {
  width: 100%;
  border-collapse: collapse;
  thead {
    position: sticky;
    top: 0px;
    z-index: 1;
    background: linear-gradient(180deg, #cbe3fd, #b5d3f9, #a3c3f1);
    width: 100%;
    color: #033393;
  }
  th,
  td {
    font-weight: normal;
    border: 1px solid #88afdd; /* 设置单元格边框样式 */
    text-align: center;
    height: 35px;
    padding: 5px;
    position: relative;
  }
  th::before {
    content: '';
    background: #88afdd;
    position: absolute;
    height: 1px;
    width: 100%;
    top: -1px;
    left: 0;
  }
  th::after {
    content: '';
    background: #88afdd;
    position: absolute;
    height: 1px;
    width: 100%;
    bottom: -1px;
    left: 0;
  }
  tbody {
    overflow: auto;
    // tr:nth-child(even) {
    //   background-color: #e3f0ff; /* 偶数行的背景颜色 */
    // }
    // tr:nth-child(odd) {
    //   background-color: #fff; /* 奇数行的背景颜色 */
    // }
    // tr:hover {
    //   background-color: #1456a7; /* 鼠标悬停时的背景颜色 */
    //   color: #fff;
    //   .table-operate {
    //     color: #fff;
    //   }
    //   .disabled-table-div {
    //     color: #fff;
    //   }
    //   .row-opera {
    //     color: #fff;
    //   }
    // }
    .table-operate {
      display: flex;
      color: #175bb0;
      justify-content: space-evenly;

      > div {
        display: flex;
        align-items: center;
        cursor: pointer;
      }
    }
    .operate-text {
      padding-left: 3px;
    }
    .row-opera {
      color: #175bb0;
      width: 75px;
      cursor: pointer;
    }
    .row-text {
      width: calc(100% - 75px);
    }
  }
  .ant-select {
    width: 100%;
  }

  .ant-select-single:not(.ant-select-customize-input) .ant-select-selector {
    height: 28px;
  }
  .ant-input {
    height: 28px;
  }
  .ant-select-multiple .ant-select-selection-item {
    background-color: #fff;
  }
  .ant-input-number {
    border: 1px solid #95bcec;
    border-radius: 5px;
  }
}

.ant-select:not(.ant-select-customize-input) .ant-select-selector {
  border: 1px solid #95bcec;
  border-radius: 5px;
}
.ant-input {
  border: 1px solid #95bcec;
  border-radius: 5px;
}

.disabled-div {
  background: #d9d9d9;
  color: #4e4e4e;
  cursor: not-allowed;
  pointer-events: none;
  position: relative;
}
.disabled-table-div {
  color: #4e4e4e;
  cursor: not-allowed;
  pointer-events: none;
}

.common-tab-button {
  position: relative;
  height: 37px;
  width: 145px;
  border-radius: 5px;
  border: 1px solid #95bcec;
  color: #033393;
  font-size: 16px;
  text-align: center;
  line-height: 35px;
  background: linear-gradient(180deg, #cee3fe, #e0f2fe);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 3px 1px rgba(178, 202, 230);
  border-bottom: 1px solid rgba(255, 255, 255, 0.8);
}
// 年度绩效数据头部按钮
.common-tab-button2 {
  position: relative;
  height: 37px;
  width: 260px;
  border-radius: 5px;
  border: 1px solid #95bcec;
  color: #033393;
  font-size: 16px;
  text-align: center;
  line-height: 35px;
  background: linear-gradient(180deg, #cee3fe, #e0f2fe);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 3px 1px rgba(178, 202, 230);
  border-bottom: 1px solid rgba(255, 255, 255, 0.8);
}

.common-tab-button-small {
  height: 30px;
  width: 140px;
  font-size: 15px;
}

.common-tab-button-selected {
  color: #fff;
  background: linear-gradient(180deg, #13519e, #1b74e2);
}

.button-small-selected {
  color: #fff;
  background: linear-gradient(180deg, #1b74e2, #13519e);
}

.search-form {
  > .ant-form-item,
  > button {
    margin-bottom: 20px !important;
  }
  > .ant-form-item {
    width: 260px;
  }
  .search-button {
    border-radius: 6px;
    background: linear-gradient(180deg, #1b74e2, #13519e);
    padding: 4px 15px;
    color: #fff;
  }
}

.whole {
  background-color: #e9f3ff;
  background-image: url('@/assets/images/biaogeneirongbg.png');
  background-repeat: no-repeat;
  border-radius: 10px;
  padding: 10px 15px;
}

.search-result-item {
  .title {
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    color: #175bb0;
  }
  .other {
    display: flex;
    align-items: center;
  }
}
/* App.css */
.highlight {
  color: red;
}

span.ant-radio + * {
  padding-right: 4px;
  padding-left: 4px;
}

.ant-radio-wrapper {
  margin-right: 1px;
}
