import React from 'react'
import { getNumber, getOrgNames } from '@/utils/utils'
import { getIndicatorAssessReportViewList } from '@/api'
import { LoadingContext } from '@/components/load/loadingProvider'

interface PropsOption {
  //选中的是哪个节点
  leafKey: string
}

export default class ComponentName extends React.Component<PropsOption> {
  static contextType = LoadingContext
  context: React.ContextType<typeof LoadingContext>

  state = {
    dateId: '', //时间
    //table数据
    keyList: []
  }

  componentDidMount() {
    this.setState({ dateId: this.props.leafKey }, () => {
      this.getKeyList()
    })
  }

  componentDidUpdate(prevProps: Readonly<PropsOption>, prevState: Readonly<{}>, snapshot?: any): void {
    if (this.props.leafKey != prevProps.leafKey) {
      this.setState({ dateId: this.props.leafKey }, () => {
        this.getKeyList()
      })
    }
  }

  getKeyList = () => {
    if (this.state.dateId) {
      this.context.showLoading()
      getIndicatorAssessReportViewList({ date: this.state.dateId })
        .then((res) => {
          if (res?.data?.list?.length > 0) {
            let list = res.data.list
            for (let i = 0; i < list.length; i++) {
              let item = list[i]
              let count = 0
              for (let j = 0; j < item?.list?.length; j++) {
                let obj = item.list[j]
                for (let k = 0; k < obj?.allLineList?.length; k++) {
                  let line = obj?.allLineList[k]
                  let orgNames = []
                  for (let m = 0; m < line?.lineList?.length; m++) {
                    orgNames.push(line.lineList[m].orgName)
                    count = count + line.lineList[m].score
                  }
                  line.orgNames = orgNames.join('、')
                }
              }
              item.hj = getNumber(count)
            }
            this.setState({ keyList: res.data.list })
          } else {
            this.setState({ keyList: [] })
          }
          this.context.hideLoading()
        })
        .catch(() => {
          this.context.hideLoading()
        })
    }
  }

  render() {
    return (
      <div style={{ height: 'calc(100% - 10px)' }}>
        <div className="div-table">
          <table className="common-table">
            <thead>
              <tr>
                <th>序号</th>
                <th>责任部门</th>
                <th>指标名称</th>
                <th>考评内容</th>
                <th>部门名称</th>
                <th>得分</th>
                <th>考评依据</th>
                <th>合计</th>
              </tr>
            </thead>
            <tbody>
              {this.state.keyList.map((outItem, outIndex) => {
                //计算rowspan
                let rowSpan = 0
                outItem.list.map((row) => {
                  rowSpan = rowSpan + row?.allLineList?.length
                })
                return outItem.list.map((centerItem, centerIndex) => {
                  return centerItem.allLineList.map((item, index) => {
                    return (
                      <tr key={`${outIndex} + ${centerIndex} + ${index}`}>
                        {centerIndex == 0 && index == 0 && (
                          <td style={{ width: '45px' }} rowSpan={rowSpan}>
                            {outIndex + 1}
                          </td>
                        )}
                        {centerIndex == 0 && index == 0 && <td rowSpan={rowSpan}>{outItem.orgName}</td>}
                        {index == 0 && <td rowSpan={centerItem.allLineList.length}>{centerItem.indicatorNameVal}</td>}
                        {index == 0 && <td rowSpan={centerItem.allLineList.length}>{centerItem.assessContent}</td>}
                        <td>{item.orgNames}</td>
                        <td>{item.score}</td>
                        <td>{item.reasonVal}</td>
                        {centerIndex == 0 && index == 0 && (
                          <td style={{ width: '45px' }} rowSpan={rowSpan}>
                            {outItem.hj}
                          </td>
                        )}
                      </tr>
                    )
                  })
                })
              })}
            </tbody>
          </table>
        </div>
      </div>
    )
  }
}
