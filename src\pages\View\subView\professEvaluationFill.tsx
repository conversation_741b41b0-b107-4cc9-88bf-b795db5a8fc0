import React from 'react'
import EvaDepar<PERSON>unCompany from './evaDeparMunCompany'
import EvaDeparCountyCompany from './evaDeparCountyCompany'
import { getTabByKey } from '@/utils/utils'

interface IProps {
  //选中的是哪个节点
  leafKey: string
  //全局搜索参数
  globalSearch?: any
}

export default class extends React.Component<IProps> {
  state = {
    childTabs: [
      {
        code: '01',
        value: '市公司部门评价'
      },
      {
        code: '02',
        value: '县公司评价'
      }
    ],
    //已选子tab
    selectedChildTab: '01'
  }

  componentDidMount(): void {
    //获取tabType
    let tab = getTabByKey(this.props.globalSearch?.tab)
    if (tab?.id == '3') {
      this.setState({ selectedChildTab: tab?.childId })
    }
  }
  handleChildTab = (e) => {
    this.setState({ selectedChildTab: e.code })
  }
  render(): React.ReactNode {
    return (
      <div style={{ position: 'relative', height: '100%' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <div style={{ display: 'flex' }}>
            {this.state.childTabs.map((item) => (
              <div
                key={item.code}
                className={`common-tab-button common-tab-button-small ${
                  this.state.selectedChildTab == item.code && 'button-small-selected'
                }`}
                style={{ marginRight: '10px' }}
                onClick={() => this.handleChildTab(item)}
              >
                {item.value}
              </div>
            ))}
          </div>
        </div>
        <div style={{ height: 'calc(100% - 45px)' }}>
          <div style={{ height: '100%', display: this.state.selectedChildTab == '01' ? 'block' : 'none' }}>
            <EvaDeparMunCompany leafKey={this.props.leafKey} />
          </div>
          <div style={{ height: '100%', display: this.state.selectedChildTab == '02' ? 'block' : 'none' }}>
            <EvaDeparCountyCompany leafKey={this.props.leafKey} />
          </div>
        </div>
      </div>
    )
  }
}
