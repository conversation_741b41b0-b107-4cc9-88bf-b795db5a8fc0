import React, { Component, RefObject } from 'react'
import './index.scss'

interface LoadingProps {
  showLoading?: () => void
  hideLoading?: () => void
}

function withLoadingIndicator<T extends LoadingProps>(WrappedComponent: React.ComponentType<T>): any {
  type PropsWithoutLoading = Omit<T, keyof LoadingProps>

  class WithLoadingIndicator extends Component<
    PropsWithoutLoading & { forwardedRef: RefObject<any> },
    { isLoading: boolean }
  > {
    state = {
      isLoading: false
    }

    showLoading = () => {
      this.setState({ isLoading: true })
    }

    hideLoading = () => {
      this.setState({ isLoading: false })
    }

    render() {
      const { forwardedRef, ...restProps } = this.props
      return (
        <>
          {this.state.isLoading && (
            <div
              style={{
                position: 'fixed',
                top: 0,
                left: 0,
                height: '100%',
                width: '100%',
                zIndex: 1200,
                background: 'rgba(240, 242, 245, 0.58)',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center'
              }}
            >
              <div className="loading-data-group">
                <div className="arc"></div>
                <h1>
                  <span>LOADING</span>
                </h1>
              </div>
            </div>
          )}
          <WrappedComponent
            {...(restProps as any)}
            ref={forwardedRef}
            showLoading={this.showLoading}
            hideLoading={this.hideLoading}
          />
        </>
      )
    }
  }

  const ForwardedComponent = React.forwardRef((props: PropsWithoutLoading, ref: RefObject<any>) => {
    return <WithLoadingIndicator {...props} forwardedRef={ref} />
  })

  return ForwardedComponent as React.ComponentType<PropsWithoutLoading>
}

export default withLoadingIndicator
