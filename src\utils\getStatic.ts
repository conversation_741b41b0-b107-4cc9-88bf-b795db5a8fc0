/**
 * 获取静态文件
 * @param path 静态文件路径
 */
export function getStaticFile(path: string): string | null {
  // 创建一个新的xhr对象
  let xhr = null
  if (window.XMLHttpRequest) {
    xhr = new XMLHttpRequest()
  } else {
    // eslint-disable-next-line
    xhr = new ActiveXObject('Microsoft.XMLHTTP')
  }
  const okStatus = document.location.protocol === 'file' ? 0 : 200
  xhr.open('GET', path, false)
  xhr.overrideMimeType('text/html;charset=utf-8')
  xhr.send(null)
  return xhr.status === okStatus ? xhr.responseText : null
}
