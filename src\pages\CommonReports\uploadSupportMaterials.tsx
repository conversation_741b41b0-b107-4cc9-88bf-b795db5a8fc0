import React from 'react'
import { Iconfont } from 'tr-cheers'
import { Modal } from 'antd'
import Pagination from '@/components/pagination'
import {
  deleteEvidenceMaterial,
  downLoadEvidenceMaterial,
  getEvidenceMaterialList,
  uploadEvidenceMaterial
} from '@/api'
import MessageSelf from '@/components/message'
import { LoadingContext } from '@/components/load/loadingProvider'

interface IProps {
  //选中的是哪个节点
  leafKey: string
  //机构id
  orgId?: string
  //view只读权限
  permission?: string
}

export default class extends React.Component<IProps> {
  static contextType = LoadingContext
  context: React.ContextType<typeof LoadingContext>

  state = {
    dateId: '',
    menuType: '',
    //table数据
    keyList: [],
    loading: false,
    //分页数据
    total: 20,
    pageNum: 1,
    pageSize: 10
  }

  componentDidMount(): void {
    if (this.props.leafKey.includes('-')) {
      //将其拆分
      let datas = this.props.leafKey.split('-')
      this.setState({ dateId: datas[0], menuType: datas[1] }, () => {
        // 获取列表数据
        this.getKeyList(1)
      })
    }
  }

  componentDidUpdate(prevProps: Readonly<IProps>, prevState: Readonly<{}>, snapshot?: any): void {
    if (prevProps.leafKey != this.props.leafKey) {
      if (this.props.leafKey.includes('-')) {
        //将其拆分
        let datas = this.props.leafKey.split('-')
        this.setState({ dateId: datas[0], menuType: datas[1] }, () => {
          //获取列表数据
          this.getKeyList(1)
        })
      }
    }
  }

  getKeyList = (pageNum: number) => {
    let params: any = {
      date: this.state.dateId,
      pageNum: pageNum,
      pageSize: this.state.pageSize,
      orgId: this.props.orgId
    }
    if (this.props.permission == 'view') {
      params = {
        ...params,
        status: '1'
      }
    }
    this.context.showLoading()
    getEvidenceMaterialList(params)
      .then((res) => {
        if (res?.data?.list?.length > 0) {
          this.setState({ keyList: res.data.list, total: res.data.total, pageNum })
        } else {
          this.setState({ keyList: [], total: 0, pageNum })
        }
        this.context.hideLoading()
      })
      .catch(() => {
        this.context.hideLoading()
      })
  }

  uploadFile = () => {
    const input = document.createElement('input')
    input.type = 'file'
    input.multiple = true
    input.style.display = 'none'

    input.addEventListener('change', (event) => {
      const files = (event.target as HTMLInputElement).files
      // 处理上传的文件列表，这里只是简单输出文件名
      for (let i = 0; i < files.length; i++) {
        if (files[i].size > 10 * 1024 * 1024) {
          Modal.warning({
            title: '文件大小超过限制',
            content: files[i].name + '文件大小超过10M',
            zIndex: 1100
          })
          return
        }
      }
      this.context.showLoading()
      //调用上传附件接口
      uploadEvidenceMaterial(this.state.dateId, files)
        .then((res: any) => {
          if (res?.code == '0') {
            MessageSelf('上传成功', 'success')
            this.getKeyList(1)
          }
          this.context.hideLoading()
        })
        .catch(() => {
          this.context.hideLoading()
        })
    })
    document.body.appendChild(input)
    input.click()
  }

  handleDownload = (pfId: string) => {
    downLoadEvidenceMaterial(pfId)
  }

  handleDelete = (pemId: string) => {
    Modal.confirm({
      title: '删除',
      content: '确定删除吗？',
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        //调用接口
        this.context.showLoading()
        deleteEvidenceMaterial(pemId)
          .then((res: any) => {
            if (res.code == '0') {
              MessageSelf('删除成功', 'success')
              this.getKeyList(1)
            }
            this.context.hideLoading()
          })
          .catch(() => {
            this.context.hideLoading()
          })
      }
    })
  }
  render(): React.ReactNode {
    return (
      <div style={{ height: '100%' }}>
        {this.props.permission != 'view' && (
          <div className="common-button2" style={{ marginLeft: 'auto' }} onClick={this.uploadFile}>
            <Iconfont type="icon-yunshangchuan" style={{ fontSize: '26px' }} />
            <div className="common-text2">上传附件</div>
          </div>
        )}

        <div className="div-table">
          <table className="common-table">
            <thead>
              <tr>
                <th>序号</th>
                <th>附件名称</th>
                <th>上传时间</th>
                <th>上传用户</th>
                <th>上传部门</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              {this.state.keyList.map((item, index) => {
                return (
                  <tr key={item.pemId}>
                    <td style={{ width: '45px' }}>{index + 1}</td>
                    <td>{item.fileName}</td>
                    <td>{item.createTime}</td>
                    <td>{item.userName}</td>
                    <td>{item.orgName}</td>
                    <td style={{ width: '125px' }}>
                      <div className="table-operate">
                        <div onClick={() => this.handleDownload(item.pfId)}>
                          <Iconfont type="icon-download" style={{ fontSize: '16px' }} />
                          <span className="operate-text">下载</span>
                        </div>
                        {this.props.permission != 'view' && (
                          <div onClick={() => this.handleDelete(item.pemId)}>
                            <Iconfont type="icon-shanchu" style={{ fontSize: '16px' }} />
                            <span className="operate-text">删除</span>
                          </div>
                        )}
                      </div>
                    </td>
                  </tr>
                )
              })}
            </tbody>
          </table>
          <Pagination
            total={this.state.total}
            pageSize={this.state.pageSize}
            pageNum={this.state.pageNum}
            onChange={this.getKeyList.bind(this)}
          />
        </div>
      </div>
    )
  }
}
