import React from 'react'
import { Iconfont } from 'tr-cheers'
import KeyPerformPlanFill from '@/pages/CommonReports/keyPerformPlanFill'
import PerformIndicatAssessFill from '@/pages/CommonReports/performIndicatAssessFill'
import ProfessEvaluationFill from '@/pages/CommonReports/professEvaluationFill'
import SpecialAssessmentFill from '@/pages/CommonReports/specialAssessmentFill'
import UploadSupportMaterials from '@/pages/CommonReports/uploadSupportMaterials'
import TemporaryUnconventional from '@/pages/CommonReports/temporaryUnconventional'
import { Empty, Modal } from 'antd'
import { getOrgList, getPrefReportStatus, passPrefReport, rejectPrefReport } from '@/api'
import MessageSelf from '@/components/message'
import { LoadingContext } from '@/components/load/loadingProvider'
import { getOrgNames, getStatusValueJx } from '@/utils/utils'

interface IProps {
  orgId: string //选择的机构id
  dateId: string
  onBack: () => void
}

export default class extends React.Component<IProps> {
  static contextType = LoadingContext
  context: React.ContextType<typeof LoadingContext>

  state = {
    leafKey: '',
    //Tab类型
    tabType: '1',
    tabs: [],
    isShow: true, // 是否显示列表
    bmList: [],
    status: {}
  }

  componentDidMount(): void {
    this.setState({ leafKey: this.props.dateId + '-' + this.props.orgId })
    //获取tabs
    this.getTabs()
    this.getBmList()
    //获取数据状态
    this.getReportStatus()
  }

  getReportStatus() {
    getPrefReportStatus({ date: this.props.dateId, orgId: this.props.orgId }).then((res: any) => {
      if (res?.code == '0') {
        this.setState({ status: res.data })
      }
    })
  }
  getBmList = () => {
    getOrgList('01,02,03').then((res) => {
      this.setState({ bmList: res.data.list })
    })
  }

  getTabs = () => {
    let tabs = [
      {
        id: '1',
        title: '重点绩效计划',
        code: 'ZDJX'
      },
      { id: '6', title: '临时性非常规工作', code: 'LSX' },
      { id: '2', title: '业绩指标考核', code: 'YJZB' },
      { id: '3', title: '专业评价', code: 'ZYPJ' },
      { id: '4', title: '专项考核', code: 'ZXKH' },
      { id: '5', title: '佐证材料' }
    ]
    this.setState({ tabs })
  }

  //切换tab
  handleTab = (e) => {
    this.setState({ tabType: e.id })
  }

  //通过
  handleApprove = () => {
    let tabName = this.state.tabs.filter((item) => item.id == this.state.tabType)[0]?.title
    let _this = this
    Modal.confirm({
      title: '通过',
      content: `确定${tabName}审批通过吗?`,
      onOk: () => {
        _this.context.showLoading()
        //调用接口
        passPrefReport({
          date: this.props.dateId,
          orgId: this.props.orgId,
          entry: this.state.tabs.filter((item) => item.id == this.state.tabType)[0]?.code
        })
          .then((res: any) => {
            if (res?.code == '0') {
              MessageSelf('审批成功', 'success')
              _this.getReportStatus()
              //执行返回页面
              // _this.props.onBack()
            }
          })
          .finally(() => {
            _this.context.hideLoading()
          })
      }
    })
  }

  //驳回
  handleReject = () => {
    let _this = this
    let tabName = this.state.tabs.filter((item) => item.id == this.state.tabType)[0]?.title
    Modal.confirm({
      title: '驳回',
      content: `确定${tabName}驳回吗?`,
      onOk: () => {
        _this.context.showLoading()
        rejectPrefReport({
          date: this.props.dateId,
          orgId: this.props.orgId,
          entry: this.state.tabs.filter((item) => item.id == this.state.tabType)[0]?.code
        })
          .then((res: any) => {
            if (res?.code == '0') {
              MessageSelf('驳回成功', 'success')
              _this.getReportStatus()
              //执行返回页面
              // _this.props.onBack()
            }
          })
          .finally(() => {
            _this.context.hideLoading()
          })
      }
    })
  }

  render() {
    return (
      <div className="main-common">
        <div className="main-right" style={{ flex: '1', padding: '5px' }}>
          <div style={{ display: 'flex' }}>
            <div
              style={{ color: '#165db5', paddingBottom: '8px', cursor: 'pointer' }}
              onClick={() => this.props.onBack()}
            >
              <Iconfont type="icon-fanhui" style={{ fontSize: '16px', paddingRight: '8px' }} />
              返回审核列表
            </div>
            <div style={{ paddingLeft: '20px' }}>部门名称： {getOrgNames([this.props.orgId], this.state.bmList)}</div>
            <div style={{ paddingLeft: '20px', color: 'red' }}>
              {this.state.tabType != '5' && (
                <>
                  {this.state.tabs.find((res) => res.id == this.state.tabType)?.title}数据状态：
                  {getStatusValueJx(
                    this.state.status[this.state.tabs.find((res) => res.id == this.state.tabType)?.code]
                  )}
                </>
              )}
            </div>
          </div>

          <div className="content-top" style={{ justifyContent: 'space-between' }}>
            <div style={{ display: 'flex' }}>
              {this.state.tabs.map((item) => (
                <div
                  className={`common-tab-button ${this.state.tabType == item.id && 'common-tab-button-selected'}`}
                  style={{ marginRight: '10px' }}
                  onClick={() => this.handleTab(item)}
                  key={item.id}
                >
                  {item.title}
                </div>
              ))}
            </div>
            {this.state.tabType != '5' && (
              <div style={{ display: 'flex' }}>
                <div
                  className={`common-button2 ${
                    this.state.status[this.state.tabs.filter((item) => item.id == this.state.tabType)[0]?.code] !=
                      '1' && 'disabled-div'
                  }`}
                  onClick={() => this.handleApprove()}
                >
                  <Iconfont type="icon-woyiyuedu" style={{ fontSize: '22px' }} />
                  <div className="common-text">通过</div>
                </div>
                <div
                  className={`common-button2 ${
                    this.state.status[this.state.tabs.filter((item) => item.id == this.state.tabType)[0]?.code] !=
                      '1' &&
                    this.state.status[this.state.tabs.filter((item) => item.id == this.state.tabType)[0]?.code] !=
                      '2' &&
                    'disabled-div'
                  }`}
                  style={{ marginLeft: '10px' }}
                  onClick={() => this.handleReject()}
                >
                  <Iconfont type="icon-chehui" style={{ fontSize: '22px' }} />
                  <div className="common-text">驳回</div>
                </div>
              </div>
            )}
          </div>
          {this.state.isShow && this.state.leafKey ? (
            <div className="content-center" style={{ height: 'calc(100% - 80px)' }}>
              <div style={{ height: '100%', display: this.state.tabType == '1' ? 'block' : 'none' }}>
                <KeyPerformPlanFill permission="view" leafKey={this.state.leafKey} orgId={this.props.orgId} />
              </div>
              <div style={{ height: '100%', display: this.state.tabType == '2' ? 'block' : 'none' }}>
                <PerformIndicatAssessFill permission="view" leafKey={this.state.leafKey} orgId={this.props.orgId} />
              </div>
              <div style={{ height: '100%', display: this.state.tabType == '3' ? 'block' : 'none' }}>
                <ProfessEvaluationFill permission="view" leafKey={this.state.leafKey} orgId={this.props.orgId} />
              </div>
              <div style={{ height: '100%', display: this.state.tabType == '4' ? 'block' : 'none' }}>
                <SpecialAssessmentFill permission="view" leafKey={this.state.leafKey} orgId={this.props.orgId} />
              </div>
              <div style={{ height: '100%', display: this.state.tabType == '5' ? 'block' : 'none' }}>
                <UploadSupportMaterials leafKey={this.state.leafKey} orgId={this.props.orgId} permission="view" />
              </div>
              <div style={{ height: '100%', display: this.state.tabType == '6' ? 'block' : 'none' }}>
                <TemporaryUnconventional leafKey={this.state.leafKey} orgId={this.props.orgId} />
              </div>
            </div>
          ) : (
            <div style={{ paddingTop: '20px' }}>
              <Empty description="暂无待审批的数据" />
            </div>
          )}
        </div>
      </div>
    )
  }
}
