import React from 'react'
import DateMenu from '@/components/DateMenu'
import { getLeafKey, getStatusValueJx, getTabBy<PERSON><PERSON> } from '@/utils/utils'
import { Iconfont } from 'tr-cheers'
import KeyPerformPlanFill from '@/pages/CommonReports/keyPerformPlanFill'
import PerformIndicatAssessFill from '@/pages/CommonReports/performIndicatAssessFill'
import ProfessEvaluationFill from '@/pages/CommonReports/professEvaluationFill'
import SpecialAssessmentFill from '@/pages/CommonReports/specialAssessmentFill'
import TemporaryAnomalyDataReporting from './temporaryAnomalyDataReporting'
import UploadSupportMaterials from '@/pages/CommonReports/uploadSupportMaterials'
import { Modal } from 'antd'
import { getDateMenuReport, getPrefReportStatus, getexportMonthExcel, saveReportRecall, saveReportSubmit } from '@/api'
import { LoadingContext } from '@/components/load/loadingProvider'
import { getMenuSub } from '@/components/MenuSub'
import { withRouter } from 'tr-cheers'
import MessageSelf from '@/components/message'

@withRouter
export default class extends React.Component {
  static contextType = LoadingContext
  context: React.ContextType<typeof LoadingContext>

  keyPerformPlanFill = React.createRef<KeyPerformPlanFill>()
  performIndicatAssessFill = React.createRef<PerformIndicatAssessFill>()
  professEvaluationFill = React.createRef<ProfessEvaluationFill>()
  specialAssessmentFill = React.createRef<SpecialAssessmentFill>()

  state = {
    dateId: '',
    leftMenu: [],
    //叶子节点
    leafKey: '',
    //Tab类型
    tabType: '1',
    tabs: [],
    status: {}, //当前状态
    showExport: false,
    //全局搜索
    globalSearch: { dateId: '', tab: '' }
  }

  componentDidMount(): void {
    //获取url参数（用于全局搜索跳转）
    let globalSearch = (this.props as any).location.state
    if (globalSearch?.dateId) {
      //获取tabType
      let tab = getTabByKey(globalSearch.tab)
      this.setState({ globalSearch, tabType: tab.id }, () => {
        //获取左侧菜单
        this.getLeftMenu()
      })
    } else {
      //获取左侧菜单
      this.getLeftMenu()
    }

    //获取tabs
    this.getTabs()
    //判断是否显示导出按钮
    this.getExport()
  }

  getExport = async () => {
    let showExport = false
    try {
      const res = await getMenuSub()
      let permission = res?.data
      let per = permission?.find((item) => item.menuType === 'TAB' && item.menuName === '导出')
      if (per) showExport = true
    } catch (error) {
      showExport = false
    }
    this.setState({ showExport: showExport })
  }

  getTabs = () => {
    let tabs = [
      {
        id: '1',
        title: '重点绩效计划填报',
        code: 'ZDJX'
      },
      { id: '2', title: '业绩指标考核填报', code: 'YJZB' },
      { id: '3', title: '专业评价填报', code: 'ZYPJ' },
      { id: '4', title: '专项考核填报', code: 'ZXKH' },
      { id: '5', title: '佐证材料上传' }
    ]
    this.setState({ tabs })
  }

  getLeftMenu() {
    this.context.showLoading()
    getDateMenuReport()
      .then((res: any) => {
        if (res?.data?.length > 0) {
          let leftMenu = res.data
          //如果有全局搜索参数，说明是从其他页面跳转过来的
          if (this.state.globalSearch?.dateId) {
            //设置全局搜索参数
            this.setState(
              {
                leftMenu: leftMenu,
                dateId: this.state.globalSearch.dateId,
                leafKey:
                  this.state.globalSearch?.dateId +
                  (this.state.globalSearch?.tab == 'temporaryUnconventional'
                    ? '-临时性非常规工作填报'
                    : '-其他绩效数据填报')
              },
              () => {
                //获取填报状态
                this.getReportStatus()
              }
            )
            return
          }

          //设置dateId和叶子节点
          let leaf = null
          for (let i = 0; i < leftMenu.length; i++) {
            if (!leaf) {
              leaf = getLeafKey(leftMenu[i], true)
            } else {
              break
            }
          }
          if (leaf.dateId.indexOf('-') > -1) {
            let node = leaf.dateId.split('-')
            this.setState({ leftMenu: leftMenu, leafKey: leaf.dateId, dateId: node[0] }, () => {
              //获取填报状态
              this.getReportStatus()
            })
          } else {
            this.setState({ leftMenu: leftMenu, leafKey: leaf.dateId, dateId: leaf.dateId })
          }
        }
      })
      .catch(() => {
        this.context.hideLoading()
      })
  }
  //获取填报状态
  getReportStatus() {
    getPrefReportStatus({ date: this.state.dateId }).then((res: any) => {
      if (res?.code == '0') {
        this.setState({ status: res.data })
      }
    })
  }

  //菜单树点击
  handleMenu = (e) => {
    ;(this.props as any).navigate('/performanceDataReporting', {
      state: null
    })
    let key = e.key.split('-')
    this.setState({ dateId: key[0], leafKey: e.key, globalSearch: { dateId: '', tab: '' } }, () => {
      //获取填报状态
      this.getReportStatus()
    })
  }

  //提交
  handleSubmit = () => {
    let tabType = this.state.tabType
    let tabName = this.state.tabs.filter((item) => item.id == tabType)[0]?.title?.replace('填报', '')
    if (tabType == '1') {
      //校验重点绩效计划填报是否完成
      let keyFlag = this.keyPerformPlanFill?.current?.submitYz()
      if (!keyFlag) {
        return
      }
    }

    if (tabType == '2') {
      //业绩指标考核填报
      let yjFlag = this.performIndicatAssessFill?.current?.submitYz()
      if (!yjFlag) {
        return
      }
    }
    if (tabType == '3') {
      //专业评价填报
      let zypjFlag = this.professEvaluationFill?.current?.submitYz()
      if (!zypjFlag) {
        return
      }
    }

    if (tabType == '4') {
      //专项考核填报
      let zxFlag = this.specialAssessmentFill?.current?.submitYz()
      if (!zxFlag) {
        return
      }
    }

    let _this = this
    Modal.confirm({
      title: '提交',
      content: `确定提交${tabName}数据吗?`,
      onOk: () => {
        _this.context.showLoading()
        //调用提交接口
        saveReportSubmit({
          date: this.state.dateId,
          entry: this.state.tabs.filter((item) => item.id == tabType)[0]?.code
        })
          .then((res: any) => {
            if (res?.code == '0') {
              MessageSelf('提交成功', 'success')
              _this.getReportStatus()
            }
          })
          .finally(() => {
            _this.context.hideLoading()
          })
      }
    })
  }

  //撤回
  handleRevoke = () => {
    let _this = this
    let tabType = this.state.tabType
    let tabName = this.state.tabs.filter((item) => item.id == tabType)[0]?.title?.replace('填报', '')
    Modal.confirm({
      title: '撤回',
      content: `确定撤回${tabName}数据吗?`,
      onOk: () => {
        _this.context.showLoading()
        //调用撤回接口
        saveReportRecall({
          date: this.state.dateId,
          entry: this.state.tabs.filter((item) => item.id == tabType)[0]?.code
        })
          .then((res: any) => {
            if (res?.code == '0') {
              MessageSelf('撤回成功', 'success')
              _this.getReportStatus()
            }
          })
          .finally(() => {
            _this.context.hideLoading()
          })
      }
    })
  }

  //切换tab
  handleTab = (e) => {
    this.setState({ tabType: e.id })
  }

  //导出
  handleExport = () => {
    Modal.confirm({
      title: '导出',
      content: '是否导出月度绩效数据？',
      onOk: () => {
        this.context.showLoading()
        getexportMonthExcel({ date: this.state.dateId })
          .then((res) => {
            this.context.hideLoading()
          })
          .catch(() => {
            this.context.hideLoading()
          })
      }
    })
  }

  render() {
    return (
      <div className="main-common">
        <div className="main-left">
          {this.state.dateId && (
            <DateMenu
              menuList={this.state.leftMenu}
              dateId={this.state.dateId}
              leafKey={this.state.leafKey}
              onClick={(e) => this.handleMenu(e)}
            />
          )}
        </div>
        {this.state.leafKey && this.state.leafKey.split('-')[1] == '其他绩效数据填报' && (
          <div className="main-right">
            <div style={{ color: 'red' }}>
              {this.state.tabType != '5' ? (
                <>
                  {this.state.tabs.find((res) => res.id == this.state.tabType)?.title?.replace('填报', '')}数据状态：
                  {getStatusValueJx(
                    this.state.status[this.state.tabs.find((res) => res.id == this.state.tabType)?.code]
                  )}
                </>
              ) : (
                <span>&nbsp;</span>
              )}
            </div>
            <div className="content-top" style={{ justifyContent: 'space-between' }}>
              <div style={{ display: 'flex' }}>
                {this.state.tabs.map((item) => (
                  <div
                    className={`common-tab-button ${this.state.tabType == item.id && 'common-tab-button-selected'}`}
                    style={{ marginRight: '10px' }}
                    onClick={() => this.handleTab(item)}
                    key={item.id}
                  >
                    {item.title}
                  </div>
                ))}
              </div>
              <div style={{ display: 'flex' }}>
                {this.state.tabType != '5' && (
                  <>
                    <div
                      className={`common-button2 ${
                        (this.state.status[this.state.tabs.filter((item) => item.id == this.state.tabType)[0]?.code] ==
                          '1' ||
                          this.state.status[this.state.tabs.filter((item) => item.id == this.state.tabType)[0]?.code] ==
                            '2') &&
                        'disabled-div'
                      }`}
                      onClick={() => this.handleSubmit()}
                    >
                      <Iconfont type="icon-wodetijiao" style={{ fontSize: '28px' }} />
                      <div className="common-text">提交</div>
                    </div>
                    <div
                      className={`common-button2 ${
                        this.state.status[this.state.tabs.filter((item) => item.id == this.state.tabType)[0]?.code] !=
                          '1' && 'disabled-div'
                      }`}
                      style={{ marginLeft: '10px' }}
                      onClick={() => this.handleRevoke()}
                    >
                      <Iconfont type="icon-chehui" style={{ fontSize: '20px' }} />
                      <div className="common-text">撤回</div>
                    </div>
                  </>
                )}
                {this.state.showExport && (
                  <div className={`common-button2`} style={{ marginLeft: '10px' }} onClick={() => this.handleExport()}>
                    <Iconfont type="icon-download" style={{ fontSize: '20px' }} />
                    <div className="common-text">导出</div>
                  </div>
                )}
              </div>
            </div>
            <div className="content-center">
              <div style={{ height: '100%', display: this.state.tabType == '1' ? 'block' : 'none' }}>
                <KeyPerformPlanFill
                  ref={this.keyPerformPlanFill}
                  isVisible={this.state.status['ZDJX'] == '0' || this.state.status['ZDJX'] == '-1'}
                  leafKey={this.state.leafKey}
                />
              </div>
              <div style={{ height: '100%', display: this.state.tabType == '2' ? 'block' : 'none' }}>
                <PerformIndicatAssessFill
                  ref={this.performIndicatAssessFill}
                  leafKey={this.state.leafKey}
                  isVisible={this.state.status['YJZB'] == '0' || this.state.status['YJZB'] == '-1'}
                />
              </div>
              <div style={{ height: '100%', display: this.state.tabType == '3' ? 'block' : 'none' }}>
                <ProfessEvaluationFill
                  ref={this.professEvaluationFill}
                  leafKey={this.state.leafKey}
                  isVisible={this.state.status['ZYPJ'] == '0' || this.state.status['ZYPJ'] == '-1'}
                  globalSearch={this.state.globalSearch}
                />
              </div>
              <div style={{ height: '100%', display: this.state.tabType == '4' ? 'block' : 'none' }}>
                <SpecialAssessmentFill
                  ref={this.specialAssessmentFill}
                  leafKey={this.state.leafKey}
                  isVisible={this.state.status['ZXKH'] == '0' || this.state.status['ZXKH'] == '-1'}
                  globalSearch={this.state.globalSearch}
                />
              </div>
              <div style={{ height: '100%', display: this.state.tabType == '5' ? 'block' : 'none' }}>
                <UploadSupportMaterials leafKey={this.state.leafKey} />
              </div>
            </div>
          </div>
        )}
        {this.state.leafKey && this.state.leafKey.split('-')[1] == '临时性非常规工作填报' && (
          <TemporaryAnomalyDataReporting dateId={this.state.leafKey.split('-')[0]} />
        )}
      </div>
    )
  }
}
