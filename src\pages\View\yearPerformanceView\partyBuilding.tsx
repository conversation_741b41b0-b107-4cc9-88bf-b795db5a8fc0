import { getPartyBuildingDetails } from '@/api'
import React from 'react'
import { LoadingContext } from '@/components/load/loadingProvider'

interface IProps {
  //选中的是哪个节点
  leafKey: string
}

// 定义表格数据类型
interface TableDataItem {
  evaluationDepartment: string
  indicatorName: string
  score: number
  licheng: number
  zhangqiu: number
  changqing: number
  pingyin: number
  jiyang: number
  shanghe: number
}

export default class extends React.Component<IProps> {
  static contextType = LoadingContext
  context: React.ContextType<typeof LoadingContext>

  state = {
    //table数据
    tableData: [] as TableDataItem[],
    dateId: ''
  }

  componentDidMount(): void {
    this.setState({ dateId: this.props.leafKey }, () => {
      // 获取列表数据
      this.getKeyList()
    })
  }

  componentDidUpdate(prevProps: Readonly<IProps>, prevState: Readonly<{}>, snapshot?: any): void {
    if (this.props.leafKey != prevProps.leafKey) {
      this.setState({ dateId: this.props.leafKey }, () => {
        // 获取列表数据
        this.getKeyList()
      })
    }
  }

  getKeyList = () => {
    if (this.state.dateId) {
      this.context.showLoading()
      // this.state.dateId.split('-')[0] 是截取年份
      getPartyBuildingDetails({ dateYear: this.state.dateId.split('-')[0] })
        .then((res) => {
          // console.log('获取县公司党建工作考核得分情况表数据', res)
          this.setState({ tableData: res.data.list })
          this.context.hideLoading()
        })
        .catch(() => {
          this.context.hideLoading()
        })
    }
  }

  // 判断是否为合计或标准总分行
  isSummaryRow = (item: TableDataItem): boolean => {
    return item.evaluationDepartment === '合计' || item.evaluationDepartment === '标准总分'
  }

  render(): React.ReactNode {
    const { tableData } = this.state

    return (
      <div style={{ height: 'calc(100% - 10px)' }}>
        <div className="div-table" style={{ height: 'calc(100vh - 250px)', overflow: 'auto' }}>
          <table className="common-table" style={{ tableLayout: 'auto', minWidth: '100%', marginBottom: '55px' }}>
            <thead>
              <tr>
                <th>评价部门</th>
                <th>指标名称</th>
                <th>分值</th>
                <th>历城</th>
                <th>章丘</th>
                <th>长清</th>
                <th>平阴</th>
                <th>济阳</th>
                <th>商河</th>
              </tr>
            </thead>
            <tbody>
              {/* 动态渲染数据行 */}
              {tableData.length === 0 ? (
                <tr>
                  <td colSpan={9} style={{ textAlign: 'center', padding: '20px' }}>
                    暂无数据
                  </td>
                </tr>
              ) : (
                tableData.map((item, index) => {
                  const isSummary = this.isSummaryRow(item)
                  return (
                    <tr key={index}>
                      {isSummary ? (
                        // 合计或标准总分行：合并评价部门和指标名称列
                        <td colSpan={2}>{item.evaluationDepartment === '合计' ? '合计' : '标准总分'}</td>
                      ) : (
                        // 普通行：正常显示评价部门和指标名称
                        <>
                          <td>{item.evaluationDepartment}</td>
                          <td>{item.indicatorName}</td>
                        </>
                      )}
                      <td>{item.score}</td>
                      <td>{item.licheng}</td>
                      <td>{item.zhangqiu}</td>
                      <td>{item.changqing}</td>
                      <td>{item.pingyin}</td>
                      <td>{item.jiyang}</td>
                      <td>{item.shanghe}</td>
                    </tr>
                  )
                })
              )}
            </tbody>
          </table>
        </div>
      </div>
    )
  }
}
