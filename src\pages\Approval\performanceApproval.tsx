import React from 'react'
import DateMenu from '@/components/DateMenu'
import { getLeafKey, getStatusValueSh, getStatusValueTj } from '@/utils/utils'
import { Iconfont } from 'tr-cheers'
import KeyPerformPlanFill from '@/pages/CommonReports/keyPerformPlanFill'
import PerformIndicatAssessFill from '@/pages/CommonReports/performIndicatAssessFill'
import ProfessEvaluationFill from '@/pages/CommonReports/professEvaluationFill'
import SpecialAssessmentFill from '@/pages/CommonReports/specialAssessmentFill'
import { getMenuDate, getPrefReportAuditStatusList, prefReportAuditListReject } from '@/api'
import { LoadingContext } from '@/components/load/loadingProvider'
import PerformanceApprovalInfo from './performanceApprovalInfo'
import { Modal } from 'antd'
import MessageSelf from '@/components/message'

export default class extends React.Component {
  static contextType = LoadingContext
  context: React.ContextType<typeof LoadingContext>

  keyPerformPlanFill = React.createRef<KeyPerformPlanFill>()
  performIndicatAssessFill = React.createRef<PerformIndicatAssessFill>()
  professEvaluationFill = React.createRef<ProfessEvaluationFill>()
  specialAssessmentFill = React.createRef<SpecialAssessmentFill>()
  state = {
    dateId: '',
    leftMenu: [],
    //叶子节点
    leafKey: '',
    dataList: [],
    orgId: '', //选择的机构id
    isShowInfo: false //是否显示审批内容页面
  }

  componentDidMount(): void {
    //获取左侧菜单
    this.getLeftMenu()
  }

  getLeftMenu() {
    this.context.showLoading()
    getMenuDate()
      .then((res) => {
        if (res?.data?.length > 0) {
          let leftMenu = res.data
          //设置dateId和叶子节点
          let leaf = null
          for (let i = 0; i < leftMenu.length; i++) {
            if (!leaf) {
              leaf = getLeafKey(leftMenu[i], true)
            } else {
              break
            }
          }
          this.context.hideLoading()
          this.setState({ leftMenu: leftMenu, leafKey: leaf.dateId, dateId: leaf.dateId }, () => {
            //获取表格内容
            this.getTableList()
          })
        }
      })
      .catch(() => {
        this.context.hideLoading()
      })
  }

  //菜单树点击
  handleMenu = (e) => {
    this.setState({ dateId: e.key, leafKey: e.key, isShowInfo: false }, () => {
      this.getTableList()
    })
  }

  getTableList = () => {
    let params = {
      date: this.state.dateId
    }
    this.context.showLoading()
    // 获取表格内容
    getPrefReportAuditStatusList(params)
      .then((res: any) => {
        if (res?.data?.list?.length > 0) {
          this.setState({ dataList: res.data.list })
        } else {
          this.setState({ dataList: [] })
        }
        this.context.hideLoading()
      })
      .catch(() => {
        this.context.hideLoading()
      })
  }

  //进入审批页面
  handleApproval = (item) => {
    this.setState({ isShowInfo: true, orgId: item.orgId })
  }

  closeInfo = () => {
    this.getTableList()
    this.setState({ isShowInfo: false })
  }

  //驳回
  handleReject = (item) => {
    let _this = this
    Modal.confirm({
      title: '驳回',
      content: '是否驳回' + item.orgName + '的绩效数据？',
      onOk: () => {
        _this.context.showLoading()
        prefReportAuditListReject({ date: _this.state.dateId, orgId: item.orgId })
          .then((res: any) => {
            if (res?.code == '0') {
              MessageSelf('驳回成功', 'success')
              //刷新页面
              _this.getTableList()
            }
          })
          .finally(() => {
            _this.context.hideLoading()
          })
      }
    })
  }
  render() {
    return (
      <div className="main-common">
        <div className="main-left">
          {this.state.dateId && (
            <DateMenu
              menuList={this.state.leftMenu}
              dateId={this.state.dateId}
              leafKey={this.state.leafKey}
              onClick={(e) => this.handleMenu(e)}
            />
          )}
        </div>
        <div className="main-right" style={{ padding: '20px' }}>
          <div className="div-table" style={{ marginTop: '0px' }}>
            {!this.state.isShowInfo && (
              <table className="common-table">
                <thead>
                  <tr>
                    <th rowSpan={2}>序号</th>
                    <th rowSpan={2}>部门名称</th>
                    <th rowSpan={2}>重点绩效计划</th>
                    <th rowSpan={2}>临时性非常规工作</th>
                    <th rowSpan={2}>业绩指标考核</th>
                    <th colSpan={2}>专业评价</th>
                    <th colSpan={2}>专项考核</th>
                    <th colSpan={5}>审批状态</th>
                    <th rowSpan={2}>操作</th>
                  </tr>
                  <tr>
                    <th>公司部门评价</th>
                    <th>县公司评价</th>
                    <th>得分考核</th>
                    <th>现金奖惩</th>
                    <th>重点绩效计划</th>
                    <th>临时性非常规工作</th>
                    <th>业绩指标考核</th>
                    <th>专业评价</th>
                    <th>专项考核</th>
                  </tr>
                </thead>
                <tbody>
                  {this.state.dataList.map((item, index) => (
                    <tr key={index}>
                      <td style={{ width: '40px' }}>{index + 1}</td>
                      <td>
                        {item.zdjxSubmit == '1' ||
                        item.lsxSubmit == '1' ||
                        item.yjzbSubmit == '1' ||
                        item.zypjsgsSubmit == '1' ||
                        item.zypjxgsSubmit == '1' ||
                        item.zxdfSubmit == '1' ||
                        item.zxxjSubmit == '1' ? (
                          <span style={{ color: 'red' }}>{item.orgName}</span>
                        ) : (
                          item.orgName
                        )}
                      </td>
                      <td>
                        {item.zdjxSubmit == '1' ? (
                          <span style={{ color: 'red' }}>已提交</span>
                        ) : (
                          getStatusValueTj(item.zdjxSubmit)
                        )}
                      </td>
                      <td>
                        {item.lsxSubmit == '1' ? (
                          <span style={{ color: 'red' }}>已提交</span>
                        ) : (
                          getStatusValueTj(item.lsxSubmit)
                        )}
                      </td>
                      <td>
                        {item.yjzbSubmit == '1' ? (
                          <span style={{ color: 'red' }}>已提交</span>
                        ) : (
                          getStatusValueTj(item.yjzbSubmit)
                        )}
                      </td>
                      <td>
                        {item.zypjsgsSubmit == '1' ? (
                          <span style={{ color: 'red' }}>已提交</span>
                        ) : (
                          getStatusValueTj(item.zypjsgsSubmit)
                        )}
                      </td>
                      <td>
                        {item.zypjxgsSubmit == '1' ? (
                          <span style={{ color: 'red' }}>已提交</span>
                        ) : (
                          getStatusValueTj(item.zypjxgsSubmit)
                        )}
                      </td>
                      <td>
                        {item.zxdfSubmit == '1' ? (
                          <span style={{ color: 'red' }}>已提交</span>
                        ) : (
                          getStatusValueTj(item.zxdfSubmit)
                        )}
                      </td>
                      <td>
                        {item.zxxjSubmit == '1' ? (
                          <span style={{ color: 'red' }}>已提交</span>
                        ) : (
                          getStatusValueTj(item.zxxjSubmit)
                        )}
                      </td>
                      <td>
                        {getStatusValueSh(item.zdjxAudit) == '已审核' ? (
                          <span style={{ color: 'green' }}>已审核</span>
                        ) : (
                          getStatusValueSh(item.zdjxAudit)
                        )}
                      </td>
                      <td>
                        {getStatusValueSh(item.lsxAudit) == '已审核' ? (
                          <span style={{ color: 'green' }}>已审核</span>
                        ) : (
                          getStatusValueSh(item.lsxAudit)
                        )}
                      </td>
                      <td>
                        {getStatusValueSh(item.yjzbAudit) == '已审核' ? (
                          <span style={{ color: 'green' }}>已审核</span>
                        ) : (
                          getStatusValueSh(item.yjzbAudit)
                        )}
                      </td>
                      <td>
                        {getStatusValueSh(item.zypjAudit) == '已审核' ? (
                          <span style={{ color: 'green' }}>已审核</span>
                        ) : (
                          getStatusValueSh(item.zypjAudit)
                        )}
                      </td>
                      <td>
                        {getStatusValueSh(item.zxkhAudit) == '已审核' ? (
                          <span style={{ color: 'green' }}>已审核</span>
                        ) : (
                          getStatusValueSh(item.zxkhAudit)
                        )}
                      </td>
                      <td>
                        <div className="table-operate">
                          <div onClick={() => this.handleApproval(item)}>
                            <Iconfont type="icon-fapiaoshenhe" style={{ fontSize: '16px' }} />
                            <span className="operate-text">审批</span>
                          </div>
                          {/* 2024.10.10 由于按照模块分开审核，所以将统一的驳回去掉 */}
                          {/* <div onClick={() => this.handleReject(item)}>
                            <Iconfont type="icon-bohui" style={{ fontSize: '16px' }} />
                            <span className="operate-text">驳回</span>
                          </div> */}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            )}
            {this.state.isShowInfo && (
              <PerformanceApprovalInfo
                orgId={this.state.orgId}
                dateId={this.state.dateId}
                onBack={() => this.closeInfo()}
              />
            )}
          </div>
        </div>
      </div>
    )
  }
}
