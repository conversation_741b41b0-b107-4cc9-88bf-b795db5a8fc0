import React from 'react'
import { getNumber, getUUID, decimalPlaces, debounce, validateNumber } from '@/utils/utils'
import { Iconfont } from 'tr-cheers'
import { Select, Input, InputNumber, Modal, Checkbox, Button } from 'antd'
import ModalInfo from '@/components/Modal/index'
import MessageSelf from '@/components/message'
import { LoadingContext } from '@/components/load/loadingProvider'
import Department from '@/pages/Common/department'

import {
  deleteCity,
  getOrgList,
  getCityList,
  getDictListByName,
  saveCity,
  pdasProfEvalReportCitySwap,
  pdasProfEvalReportCityPinned,
  pdasProfEvalReportCityOrgSwap,
  pdasProfEvalReportCityOrgPinned
} from '@/api'
const { TextArea } = Input

interface IProps {
  //选中的是哪个节点
  leafKey: string
  //是否可见，true可见 false不可见
  isVisible?: boolean
  //权限 view 只能查看
  permission?: string
  //组织机构id
  orgId?: string
}

export default class extends React.Component<IProps> {
  static contextType = LoadingContext
  context: React.ContextType<typeof LoadingContext>
  intervalId = null // 在这里定义 intervalId
  //保存前数据
  preKeyList: any = []

  state = {
    dateId: '',
    menuType: '',
    //表格list
    performanList: [],
    //指标名称列表
    zbmcList: [],
    //部门list
    bmList: [],
    //已选择的行
    selectedRows: [],
    isModalVisible: false,
    // 选择考评依据的行和列
    index: 0,
    reasonItem: {
      select: '', // 选中的dictKey
      list: [] // 用于展示在弹窗table中的列
    },
    departMent: {
      isDepartVisible: false, //弹出部门选择
      indexBig: '',
      indexSmall: '',
      selectedCode: []
    }
  }

  componentDidMount() {
    if (this.props.leafKey.includes('-')) {
      this.initializeData()
    }
    // 每隔2分钟（122000毫秒）执行
    this.intervalId = setInterval(this.saveAll, 122000)
  }

  componentWillUnmount() {
    // 清除定时器
    clearInterval(this.intervalId)
  }

  //定时保存
  saveAll = () => {
    let list = [...this.state.performanList]
    for (let i = 0; i < list.length; i++) {
      let item = list[i]
      let smallList = item.lineList
      let smallEdit = false
      for (let j = 0; j < smallList.length; j++) {
        let smallItem = smallList[j]
        if (smallItem.isSmallEdit) {
          smallEdit = true
          break
        }
      }
      if (smallEdit && JSON.stringify(this.preKeyList[i]) !== JSON.stringify(item)) {
        this.handleSaveSmall(i, 0, 'regular')
      }
    }
    //记录当前数据
    this.preKeyList = JSON.parse(JSON.stringify(list))
  }

  componentDidUpdate(prevProps: Readonly<IProps>) {
    if (prevProps.leafKey !== this.props.leafKey) {
      if (this.props.leafKey.includes('-')) {
        const [dateId, menuType] = this.props.leafKey.split('-')
        this.setState({ dateId, menuType, selectedRows: [] }, () => {
          this.initializeData()
        })
      }
    }
  }

  initializeData = async () => {
    try {
      //获取部门list
      this.getBmList()
      //获取业绩指标名称
      getDictListByName({
        categoryType: '专业评价市公司',
        type: '考评项目',
        orgId: this.props.orgId
      }).then((res) => {
        // 同时拿到考评项目和对应的key，以及考评依据，保存到
        let performanList = this.state.performanList
        performanList.map((item) => {
          item.zbmcCode = res.data.list.find((dict) => dict.value === item.assessItem)?.id
        })
        let data = []
        res.data.list.map((item) => {
          data.push({
            value: item.value,
            dictKey: item.dictKey,
            content: item.content
          })
        })
        this.setState({ zbmcList: res.data.list, performanList })
      })
    } catch (error) {
      console.error('Error loading data:', error)
    }
  }

  getPerformanList = () => {
    let datas = this.props.leafKey.split('-')
    let params: any = {
      date: datas[0],
      orgId: null
    }
    this.props.orgId && (params.orgId = this.props.orgId)
    this.setState({ dateId: datas[0], menuType: datas[1] })
    this.context.showLoading()

    if (this.props.permission == 'view') {
      params = {
        ...params,
        status: '1',
        source: 'audit'
      }
    }
    getCityList(params)
      .then((res) => {
        let list = res.data.list
        let performanList = []
        list.map((item) => {
          let obj = {
            id: item.ppercId,
            pprId: item.pprId,
            assessItem: item.assessItem,
            zbmcCode: this.state.zbmcList.find((dict) => dict.value === item.assessItem)?.dictKey,
            assessContent: item.assessContent,
            reason: item.reason || item.assessItem,
            sort: item.sort,
            hj: 0,
            isEdit: item.allLineList[0]?.score ? false : true,
            lineList: []
          }
          let totalScore = 0
          if (item.allLineList.length > 0) {
            item.allLineList.map((item2) => {
              let obj2 = {
                bmmc: item2.lineList.map((item3) => item3.orgName).join('、'),
                bmmcCode: item2.lineList.map((item3) => item3.orgId),
                score: item2.score,
                rowIdx: item2.rowIdx,
                isSmallEdit: item2.score ? false : true
              }
              totalScore = totalScore + item2.score * obj2.bmmcCode.length
              obj.lineList.push(obj2)
            })
          }
          // totalScore取一位小数
          obj.hj = getNumber(totalScore)
          if (obj.lineList?.length == 0) {
            obj.lineList.push({
              bmmc: '',
              bmmcCode: [],
              score: '',
              isSmallEdit: false
            })
          }
          performanList.push(obj)
        })
        this.setState({ performanList })
      })
      .finally(() => {
        this.context.hideLoading()
      })
  }

  getBmList = () => {
    getOrgList('02', '1').then((res) => {
      this.setState({ bmList: res.data.list }, () =>
        //获取列表数据
        this.getPerformanList()
      )
    })
  }

  //提交前校验
  submitYz = () => {
    //默认填写完成
    let flag = true
    //判断是否有正在填写的内容，并且所有的合计分不能超过15分
    let list = this.state.performanList
    let allScore = 0
    for (let i = 0; i < list.length; i++) {
      let item = list[i]
      if (item.isEdit) {
        flag = false
        Modal.warning({
          title: '提示',
          content: `【专业评价填报-市公司部门评价】中，第${i + 1}行需填写并保存完成，并保存！`,
          zIndex: 1100
        })
        break
      }
      let bmqkList = list[i].lineList
      for (let j = 0; j < bmqkList.length; j++) {
        let bmqkItem = bmqkList[j]
        if (bmqkItem.isSmallEdit) {
          flag = false
          Modal.warning({
            title: '提示',
            content: `【专业评价填报-市公司部门评价】中，第${i + 1}行需填写完成，并保存！`,
            zIndex: 1100
          })
          break
        }
      }
      allScore = allScore + parseFloat(item.hj)
      if (getNumber(allScore) > 15) {
        flag = false
        Modal.warning({
          title: '提示',
          content: '【专业评价填报-市公司部门评价】中，合计分不能超过15分！',
          zIndex: 1100
        })
        break
      }
      if (!flag) break
    }

    return flag
  }

  //新增一行
  handleAdd = () => {
    let list = [...this.state.performanList]
    for (let i = 0; i < list.length; i++) {
      let obj = list[i]
      // 如果有正在编辑的内容，不能进行新增
      if (obj.isEdit) {
        // if (obj.isEdit || obj.lineList.filter((item) => item.isSmallEdit).length > 0) {
        Modal.warning({
          title: '提示',
          content: '有正在编辑的内容，请完成后，再新增一行！',
          zIndex: 1100
        })
        return
      }
    }
    list.push({
      id: '',
      assessItem: '',
      zbmcCode: '',
      assessContent: '',
      hj: 0,
      isEdit: true,
      score: '',
      reason: '',
      //临时id
      cacheId: getUUID(),
      lineList: [
        {
          bmmc: '',
          bmmcCode: [],
          score: '',
          isSmallEdit: true
        }
      ]
    })
    this.setState({ performanList: list })
  }

  //批量删除行
  handleDelete = () => {
    let _this = this
    Modal.confirm({
      title: '删除提示',
      content: '确定删除吗?',
      onOk: () => {
        let allId = _this.state.performanList.map((item) => item.id)
        let cachIdList = _this.state.selectedRows.filter((item) => !allId.includes(item))
        let idList = _this.state.selectedRows.filter((item) => allId.includes(item))
        _this.context.showLoading()
        if (idList.length === 0) {
          let list = _this.state.performanList.filter((item) => !cachIdList.includes(item.cacheId))
          MessageSelf('删除成功', 'success')
          _this.setState({ performanList: list, selectedRows: [] })
          _this.context.hideLoading()
          return
        }
        //调用删除接口
        deleteCity({ ids: idList.join(',') })
          .then((res: any) => {
            let schList = _this.state.performanList.filter(
              (item) => !_this.state.selectedRows.includes(item.id || item.cacheId)
            )
            if (res.code === '0') {
              MessageSelf('删除成功', 'success')
              _this.setState({ performanList: schList, selectedRows: [] })
            }
          })
          .finally(() => {
            _this.context.hideLoading()
          })
      }
    })
  }

  handleContent = (value, index, tdName, indexSmall?: number) => {
    let list = [...this.state.performanList]
    //如果是zbmcCode指标名称，将选择的保存到已选指标中
    if (tdName === 'assessItem') {
      // 根据指标名称，获取考评依据
      getDictListByName({
        categoryType: '业绩指标考核',
        type: '考评依据-' + value,
        orgId: this.props.orgId
      }).then((res) => {
        list[index].kpList = res.data.list
      })
    }
    if (indexSmall || indexSmall == 0) {
      list[index].lineList[indexSmall][tdName] = value
    } else {
      list[index][tdName] = value
    }

    //如果部门和得分，则需要计算合计
    if (tdName === 'bmmcCode' || tdName === 'score') {
      let count = this.getHj(index, list)
      list[index].hj = count
    }
    this.setState({ performanList: list })
  }

  //计算合计
  getHj = (index, list) => {
    let count = 0
    let bmqkList = list[index].lineList
    for (let i = 0; i < bmqkList.length; i++) {
      let obj = bmqkList[i]
      count = count + obj.bmmcCode.length * obj.score
    }
    return getNumber(count)
  }

  //保存一行
  handleSaveSmall = (index, indexSmall, type?: string) => {
    //校验
    let list = this.state.performanList[index]
    let obj = list.lineList[indexSmall]
    if (obj.bmmcCode.length <= 0) {
      if (!type) {
        Modal.warning({
          title: '提示',
          content: '请选择部门名称！',
          zIndex: 1100
        })
      }

      return
    }
    if (!obj.score) {
      if (!type) {
        Modal.warning({
          title: '提示',
          content: '请输入得分！',
          zIndex: 1100
        })
      }

      return
    }
    if (!list.reason) {
      if (!type) {
        Modal.warning({
          title: '提示',
          content: '请选择考评依据！',
          zIndex: 1100
        })
      }

      return
    }
    if (!list.assessItem) {
      if (!type) {
        Modal.warning({
          title: '提示',
          content: '请选择考评项目！',
          zIndex: 1100
        })
      }

      return
    }
    if (!list.assessContent.trim()) {
      if (!type) {
        Modal.warning({
          title: '提示',
          content: '请填写考评内容！',
          zIndex: 1100
        })
      }

      return
    }
    let list2 = [...this.state.performanList]

    // 单个部门，同一指标下的总得分不能超过0.5   2024.8.14 客户提出不要这个限制条件了
    // 当前行的考评项目
    // let assessItem = list2[index].assessItem
    // 寻找所有的相同考评项目的行
    // let sameAssessItem = list2.filter((item) => item.assessItem === assessItem)
    // 循环当前行选择的部门，计算相同部门的得分总和，如果超过0.5，提示
    // let flag = false
    // let errInfo = {
    //   orgName: '',
    //   assessItemName: ''
    // }
    // obj.bmmcCode.map((orgId) => {
    // let score = 0
    // sameAssessItem.map((item) => {
    //   item.lineList.map((obj2) => {
    //     if (obj2.bmmcCode.includes(orgId)) {
    //       score = score + obj2.score
    //     }
    //   })
    // })
    // if (score > 0.5) {
    //   flag = true
    //   errInfo = {
    //     assessItemName: this.state.zbmcList.find((dict) => dict.dictKey === assessItem)?.value,
    //     orgName: this.state.bmList.filter((zditem) => zditem.id == orgId)[0].orgName
    //   }
    // }
    // })
    // if (flag) {
    //   Modal.warning({
    //     title: '提示',
    //     content: `【${errInfo.orgName}】部门的【${errInfo.assessItemName}】考核项目的总得分已超过0.5分，请检查。`,
    //     zIndex: 1100
    //   })
    //   return
    // }
    //所有分值加起来不能超过15分
    let allScore = 0
    let flag = false
    for (let i = 0; i < list2.length; i++) {
      let item = list2[i]
      allScore = allScore + parseFloat(item.hj)
      if (getNumber(allScore) > 15) {
        flag = true
        break
      }
    }
    if (flag) {
      if (!type) {
        Modal.warning({
          title: '提示',
          content: '【专业评价填报-市公司部门评价】中，合计分不能超过15分！',
          zIndex: 1100
        })
      }

      return
    }
    //调用保存接口，把全部数据全部传过去
    /**
     */
    if (!type) {
      this.context.showLoading()
    }

    let data = this.getSaveData(list2, index)
    saveCity(data)
      .then((res: any) => {
        if (res.code === '0') {
          if (!type) {
            MessageSelf('保存成功', 'success')
          }
        }
        //根据职能部门字典，查找职能部门名称
        let bmmcList = list2[index]['lineList'][indexSmall].bmmcCode
        let bms = []
        bmmcList.map((id) => {
          let znbmKey = this.state.bmList.filter((item) => item.id == id)
          bms.push(znbmKey[0].orgName)
        })
        let bm = bms.join('、')
        list2[index]['lineList'][indexSmall].bmmc = bm
        if (!type) {
          list2[index].isEdit = false
        }

        list2[index].id = res.data.ppercId
        list2[index].pprId = res.data.pprId
        list2[index].sort = res.data.sort
        let smallList = list2[index].lineList
        for (let i = 0; i < smallList.length; i++) {
          smallList[i].rowIdx = res.data.allLineList[i]?.rowIdx
        }
        if (!type) {
          list2[index]['lineList'][indexSmall].isSmallEdit = false
        }

        this.setState({ performanList: list2 })
      })
      .finally(() => {
        if (!type) {
          this.context.hideLoading()
        }
      })
  }

  //编辑
  handleEditSmall = (index, indexSmall) => {
    let list = [...this.state.performanList]
    list[index]['lineList'][indexSmall].isSmallEdit = true
    this.setState({ performanList: list })
  }

  //删除小行
  handleDeleteSmall = (index, indexSmall) => {
    let _this = this
    Modal.confirm({
      title: '删除提示',
      content: '确定删除吗?',
      onOk: () => {
        let list = [..._this.state.performanList]
        list[index]['lineList'].splice(indexSmall, 1)
        _this.context.showLoading()
        //调用删除接口
        let data = _this.getSaveData(list, index)
        saveCity(data)
          .then((res: any) => {
            if (res.code === '0') {
              MessageSelf('删除成功', 'success')
            }
            //重新计算合计
            let count = _this.getHj(index, list)
            list[index].hj = count
            _this.setState({ performanList: list })
          })
          .finally(() => {
            _this.context.hideLoading()
          })
      }
    })
  }

  //新增一小行
  handleAddSmall = (index) => {
    //判断有无正在编辑的内容，如果有，不能进行编辑，提示：有正在编辑的内容，请完成后，再新增一行
    let flag = false //默认没有编辑的
    let yyList = this.state.performanList[index].lineList
    for (let i = 0; i < yyList.length; i++) {
      let obj = yyList[i]
      if (obj.isSmallEdit) {
        flag = true
        break
      }
    }
    if (flag) {
      Modal.warning({
        title: '提示',
        content: '有正在编辑的内容，请完成后，再新增一行！',
        zIndex: 1100
      })
      return
    }
    let list = [...this.state.performanList]
    list[index].lineList.push({
      bmmc: '',
      bmmcCode: [],
      score: '',
      isSmallEdit: true
    })
    this.setState({ performanList: list })
  }

  getSaveData = (list, index) => {
    // list.map((item) => {
    let item = list[index]
    let obj = {
      ppercId: item.id,
      pprId: item.pprId,
      assessItem: item.assessItem,
      assessContent: item.assessContent,
      date: this.state.dateId,
      reason: item.reason,
      sort: index == 0 ? item.sort || 1 : parseInt(list[index - 1].sort + 1),
      allLineList: []
    }
    item.lineList.map((obj2) => {
      let lineList = []
      obj2.bmmcCode.map((orgId) => {
        lineList.push({
          orgId: orgId,
          orgName: this.state.bmList.filter((zditem) => zditem.id == orgId)[0].orgName
        })
      })
      obj.allLineList.push({
        score: obj2.score,
        lineList: lineList
      })
    })
    return obj
  }

  //复选框
  handleCheckBox = (value, key) => {
    if (value) {
      //true
      let list = [...this.state.selectedRows]
      list.push(key)
      this.setState({ selectedRows: list })
    } else {
      //false
      let list = [...this.state.selectedRows]
      let index = list.indexOf(key)
      if (index > -1) {
        list.splice(index, 1)
      }
      this.setState({ selectedRows: list })
    }
  }

  //全选，全不选
  onCheckAllChange = () => {
    let selectedRows = []
    if (this.state.performanList.length > 0) {
      if (this.state.performanList.length != this.state.selectedRows.length) {
        //全选
        let list = this.state.performanList
        list.map((item) => {
          selectedRows.push(item.id || item.cacheId)
        })
      }
    }
    this.setState({
      selectedRows: selectedRows
    })
  }

  openModalTable = (index) => {
    let _this = this
    _this.setState({
      isModalVisible: true,
      index,
      reasonItem: {
        select: _this.state.performanList[index].reason,
        list: _this.state.zbmcList
      }
    })
  }

  handleOk = () => {
    let list = [...this.state.performanList]
    list[this.state.index].assessItem = this.state.reasonItem.select
    // 保存reason
    list[this.state.index].reason = this.state.reasonItem.select
    this.setState({ performanList: list, isModalVisible: false })
  }

  //选择部门名称
  openModalDepartMent = (indexBig, indexSmall) => {
    let list = [...this.state.performanList]
    let selectedCode = list[indexBig].lineList[indexSmall].bmmcCode
    this.setState({ departMent: { isDepartVisible: true, indexBig, indexSmall, selectedCode } })
  }

  //部门选择确定
  determine = (selectedList) => {
    let list = [...this.state.performanList]
    let indexBig = this.state.departMent.indexBig
    let indexSmall = this.state.departMent.indexSmall
    list[indexBig].lineList[indexSmall].bmmc = selectedList?.map((item) => item.orgName).join('，')
    let bmmcCode = []
    selectedList?.map((item) => bmmcCode.push(item.id))
    list[indexBig].lineList[indexSmall].bmmcCode = bmmcCode
    let count = this.getHj(indexBig, list)
    list[indexBig].hj = count
    this.setState({ performanList: list })
  }

  //移动
  handleMove = (index, type) => {
    let list = [...this.state.performanList]
    if (type == 'up') {
      if (index == 0) {
        return
      }
      let temp = list[index]
      let temp2 = list[index - 1]
      if (!temp2?.id || !temp?.id) {
        return
      }
      list[index] = list[index - 1]
      list[index - 1] = temp
      this.saveSwap(temp2.id, temp.id)
    } else if (type == 'down') {
      if (index == list.length - 1) {
        return
      }
      let temp = list[index]
      let temp2 = list[index + 1]
      if (!temp2?.id || !temp?.id) {
        return
      }
      list[index] = list[index + 1]
      list[index + 1] = temp
      this.saveSwap(temp2.id, temp.id)
    } else if (type == 'top') {
      if (index == 0) {
        return
      }
      //置顶
      let temp = list[index]
      if (!temp?.id) {
        return
      }
      list.splice(index, 1)
      list.unshift(temp)
      this.savePinned(temp.id)
    } else if (type == 'bottom') {
      //暂时无置底功能
      if (index == list.length - 1) {
        return
      }
      //置底
      let temp = list[index]
      list.splice(index, 1)
      list.push(temp)
      // this.savePinned(temp.id, '2')
    }
    this.setState({ performanList: list })
  }

  saveSwap = (id1, id2) => {
    this.context.showLoading()
    pdasProfEvalReportCitySwap({ id1: id1, id2: id2 })
      .then((res) => {
        this.getPerformanList()
      })
      .finally(() => {
        this.context.hideLoading()
      })
  }

  savePinned = (id) => {
    this.context.showLoading()
    pdasProfEvalReportCityPinned({ ppercId: id })
      .then((res) => {
        this.getPerformanList()
      })
      .finally(() => {
        this.context.hideLoading()
      })
  }

  //移动小行
  handleMoveSmall = (index, indexSmall, type) => {
    let list = [...this.state.performanList]
    let listSmall = list[index].lineList
    if (type == 'up') {
      if (indexSmall == 0) {
        return
      }
      let temp = listSmall[indexSmall]
      let temp2 = listSmall[indexSmall - 1]
      if (!list[index]?.id || !temp?.rowIdx?.toString() || !temp2?.rowIdx?.toString()) {
        return
      }
      listSmall[indexSmall] = listSmall[indexSmall - 1]
      listSmall[indexSmall - 1] = temp
      this.saveSwapSmall(list[index].id, temp.rowIdx, temp2.rowIdx)
    } else if (type == 'down') {
      if (indexSmall == listSmall.length - 1) {
        return
      }
      let temp = listSmall[indexSmall]
      let temp2 = listSmall[indexSmall + 1]
      if (!list[index]?.id || !temp?.rowIdx?.toString() || !temp2?.rowIdx?.toString()) {
        return
      }
      listSmall[indexSmall] = listSmall[indexSmall + 1]
      listSmall[indexSmall + 1] = temp
      this.saveSwapSmall(list[index].id, temp.rowIdx, temp2.rowIdx)
    } else if (type == 'top') {
      if (indexSmall == 0) {
        return
      }
      //置顶
      let temp = listSmall[indexSmall]
      listSmall.splice(indexSmall, 1)
      if (!list[index]?.id || !temp.rowIdx?.toString()) {
        return
      }
      listSmall.unshift(temp)
      this.savePinnedSmall(list[index].id, temp.rowIdx)
    } else if (type == 'bottom') {
      //暂时无置底功能
      if (indexSmall == listSmall.length - 1) {
        return
      }
      //置底
      let temp = listSmall[indexSmall]
      listSmall.splice(indexSmall, 1)
      listSmall.push(temp)
    }
    this.setState({ performanList: list })
  }

  saveSwapSmall = (ppercId: string, rowIdx1: number, rowIdx2: number) => {
    this.context.showLoading()
    pdasProfEvalReportCityOrgSwap({ ppercId, rowIdx1, rowIdx2 })
      .then((res) => {
        this.getPerformanList()
      })
      .finally(() => {
        this.context.hideLoading()
      })
  }

  savePinnedSmall = (ppercId: string, rowIdx: number) => {
    this.context.showLoading()
    pdasProfEvalReportCityOrgPinned({ ppercId, rowIdx })
      .then((res) => {
        this.getPerformanList()
      })
      .finally(() => {
        this.context.hideLoading()
      })
  }

  render(): React.ReactNode {
    return (
      <>
        <div style={{ height: '100%' }}>
          {this.props.permission != 'view' && (
            <div style={{ display: 'flex', position: 'absolute', top: 0, right: 0 }}>
              <div
                className={`common-button2 common-button3 ${!this.props.isVisible && 'disabled-div'}`}
                onClick={() => this.handleAdd()}
              >
                <Iconfont type="icon-xinzeng" style={{ fontSize: '20px' }} />
                <div className="common-text2">新增一行</div>
              </div>
              <div
                style={{ marginLeft: '10px' }}
                className={`common-button2 common-button3 ${
                  (this.state.selectedRows.length == 0 || !this.props.isVisible) && 'disabled-div'
                }`}
                onClick={() => this.handleDelete()}
              >
                <Iconfont type="icon-shanchu" style={{ fontSize: '20px' }} />
                <div className="common-text">删除</div>
              </div>
            </div>
          )}

          <div style={{ height: '100%' }}>
            <div className="div-table">
              <table className="common-table">
                <thead>
                  <tr>
                    {this.props.permission != 'view' && (
                      <th>
                        <Checkbox
                          onChange={() => this.onCheckAllChange()}
                          checked={
                            this.state.performanList.length > 0 &&
                            this.state.selectedRows.length == this.state.performanList.length
                              ? true
                              : false
                          }
                        ></Checkbox>
                      </th>
                    )}

                    <th>序号</th>
                    {this.props.permission != 'view' && <th>顺序调整</th>}
                    <th>考评项目</th>
                    <th>考评内容</th>
                    <th>部门名称</th>
                    <th>得分</th>
                    <th>合计</th>
                    <th>考评依据</th>
                    {this.props.permission != 'view' && <th>操作</th>}
                  </tr>
                </thead>
                <tbody>
                  {this.state.performanList.map((item, index) => {
                    return item.lineList.map((obj, index2) => {
                      return (
                        <tr key={`${index}-${index2}`}>
                          {this.props.permission != 'view' && index2 == 0 && (
                            <td style={{ width: '45px' }} rowSpan={item.lineList.length}>
                              <Checkbox
                                checked={this.state.selectedRows.includes(item.id || item.cacheId)}
                                onChange={(e) => this.handleCheckBox(e.target.checked, item.id || item.cacheId)}
                              ></Checkbox>
                            </td>
                          )}

                          {index2 == 0 && (
                            <td style={{ width: '45px' }} rowSpan={item.lineList.length}>
                              {index + 1}
                            </td>
                          )}
                          {index2 == 0 && this.props.permission != 'view' && (
                            <td style={{ width: '70px' }} rowSpan={item.lineList.length}>
                              <div className="table-operate">
                                <div
                                  className={`${!this.props.isVisible && 'disabled-table-div'}`}
                                  onClick={() => this.handleMove(index, 'up')}
                                >
                                  <Iconfont title="上移" type="icon-shangyimian" style={{ fontSize: '16px' }} />
                                </div>
                                <div
                                  style={{ paddingLeft: '3px' }}
                                  className={`${!this.props.isVisible && 'disabled-table-div'}`}
                                  onClick={() => this.handleMove(index, 'down')}
                                >
                                  <Iconfont title="下移" type="icon-xiayimian" style={{ fontSize: '16px' }} />
                                </div>
                                <div
                                  style={{ paddingLeft: '3px' }}
                                  className={`${!this.props.isVisible && 'disabled-table-div'}`}
                                  onClick={() => this.handleMove(index, 'top')}
                                >
                                  <Iconfont title="置顶" type="icon-zhidingmian" style={{ fontSize: '16px' }} />
                                </div>
                              </div>
                            </td>
                          )}
                          {index2 == 0 && (
                            <td style={{ width: '140px' }} rowSpan={item.lineList.length}>
                              {obj.isSmallEdit && this.props.isVisible ? (
                                item.assessItem ? (
                                  <div
                                    style={{
                                      border: '1px solid #95bcec',
                                      cursor: 'pointer',
                                      borderRadius: '5px',
                                      backgroundColor: '#fff'
                                    }}
                                    onClick={() => this.openModalTable(index)}
                                  >
                                    {this.state.zbmcList.find((dict) => dict.dictKey === item.assessItem)?.value}
                                  </div>
                                ) : (
                                  <div
                                    style={{ cursor: 'pointer', color: '#1890ff' }}
                                    onClick={() => this.openModalTable(index)}
                                  >
                                    点击选择考评项目
                                  </div>
                                )
                              ) : (
                                this.state.zbmcList.find((dict) => dict.dictKey === item.assessItem)?.value
                              )}
                            </td>
                          )}
                          {index2 == 0 && (
                            <td style={{ minWidth: '120px' }} rowSpan={item.lineList.length}>
                              {obj.isSmallEdit && this.props.isVisible ? (
                                <div style={{ display: 'flex', alignItems: 'center' }}>
                                  <TextArea
                                    autoSize
                                    maxLength={1000}
                                    // showCount
                                    value={item.assessContent}
                                    placeholder="请填写考评内容"
                                    onChange={(obj) => this.handleContent(obj.target.value, index, 'assessContent')}
                                  />
                                </div>
                              ) : (
                                item.assessContent
                              )}
                            </td>
                          )}
                          <td style={{ width: '130px' }}>
                            {obj.bmmc}
                            {obj.isSmallEdit && (
                              <div
                                style={{ cursor: 'pointer', color: '#1890ff' }}
                                onClick={() => this.openModalDepartMent(index, index2)}
                              >
                                点击选择部门名称
                              </div>
                            )}
                          </td>
                          <td style={{ width: '50px' }}>
                            {obj.isSmallEdit ? (
                              <InputNumber
                                style={{ width: '50px' }}
                                controls={false}
                                precision={decimalPlaces}
                                // max={0.5}
                                value={obj.score}
                                onChange={(value) => {
                                  if (!validateNumber(value, '请输入<=0.5的数值！', 0.5, '')) return
                                  this.handleContent(value, index, 'score', index2)
                                }}
                              />
                            ) : (
                              obj.score
                            )}
                          </td>
                          {index2 == 0 && (
                            <td style={{ width: '50px' }} rowSpan={item.lineList.length}>
                              {item.hj}
                            </td>
                          )}

                          {index2 == 0 && (
                            <td rowSpan={item.lineList.length}>
                              <div
                                style={{
                                  textAlign: 'start'
                                }}
                              >
                                {this.state.zbmcList.find((dict) => dict.dictKey === item.assessItem)?.content}
                              </div>
                            </td>
                          )}
                          {this.props.permission != 'view' && (
                            <td style={{ width: '150px' }}>
                              <div className="table-operate" style={{ justifyContent: 'center' }}>
                                {obj.isSmallEdit ? (
                                  <div
                                    className={`${!this.props.isVisible && 'disabled-table-div'}`}
                                    onClick={() => this.handleSaveSmall(index, index2)}
                                  >
                                    <Iconfont type="icon-icon_duihao-mian" style={{ fontSize: '16px' }} />
                                    <span className="operate-text">保存</span>
                                  </div>
                                ) : (
                                  <div
                                    className={`${!this.props.isVisible && 'disabled-table-div'}`}
                                    onClick={() => this.handleEditSmall(index, index2)}
                                  >
                                    <Iconfont type="icon-bianji3" style={{ fontSize: '16px' }} />
                                    <span className="operate-text">编辑</span>
                                  </div>
                                )}

                                {index2 != 0 && (
                                  <div
                                    className={`${!this.props.isVisible && 'disabled-table-div'}`}
                                    style={{ paddingLeft: '10px' }}
                                    onClick={() => this.handleDeleteSmall(index, index2)}
                                  >
                                    <Iconfont type="icon-shanchu" style={{ fontSize: '16px' }} />
                                    <span className="operate-text">删除</span>
                                  </div>
                                )}

                                {index2 == 0 && (
                                  <div
                                    className={`${!this.props.isVisible && 'disabled-table-div'}`}
                                    style={{ paddingLeft: '10px' }}
                                    onClick={() => this.handleAddSmall(index)}
                                  >
                                    <Iconfont type="icon-xinzeng" style={{ fontSize: '16px' }} />
                                    <span className="operate-text">新增一行</span>
                                  </div>
                                )}
                              </div>
                              <div className="table-operate" style={{ justifyContent: 'center', paddingTop: 5 }}>
                                <div
                                  className={`${!this.props.isVisible && 'disabled-table-div'}`}
                                  onClick={() => this.handleMoveSmall(index, index2, 'up')}
                                >
                                  <Iconfont title="上移" type="icon-shangyimian" style={{ fontSize: '16px' }} />
                                </div>
                                <div
                                  style={{ paddingLeft: '3px' }}
                                  className={`${!this.props.isVisible && 'disabled-table-div'}`}
                                  onClick={() => this.handleMoveSmall(index, index2, 'down')}
                                >
                                  <Iconfont title="下移" type="icon-xiayimian" style={{ fontSize: '16px' }} />
                                </div>
                                <div
                                  style={{ paddingLeft: '3px' }}
                                  className={`${!this.props.isVisible && 'disabled-table-div'}`}
                                  onClick={() => this.handleMoveSmall(index, index2, 'top')}
                                >
                                  <Iconfont title="置顶" type="icon-zhidingmian" style={{ fontSize: '16px' }} />
                                </div>
                              </div>
                            </td>
                          )}
                        </tr>
                      )
                    })
                  })}
                </tbody>
              </table>
            </div>
          </div>
        </div>
        <ModalInfo
          title="选择考评依据"
          visible={this.state.isModalVisible}
          width="1150px"
          maxHeight="800px"
          onClose={() => this.setState({ isModalVisible: false })}
        >
          <div>
            <div style={{ display: 'flex', justifyContent: 'start', marginBottom: '10px', position: 'sticky', top: 0 }}>
              <Input
                style={{ width: '200px', marginRight: '10px' }}
                placeholder="请输入考评项目关键字"
                onChange={debounce((e) => {
                  if (!e.target.value) {
                    this.setState({
                      reasonItem: { list: this.state.zbmcList, select: this.state.reasonItem.select }
                    })
                    return
                  }
                  let list = this.state.zbmcList
                  list = list.filter((item) => item.value.indexOf(e.target.value) > -1)
                  this.setState({ reasonItem: { list, select: this.state.reasonItem.select } })
                }, 200)}
              />
              <Input
                style={{ width: '200px' }}
                placeholder="请输入考评依据关键字"
                onChange={debounce((e) => {
                  if (!e.target.value) {
                    this.setState({
                      reasonItem: { list: this.state.zbmcList, select: this.state.reasonItem.select }
                    })
                    return
                  }
                  let list = this.state.zbmcList
                  list = list.filter((item) => item.content.indexOf(e.target.value) > -1)
                  this.setState({ reasonItem: { list, select: this.state.reasonItem.select } })
                }, 200)}
              />
            </div>
            <div style={{ maxHeight: '600px', overflow: 'auto' }}>
              <table className="common-table">
                <thead>
                  <tr>
                    <th>选择</th>
                    <th>考评项目</th>
                    <th>考评依据</th>
                  </tr>
                </thead>
                <tbody>
                  {this.state.reasonItem.list.map((item, index) => {
                    return (
                      <tr
                        key={index}
                        onClick={() => {
                          this.setState({ reasonItem: { list: this.state.reasonItem.list, select: item.dictKey } })
                        }}
                        onDoubleClick={() => {
                          this.setState({
                            reasonItem: { list: this.state.reasonItem.list, select: item.dictKey }
                          })
                          this.handleOk()
                        }}
                        style={{
                          cursor: 'pointer',
                          backgroundColor: this.state.reasonItem.select === item.dictKey && '#f0f0f0'
                        }}
                      >
                        <td style={{ width: '45px' }}>
                          <Checkbox
                            checked={this.state.reasonItem.select === item.dictKey}
                            onChange={(e) => {
                              if (e.target.checked) {
                                let list = [...this.state.reasonItem.list]
                                list.map((obj) => {
                                  obj.dictKey === item.dictKey ? (obj.checked = true) : (obj.checked = false)
                                })
                                this.setState({
                                  reasonItem: {
                                    list: list,
                                    select: item.dictKey
                                  }
                                })
                              }
                            }}
                          ></Checkbox>
                        </td>
                        <td>{item.value}</td>
                        <td>
                          <div
                            style={{
                              cursor: 'pointer',
                              textAlign: 'start'
                            }}
                          >
                            {item.content}
                          </div>
                        </td>
                      </tr>
                    )
                  })}
                </tbody>
              </table>
            </div>
          </div>
          <div style={{ display: 'flex', justifyContent: 'end', marginTop: '20px' }}>
            <Button
              className="tr-btn"
              style={{ marginRight: '12px' }}
              onClick={() => this.setState({ isModalVisible: false })}
            >
              取消
            </Button>
            <Button
              type="primary"
              className="tr-btn"
              onClick={() => {
                this.handleOk()
              }}
            >
              确定
            </Button>
          </div>
        </ModalInfo>

        {this.state.departMent.isDepartVisible && (
          <Department
            isModalVisible={this.state.departMent.isDepartVisible}
            list={this.state.bmList}
            selectedCode={this.state.departMent.selectedCode}
            closeModal={() => this.setState({ departMent: { isDepartVisible: false } })}
            determine={(selectedList) => this.determine(selectedList)}
          />
        )}
      </>
    )
  }
}
