import React from 'react'
import { Iconfont } from 'tr-cheers'
import { Input, Modal, Select, InputNumber } from 'antd'
import { getNumber, decimalPlaces, getOrgNames, validateNumber } from '@/utils/utils'
import MessageSelf from '@/components/message'
import {
  getMeargeTemplList,
  getOrgList,
  pdasPrefPlanCollabOrgPinned,
  pdasPrefPlanCollabOrgSwap,
  savePrefPlanReport
} from '@/api'
import { LoadingContext } from '@/components/load/loadingProvider'
import Department from '../Common/department'
const { TextArea } = Input

interface IProps {
  leafKey: String
  isVisible?: boolean
  permission?: string
  orgId?: string
}

export default class extends React.Component<IProps> {
  static contextType = LoadingContext
  context: React.ContextType<typeof LoadingContext>
  intervalId = null // 在这里定义 intervalId
  //保存前数据
  preKeyList: any = []

  state = {
    //table数据
    keyList: [],
    //部门列表
    bmList: [],
    //有效的部门列表
    bmActiveList: [],
    dateId: '',
    menuType: '',
    departMent: {
      isDepartVisible: false, //弹出部门选择
      indexBig: '',
      indexSmall: '',
      selectedCode: []
    }
  }

  componentDidMount(): void {
    if (this.props.leafKey.includes('-')) {
      //将其拆分
      let datas = this.props.leafKey.split('-')
      this.setState({ dateId: datas[0], menuType: datas[1] }, () => {
        // 获取列表数据
        this.getKeyList()
      })
    }
    //获取部门列表
    this.getBmList()

    // 每隔2分钟（120000毫秒）执行
    this.intervalId = setInterval(this.saveAll, 126000)
  }

  componentWillUnmount() {
    // 清除定时器
    clearInterval(this.intervalId)
  }

  //定时保存
  saveAll = () => {
    let list = [...this.state.keyList]
    for (let i = 0; i < list.length; i++) {
      let item = list[i]
      if (item.isEdit) {
        if (JSON.stringify(this.preKeyList[i]) !== JSON.stringify(item)) {
          //保存大行
          this.handleSaveWcqk(i, 'regular')
        }
      } else {
        let smallList = item.allLineList
        let smallEdit = false
        for (let j = 0; j < smallList.length; j++) {
          let smallItem = smallList[j]
          if (smallItem.isSmallEdit) {
            smallEdit = true
            break
          }
        }

        if (smallEdit && JSON.stringify(this.preKeyList[i]) !== JSON.stringify(item)) {
          this.handleSaveWcqk(i, 'regular')
        }
      }
    }
    //记录当前数据
    this.preKeyList = JSON.parse(JSON.stringify(list))
  }

  componentDidUpdate(prevProps: Readonly<IProps>, prevState: Readonly<{}>, snapshot?: any): void {
    if (prevProps.leafKey != this.props.leafKey) {
      if (this.props.leafKey.includes('-')) {
        //将其拆分
        let datas = this.props.leafKey.split('-')
        this.setState({ dateId: datas[0], menuType: datas[1] }, () => {
          // 获取列表数据
          this.getKeyList()
        })
      }
    }
  }

  //提交前验证
  submitYz = () => {
    //默认填写完成
    let flag = true
    //判断是否有正在填写的内容
    let list = this.state.keyList
    for (let i = 0; i < list.length; i++) {
      let item = list[i]
      if (item.isEdit) {
        flag = false
        Modal.warning({
          title: '提示',
          content: `【重点绩效计划填报】中，第${i + 1}行“完成情况”需填写完成，并保存！`,
          zIndex: 1100
        })
        break
      }
      let phbmList = list[i].allLineList
      for (let j = 0; j < phbmList.length; j++) {
        let phbmItem = phbmList[j]
        if (phbmItem.isSmallEdit) {
          flag = false
          Modal.warning({
            title: '提示',
            content: `【重点绩效计划填报】中，第${i + 1}行"配合部门和得分倍比"需填写完成，并保存！`,
            zIndex: 1100
          })
          break
        }
      }
      if (!flag) break
    }

    return flag
  }

  getBmList = () => {
    getOrgList('01,02,03').then((res: any) => {
      if (res?.data?.list?.length > 0) {
        //根据返回数据是否设置有效的部门列表 isActive:"1" 为有效
        let bmActiveList = []
        for (let i = 0; i < res?.data?.list?.length; i++) {
          let item = res.data.list[i]
          if (item.isActive == '1') {
            bmActiveList.push(item)
          }
        }
        this.setState({ bmList: res.data.list, bmActiveList: bmActiveList })
      }
    })
  }

  getKeyList = () => {
    let params: any = {
      date: this.state.dateId,
      orgId: this.props.orgId
    }
    if (this.props.permission == 'view') {
      params = {
        ...params,
        status: '1',
        source: 'audit'
      }
    }
    this.context.showLoading()
    getMeargeTemplList(params)
      .then((res: any) => {
        if (res?.data?.list?.length > 0) {
          //设置牵头部门
          for (let i = 0; i < res.data.list.length; i++) {
            let obj = res.data.list[i]
            let znbms = []
            if (obj.orgName1) {
              znbms.push(obj.orgName1)
            }
            if (obj.orgName2) {
              znbms.push(obj.orgName2)
            }
            let znbm = znbms.join('、')
            obj.znbm = znbm
            if (obj.ppprId && obj.completionStatus) {
              obj.isEdit = false
            } else {
              obj.isEdit = true
            }

            if (!obj.allLineList || obj.allLineList.length == 0) {
              //初始化一行
              obj.allLineList = [
                {
                  orgIds: [],
                  ratio: '',
                  isSmallEdit: true
                }
              ]
              obj.hj = 0
            } else {
              for (let j = 0; j < obj.allLineList.length; j++) {
                let orgIds = []
                let lineList = obj.allLineList[j].lineList
                for (let k = 0; k < lineList.length; k++) {
                  let line = lineList[k]
                  orgIds.push(line.orgId)
                }
                obj.allLineList[j].isSmallEdit = obj.allLineList[j].ratio ? false : true
                obj.allLineList[j].orgIds = orgIds
              }
              //设置合计
              let count = this.getHj(i, res.data.list)
              obj.hj = count
            }
          }

          this.setState({ keyList: res.data.list })
        } else {
          this.setState({ keyList: [] })
        }
        this.context.hideLoading()
      })
      .catch(() => {
        this.context.hideLoading()
      })
  }

  //编辑
  handleEditSmall = (index, indexSmall) => {
    let list = [...this.state.keyList]
    list[index]['allLineList'][indexSmall].isSmallEdit = true
    this.setState({ keyList: list })
  }

  //删除
  handleDeleteSmall = (index, indexSmall) => {
    let _this = this
    Modal.confirm({
      title: '删除提示',
      content: '确定删除吗?',
      onOk: () => {
        let list = [..._this.state.keyList]
        list[index]['allLineList'].splice(indexSmall, 1)
        //调用删除接口(与保存接口是同一个接口)
        let data = this.setSaveParam(list[index])
        //调用保存完成情况接口
        _this.context.showLoading()
        savePrefPlanReport(data)
          .then((res: any) => {
            if (res?.data) {
              MessageSelf('删除成功！', 'success')
              //重新计算合计
              let count = _this.getHj(index, list)
              list[index].hj = count
              _this.setState({ keyList: list })
            } else {
              _this.getKeyList()
            }
            _this.context.hideLoading()
          })
          .catch(() => {
            _this.context.hideLoading()
          })
      }
    })
  }

  //保存一行
  handleSaveSmall = (index, indexSmall, type?: string) => {
    //校验
    let obj = this.state.keyList[index].allLineList[indexSmall]

    if (obj.orgIds.length <= 0) {
      if (!type) {
        Modal.warning({
          title: '提示',
          content: '请选择配合部门名称！',
          zIndex: 1100
        })
      }
      return
    }
    if (!obj.ratio) {
      if (!type) {
        Modal.warning({
          title: '提示',
          content: '请输入得分倍比！',
          zIndex: 1100
        })
      }
      return
    }
    let count = this.getHj(index, this.state.keyList)
    if (count > 5) {
      if (!type) {
        Modal.warning({
          title: '提示',
          content: `第${index + 1}行，合计不能超过5！`,
          zIndex: 1100
        })
      }
      return
    }
    //针对同一项重点工作计划的所有的小行里，“配合单位”字段里同一个单位只能出现一次
    let smallList = this.state.keyList[index].allLineList
    for (let i = 0; i < smallList.length; i++) {
      if (i != indexSmall) {
        let objOther = smallList[i]
        let orgIds = objOther.orgIds
        for (let j = 0; j < orgIds.length; j++) {
          let orgId = orgIds[j]
          if (obj.orgIds.indexOf(orgId) != -1) {
            let orgName = getOrgNames([orgId], this.state.bmList)
            if (!type) {
              Modal.warning({
                title: '提示',
                content: orgName + '已存在，无法再次选择',
                zIndex: 1100
              })
            }
            return
          }
        }
      }
    }
    //调用保存接口
    let data = this.setSaveParam(this.state.keyList[index])
    if (!type) {
      this.context.showLoading()
    }

    savePrefPlanReport(data)
      .then((res) => {
        if (res?.data) {
          if (!type) {
            MessageSelf('保存成功！', 'success')
          }
          let list = [...this.state.keyList]
          list[index].ppprId = res.data.ppprId
          list[index].pprId = res.data.pprId
          if (!type) {
            list[index].allLineList[indexSmall].isSmallEdit = false
          }
          this.setState({ keyList: list })
        } else {
          if (!type) {
            this.getKeyList()
          }
        }
      })
      .finally(() => {
        if (!type) {
          this.context.hideLoading()
        }
      })
  }

  //新增一行
  handleAddRow = (index) => {
    //判断有无正在编辑的内容，如果有，不能进行编辑，提示：有正在编辑的内容，请完成后，再新增一行
    let flag = false //默认没有编辑的
    let yyList = this.state.keyList[index].allLineList
    for (let i = 0; i < yyList.length; i++) {
      let obj = yyList[i]
      if (obj.isSmallEdit) {
        flag = true
        break
      }
    }
    if (flag) {
      Modal.warning({
        title: '提示',
        content: '有正在编辑的内容，请完成后，再新增一行！',
        zIndex: 1100
      })
      return
    }
    let list = [...this.state.keyList]
    list[index].allLineList.push({
      orgIds: [],
      ratio: '',
      isSmallEdit: true
    })
    this.setState({ keyList: list })
  }

  //编辑框内容
  handleContent = (value, index, tdName, indexSmall?: number) => {
    //判断，如果是职能部门，只能选择25个
    if (tdName === 'orgIds') {
      if (value.length > 25) {
        //比25个多出几个
        let dcNum = value.length - 25
        value.splice(0, dcNum)
      }
    }
    let list = [...this.state.keyList]
    if (indexSmall || indexSmall == 0) {
      list[index]['allLineList'][indexSmall][tdName] = value
    } else {
      list[index][tdName] = value
    }

    //如果是配合单位名称和得分对比，则需要计算合计
    if (tdName === 'orgIds' || tdName === 'ratio') {
      let count = this.getHj(index, list)
      list[index].hj = count
    }
    this.setState({ keyList: list })
  }

  //计算合计，并且合计总数需要<=5
  getHj = (index, list, type?: string) => {
    let count = 0
    let phbmList = list[index].allLineList
    for (let i = 0; i < phbmList.length; i++) {
      let obj = phbmList[i]
      count = count + obj.orgIds.length * obj.ratio
    }
    if (count > 5) {
      if (!type) {
        Modal.warning({
          title: '提示',
          content: `第${index + 1}行，合计不能超过5！`,
          zIndex: 1100
        })
      }
    }
    return getNumber(count)
  }

  //设置保存时的参数
  setSaveParam = (obj) => {
    let allLineList = obj.allLineList
    let saveList = []
    for (let i = 0; i < allLineList.length; i++) {
      let item = allLineList[i]
      let lineList = []
      item.orgIds.forEach((org) => {
        lineList.push({
          orgId: org
        })
      })
      saveList.push({
        ratio: item.ratio,
        lineList: lineList
      })
    }
    let data = {
      pptmId: obj.pptmId,
      ppprId: obj.ppprId,
      pprId: obj.pprId,
      planName: obj.planName,
      workType: obj.workType,
      completionStatus: obj.completionStatus,
      allLineList: saveList
    }
    return data
  }

  //保存完成情况
  handleSaveWcqk = (index, type?: string) => {
    let obj = this.state.keyList[index]
    //判断是否为空
    if (!obj.completionStatus) {
      if (!type) {
        Modal.warning({
          title: '提示',
          content: '完成情况不能为空！',
          zIndex: 1100
        })
      }
      return
    }

    if (type) {
      //自动保存，校验其他内容
      //校验小行
      let phbmList = obj.allLineList
      let orgIdMap = new Map()
      for (let i = 0; i < phbmList.length; i++) {
        let objSmall = phbmList[i]
        //配合部门名称 必填
        if (objSmall.orgIds.length <= 0) {
          return
        }
        //得分倍比 必填
        if (!objSmall.ratio) {
          return
        }
        let count = this.getHj(index, this.state.keyList, 'regular')
        //合计不能超过5！
        if (count > 5) {
          return
        }
        //针对同一项重点工作计划的所有的小行里，“配合单位”字段里同一个单位只能出现一次
        let orgIds = objSmall.orgIds
        for (let k = 0; k < orgIds.length; k++) {
          let orgId = orgIds[k]
          if (orgIdMap.has(orgId)) {
            return
          } else {
            orgIdMap.set(orgId, orgId)
          }
        }
      }
    }
    let data = this.setSaveParam(obj)
    if (!type) {
      this.context.showLoading()
    }

    //调用保存完成情况接口
    savePrefPlanReport(data)
      .then((res) => {
        if (res?.data) {
          if (!type) {
            MessageSelf('保存成功！', 'success')
          }
          let list = [...this.state.keyList]
          list[index].ppprId = res.data.ppprId
          list[index].pprId = res.data.pprId
          if (!type) {
            list[index].isEdit = false
          }
          this.setState({ keyList: list })
        } else {
          if (!type) {
            this.getKeyList()
          }
        }
      })
      .finally(() => {
        if (!type) {
          this.context.hideLoading()
        }
      })
  }

  //编辑完成情况
  handleEditWcqk = (index) => {
    let list = [...this.state.keyList]
    list[index].isEdit = true
    this.setState({ keyList: list })
  }

  //选择部门名称
  openModalDepartMent = (indexBig, indexSmall) => {
    let list = [...this.state.keyList]
    let selectedCode = list[indexBig].allLineList[indexSmall].orgIds
    this.setState({ departMent: { isDepartVisible: true, indexBig, indexSmall, selectedCode } })
  }

  //部门选择确定
  determine = (selectedList) => {
    let list = [...this.state.keyList]
    let indexBig = this.state.departMent.indexBig
    let indexSmall = this.state.departMent.indexSmall
    let bmmcCode = []
    selectedList?.map((item) => bmmcCode.push(item.id))
    list[indexBig].allLineList[indexSmall].orgIds = bmmcCode

    let count = this.getHj(indexBig, list)
    list[indexBig].hj = count
    this.setState({ keyList: list })
  }

  //移动小行
  handleMoveSmall = (index, indexSmall, type) => {
    let list = [...this.state.keyList]
    let listSmall = list[index].allLineList
    if (type == 'up') {
      if (indexSmall == 0) {
        return
      }
      let temp = listSmall[indexSmall]
      let temp2 = listSmall[indexSmall - 1]
      if (!list[index]?.ppprId || !temp?.rowIdx?.toString() || !temp2?.rowIdx?.toString()) {
        return
      }
      listSmall[indexSmall] = listSmall[indexSmall - 1]
      listSmall[indexSmall - 1] = temp
      this.saveSwapSmall(list[index].ppprId, temp.rowIdx, temp2.rowIdx)
    } else if (type == 'down') {
      if (indexSmall == listSmall.length - 1) {
        return
      }
      let temp = listSmall[indexSmall]
      let temp2 = listSmall[indexSmall + 1]
      if (!list[index]?.ppprId || !temp.rowIdx?.toString() || !temp2.rowIdx?.toString()) {
        return
      }
      listSmall[indexSmall] = listSmall[indexSmall + 1]
      listSmall[indexSmall + 1] = temp
      this.saveSwapSmall(list[index].ppprId, temp.rowIdx, temp2.rowIdx)
    } else if (type == 'top') {
      if (indexSmall == 0) {
        return
      }
      //置顶
      let temp = listSmall[indexSmall]
      if (!list[index]?.ppprId || !temp.rowIdx?.toString()) {
        return
      }
      listSmall.splice(indexSmall, 1)
      listSmall.unshift(temp)
      this.savePinnedSmall(list[index].ppprId, temp.rowIdx)
    } else if (type == 'bottom') {
      //暂时无置底功能
      if (indexSmall == listSmall.length - 1) {
        return
      }
      //置底
      let temp = listSmall[indexSmall]
      listSmall.splice(indexSmall, 1)
      listSmall.push(temp)
    }
    this.setState({ keyList: list })
  }

  saveSwapSmall = (ppprId: string, rowIdx1: number, rowIdx2: number) => {
    this.context.showLoading()
    pdasPrefPlanCollabOrgSwap({ ppprId, rowIdx1, rowIdx2 })
      .then((res) => {
        this.getKeyList()
      })
      .finally(() => {
        this.context.hideLoading()
      })
  }

  savePinnedSmall = (ppprId: string, rowIdx: number) => {
    this.context.showLoading()
    pdasPrefPlanCollabOrgPinned({ ppprId, rowIdx })
      .then((res) => {
        this.getKeyList()
      })
      .finally(() => {
        this.context.hideLoading()
      })
  }
  render(): React.ReactNode {
    return (
      <div style={{ height: '100%' }}>
        <div className="div-table">
          <table className="common-table">
            <thead>
              <tr>
                <th>序号</th>
                <th>计划名称</th>
                <th>类型</th>
                <th>完成情况</th>
                <th>牵头部门</th>
                <th>配合部门名称</th>
                <th>得分倍比</th>
                <th>合计</th>
                {this.props.permission != 'view' && <th>操作</th>}
              </tr>
            </thead>
            <tbody>
              {this.state.keyList.map((item, index) => {
                return item.allLineList.map((obj, index2) => {
                  return (
                    <tr key={`${index}-${index2}`}>
                      {index2 == 0 && (
                        <td style={{ width: '45px' }} rowSpan={item.allLineList.length}>
                          {index + 1}
                        </td>
                      )}
                      {index2 == 0 && (
                        <td style={{ width: '340px' }} rowSpan={item.allLineList.length}>
                          {item.planName}
                        </td>
                      )}
                      {index2 == 0 && (
                        <td style={{ width: '45px' }} rowSpan={item.allLineList.length}>
                          {item.workTypeVal}
                        </td>
                      )}
                      {index2 == 0 && (
                        <td rowSpan={item.allLineList.length}>
                          {item.isEdit && this.props.isVisible ? (
                            <div style={{ display: 'flex', alignItems: 'center' }}>
                              <TextArea
                                autoSize
                                maxLength={1000}
                                // showCount
                                value={item.completionStatus}
                                placeholder="请填写完成情况"
                                onChange={(obj) => this.handleContent(obj.target.value, index, 'completionStatus')}
                              />
                              <div className="row-opera" onClick={() => this.handleSaveWcqk(index)}>
                                <Iconfont type="icon-icon_duihao-mian" style={{ fontSize: '16px' }} />
                                <span className="operate-text">保存</span>
                              </div>
                            </div>
                          ) : this.props.isVisible ? (
                            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                              <div className="row-text">{item.completionStatus}</div>
                              <div className="row-opera" onClick={() => this.handleEditWcqk(index)}>
                                <Iconfont type="icon-bianji3" style={{ fontSize: '16px' }} />
                                <span className="operate-text">编辑</span>
                              </div>
                            </div>
                          ) : (
                            item.completionStatus
                          )}
                        </td>
                      )}
                      {index2 == 0 && (
                        <td style={{ width: '90px' }} rowSpan={item.allLineList.length}>
                          {item.znbm}
                        </td>
                      )}
                      <td style={{ width: '130px' }}>
                        {getOrgNames(obj.orgIds, this.state.bmList)}
                        {obj.isSmallEdit && (
                          <div
                            style={{ cursor: 'pointer', color: '#1890ff' }}
                            onClick={() => this.openModalDepartMent(index, index2)}
                          >
                            点击选择配合部门名称
                          </div>
                        )}
                      </td>
                      <td style={{ width: '90px' }}>
                        {obj.isSmallEdit ? (
                          <InputNumber
                            controls={false}
                            precision={decimalPlaces}
                            // min={0}
                            // max={1}
                            value={obj.ratio}
                            onChange={(value) => {
                              if (!validateNumber(value, '请输入>0，<=1的数值！', 1, 0)) return
                              this.handleContent(value, index, 'ratio', index2)
                            }}
                          />
                        ) : (
                          obj.ratio
                        )}
                      </td>
                      {index2 == 0 && (
                        <td style={{ width: '45px' }} rowSpan={item.allLineList.length}>
                          {item.hj}
                        </td>
                      )}
                      {this.props.permission != 'view' && (
                        <td style={{ width: '150px' }}>
                          <div className="table-operate" style={{ justifyContent: 'left' }}>
                            {obj.isSmallEdit ? (
                              <div
                                className={`${!this.props.isVisible && 'disabled-table-div'}`}
                                onClick={() => this.handleSaveSmall(index, index2)}
                              >
                                <Iconfont type="icon-icon_duihao-mian" style={{ fontSize: '16px' }} />
                                <span className="operate-text">保存</span>
                              </div>
                            ) : (
                              <div
                                className={`${!this.props.isVisible && 'disabled-table-div'}`}
                                onClick={() => this.handleEditSmall(index, index2)}
                              >
                                <Iconfont type="icon-bianji3" style={{ fontSize: '16px' }} />
                                <span className="operate-text">编辑</span>
                              </div>
                            )}

                            {index2 != 0 && (
                              <div
                                className={`${!this.props.isVisible && 'disabled-table-div'}`}
                                style={{ paddingLeft: '10px' }}
                                onClick={() => this.handleDeleteSmall(index, index2)}
                              >
                                <Iconfont type="icon-shanchu" style={{ fontSize: '16px' }} />
                                <span className="operate-text">删除</span>
                              </div>
                            )}

                            {index2 == 0 && (
                              <div
                                className={`${!this.props.isVisible && 'disabled-table-div'}`}
                                style={{ paddingLeft: '10px' }}
                                onClick={() => this.handleAddRow(index)}
                              >
                                <Iconfont type="icon-xinzeng" style={{ fontSize: '16px' }} />
                                <span className="operate-text">新增一行</span>
                              </div>
                            )}
                          </div>
                          <div className="table-operate" style={{ justifyContent: 'center', paddingTop: 5 }}>
                            <div
                              className={`${!this.props.isVisible && 'disabled-table-div'}`}
                              onClick={() => this.handleMoveSmall(index, index2, 'up')}
                            >
                              <Iconfont title="上移" type="icon-shangyimian" style={{ fontSize: '16px' }} />
                            </div>
                            <div
                              style={{ paddingLeft: '3px' }}
                              className={`${!this.props.isVisible && 'disabled-table-div'}`}
                              onClick={() => this.handleMoveSmall(index, index2, 'down')}
                            >
                              <Iconfont title="下移" type="icon-xiayimian" style={{ fontSize: '16px' }} />
                            </div>
                            <div
                              style={{ paddingLeft: '3px' }}
                              className={`${!this.props.isVisible && 'disabled-table-div'}`}
                              onClick={() => this.handleMoveSmall(index, index2, 'top')}
                            >
                              <Iconfont title="置顶" type="icon-zhidingmian" style={{ fontSize: '16px' }} />
                            </div>
                          </div>
                        </td>
                      )}
                    </tr>
                  )
                })
              })}
            </tbody>
          </table>
        </div>
        {this.state.departMent.isDepartVisible && (
          <Department
            isModalVisible={this.state.departMent.isDepartVisible}
            list={this.state.bmActiveList}
            selectedCode={this.state.departMent.selectedCode}
            closeModal={() => this.setState({ departMent: { isDepartVisible: false } })}
            determine={(selectedList) => this.determine(selectedList)}
            limit={25}
          />
        )}
      </div>
    )
  }
}
