import React from 'react'
import { Iconfont } from 'tr-cheers'
import ModalInfo from '@/components/Modal/index'
import Pagination from '@/components/pagination'
import { <PERSON><PERSON>, DatePicker, Modal } from 'antd'
import { LoadingContext } from '@/components/load/loadingProvider'
import {
  deletePerfPublish,
  downLoadEvidenceMaterial,
  getPerfPublish,
  getPerfPublishList,
  getPerfPublishRetract,
  uploadPerfPublish
} from '@/api'
import MessageSelf from '@/components/message'
import moment from 'moment'

export default class extends React.Component {
  static contextType = LoadingContext
  context: React.ContextType<typeof LoadingContext>
  state = {
    total: 0,
    pageNum: 1,
    pageSize: 10,
    keyList: [],
    isModalVisible: false,
    date: null,
    file: null
  }

  componentDidMount(): void {
    this.getKeyList(1)
  }

  getKeyList = (pageNum: number) => {
    let params: any = {
      pageNum: pageNum,
      pageSize: this.state.pageSize
    }
    this.context.showLoading()
    getPerfPublishList(params)
      .then((res) => {
        if (res?.data?.list?.length > 0) {
          this.setState({ keyList: res.data.list, total: res.data.total, pageNum })
        } else {
          this.setState({ keyList: [], total: 0, pageNum })
        }
      })
      .finally(() => {
        this.context.hideLoading()
      })
  }

  showUploadFile = () => {
    this.setState({ isModalVisible: true, date: null, file: null })
  }

  onChangeDate = (date) => {
    this.setState({ date: date })
  }

  uploadFile = () => {
    const input = document.createElement('input')
    input.type = 'file'
    input.multiple = false
    input.style.display = 'none'

    input.addEventListener('change', (event) => {
      const files = (event.target as HTMLInputElement).files
      if (files.length === 0) return
      this.setState({ file: files[0] })
    })
    document.body.appendChild(input)
    input.click()
  }

  handleOk = () => {
    let file = this.state.file
    //必填校验
    if (!this.state.date) {
      Modal.warning({
        title: '提示',
        content: '请选择考评周期',
        zIndex: 1100
      })
      return
    }
    if (!file) {
      Modal.warning({
        title: '提示',
        content: '请选择考评文件',
        zIndex: 1100
      })
      return
    }
    // 处理上传的文件列表，这里只是简单输出文件名
    if (file.size > 100 * 1024 * 1024) {
      Modal.warning({
        title: '文件大小超过限制',
        content: file.name + '文件大小超过100M',
        zIndex: 1100
      })
      return
    }
    this.context.showLoading()
    //调用上传附件接口
    uploadPerfPublish(moment(this.state.date).format('YYYY年MM月'), file)
      .then((res: any) => {
        if (res?.code == '0') {
          MessageSelf('上传成功', 'success')
          this.setState({ isModalVisible: false })
          this.getKeyList(1)
        }
      })
      .finally(() => {
        this.context.hideLoading()
      })
  }

  handleCancel = () => {
    this.setState({ isModalVisible: false })
  }

  handleDownload = (pfId: string) => {
    downLoadEvidenceMaterial(pfId)
  }

  handlePublish = (id) => {
    Modal.confirm({
      title: '发布',
      content: '确定发布吗？',
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        //调用接口
        this.context.showLoading()
        getPerfPublish({ pppId: id })
          .then((res: any) => {
            if (res.code == '0') {
              MessageSelf('发布成功', 'success')
              this.getKeyList(1)
            }
          })
          .finally(() => {
            this.context.hideLoading()
          })
      }
    })
  }

  //删除
  handleDelete = (id) => {
    Modal.confirm({
      title: '删除',
      content: '确定删除吗？',
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        //调用接口
        this.context.showLoading()
        deletePerfPublish(id)
          .then((res: any) => {
            if (res.code == '0') {
              MessageSelf('删除成功', 'success')
              this.getKeyList(1)
            }
          })
          .finally(() => {
            this.context.hideLoading()
          })
      }
    })
  }

  //撤回
  handleRetract = (id) => {
    Modal.confirm({
      title: '撤回',
      content: '确定撤回吗？',
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        //调用接口
        this.context.showLoading()
        getPerfPublishRetract({ pppId: id })
          .then((res: any) => {
            if (res.code == '0') {
              MessageSelf('撤回成功', 'success')
              this.getKeyList(1)
            }
          })
          .finally(() => {
            this.context.hideLoading()
          })
      }
    })
  }
  render() {
    return (
      <div className="whole" style={{ height: '100%' }}>
        <div
          className="common-button2"
          style={{ marginLeft: 'auto', padding: '10px 15px' }}
          onClick={this.showUploadFile}
        >
          <Iconfont type="icon-yunshangchuan" style={{ fontSize: '26px' }} />
          <div className="common-text">上传</div>
        </div>
        <div className="div-table" style={{ height: 'calc(100% - 45px)' }}>
          <table className="common-table">
            <thead>
              <tr>
                <th>序号</th>
                <th>绩效考评周期</th>
                <th>文件</th>
                <th>上传时间</th>
                <th>发布时间</th>
                <th>数据状态</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              {this.state.keyList.map((item, index) => {
                return (
                  <tr key={index}>
                    <td style={{ width: '45px' }}>{index + 1}</td>
                    <td>{item.evaluationDate}</td>
                    <td>
                      <a onClick={() => this.handleDownload(item.pfId)}>{item.fileName}</a>
                    </td>
                    <td>{item.uploadTime}</td>
                    <td>{item.publishTime}</td>
                    <td>{item.status == '1' ? '已发布' : '未发布'}</td>
                    <td>
                      <div className="table-operate">
                        {item.status != '1' && (
                          <div onClick={() => this.handlePublish(item.pppId)}>
                            <Iconfont type="icon-fabu" style={{ fontSize: '16px' }} />
                            <span className="operate-text">发布</span>
                          </div>
                        )}
                        {item.status != '1' && (
                          <div onClick={() => this.handleDelete(item.pppId)}>
                            <Iconfont type="icon-shanchu" style={{ fontSize: '16px' }} />
                            <span className="operate-text">删除</span>
                          </div>
                        )}
                        {item.status == '1' && (
                          <div onClick={() => this.handleRetract(item.pppId)}>
                            <Iconfont type="icon-chehui1" style={{ fontSize: '16px' }} />
                            <span className="operate-text">撤回</span>
                          </div>
                        )}
                      </div>
                    </td>
                  </tr>
                )
              })}
            </tbody>
          </table>
          <Pagination
            total={this.state.total}
            pageSize={this.state.pageSize}
            pageNum={this.state.pageNum}
            onChange={this.getKeyList.bind(this)}
          />
        </div>
        <ModalInfo
          title="绩效文件上传"
          visible={this.state.isModalVisible}
          width="600px"
          onClose={() => this.setState({ isModalVisible: false })}
        >
          <div>
            <div style={{ lineHeight: '50px' }}>
              考评周期：
              <DatePicker
                value={this.state.date}
                placeholder="请选择考评周期"
                picker="month"
                format="YYYY年MM月"
                onChange={this.onChangeDate}
              />
            </div>
            <div style={{ lineHeight: '50px', display: 'flex', alignItems: 'center' }}>
              考评文件：
              <div className="common-button2" style={{ width: '156px', height: '30px' }} onClick={this.uploadFile}>
                <Iconfont type="icon-yunshangchuan" style={{ fontSize: '20px' }} />
                <div className="common-text2">点击选择文件</div>
              </div>
              <div
                style={{
                  paddingLeft: '10px',
                  maxWidth: 'calc(100% - 230px)',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap'
                }}
              >
                {this.state.file?.name}
              </div>
            </div>
            <div style={{ display: 'flex', justifyContent: 'end', marginTop: '20px' }}>
              <Button
                className="tr-btn"
                style={{ marginRight: '12px' }}
                onClick={() => this.setState({ isModalVisible: false })}
              >
                取消
              </Button>
              <Button
                type="primary"
                className="tr-btn"
                onClick={() => {
                  this.handleOk()
                }}
              >
                确定
              </Button>
            </div>
          </div>
        </ModalInfo>
      </div>
    )
  }
}
