import CryptoJS from 'crypto-js'
import JSEncrypt from 'jsencrypt'

// 十六位十六进制数作为密钥
const key = CryptoJS.enc.Utf8.parse('nonce_sign_12345')
// 十六位十六进制数作为密钥偏移量
const iv = CryptoJS.enc.Utf8.parse('qcMtNqJQVSds2h21')

const publicKey = `
-----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCpmsv7XmmuHyg2EXYtLTDpPsUb
2Jmz932OScTQdWZRpsQ/Y6LrMJ7PYFqI9WrTfO5canfjlSKth8rdonhbeU782Niw
2LBUaNHdjXMAWF6lEyK5QJmZKDQ4XAiwl7EdUd2U3ykBJ89HExTmTm91ipEA3cTJ
NlW5JXCYSVWoNL8uLQIDAQAB
-----E<PERSON> PUBLIC KEY-----
`

export function EncryptAes(word: string) {
  let encrypted = CryptoJS.AES.encrypt(word, key, { iv: iv, mode: CryptoJS.mode.CBC, padding: CryptoJS.pad.Pkcs7 })
  return encrypted.toString()
}

export function EncryptRSA(words: string): string {
  if (!words) return ''
  var encryptor = new JSEncrypt()
  encryptor.setPublicKey(publicKey)
  return encryptor.encrypt(words) || ''
}
