.ant-menu-dark .ant-menu-submenu-selected {
  background: transparent !important;
}

.ant-menu-dark .ant-menu-submenu-selected > div {
  background: rgba(0, 0, 0, 0.24) !important;
}

.ant-menu-sub.ant-menu-inline > .ant-menu-item,
.ant-menu-sub.ant-menu-inline > .ant-menu-submenu > .ant-menu-submenu-title {
  height: 45px !important;
  line-height: 45px !important;
}

.ant-menu .ant-menu-sub,
.ant-menu.ant-menu .ant-menu-sub {
  background: transparent !important;
}
.ant-menu .ant-menu-item,
.ant-menu .ant-menu-item-group-title,
.ant-layout-sider-children .ant-menu .ant-menu-item > a,
.ant-layout-sider-children .ant-menu .ant-menu-item > span > a {
  color: var(--gray-color1);
}

.ant-menu-dark .ant-menu-item:hover {
  > a {
    color: #fff !important;
  }
  background: linear-gradient(180deg, #1a74e2, #11509d) !important;
  color: #fff !important;
}
.tr-menu {
  height: 100%;

  border-radius: 10px;
  background: url('@/assets/images/cebianpeitu.png') no-repeat !important;
  background-repeat: no-repeat;
  background-position-y: bottom !important;
  background-size: 100% 100% !important;
  padding: 10px 0 !important;
  .ant-menu-inline .ant-menu-item {
    padding-left: 0 !important;
  }
  .ant-menu-submenu-arrow {
    display: none;
  }

  .ant-menu-submenu-title {
    text-align: center;
    height: 130px !important;
    display: block !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
  }
  .ant-menu-title-content {
    display: block;
    color: var(--gray-color1) !important;
    margin-left: 0 !important;
  }
}
.ant-layout-sider-children .ant-menu .ant-menu-inline .ant-menu-item {
  padding-left: 0 !important;
  padding-right: 0 !important;
  text-align: center;
}

.ant-menu.ant-menu:not(.ant-menu-horizontal) .ant-menu-item-selected a,
.ant-menu.ant-menu:not(.ant-menu-horizontal) .ant-menu-item-selected svg {
  color: #fff !important;
}

.ant-menu-dark.ant-menu-dark:not(.ant-menu-horizontal) .ant-menu-item-selected {
  color: #fff !important;
  background: linear-gradient(180deg, #1a74e2, #11509d) !important;
}

.ant-menu-item-only-child > .ant-menu-title-content > a {
  font-size: 15px !important;
}
