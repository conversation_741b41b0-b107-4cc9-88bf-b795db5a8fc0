import './message.scss'

const MessageSelf = (content: string, type?: string, time?: number) => {
  // 销毁超出显示数量的消息 最多10条
  let arr = document.getElementsByClassName('ui-message-bg')
  if (arr.length === 10) {
    try {
      window.document.body.removeChild(arr[0])
    } catch (error) {}
  }
  //消息类型
  type = type === undefined ? 'default' : type
  // 生成消息
  let obj = document.createElement('div')
  let achilddv = document.createElement('div')
  let errorSvg = `
  <svg viewBox="64 64 896 896" focusable="false" data-icon="close-circle" width="1em" height="1em" fill="currentColor" aria-hidden="true"><path d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm165.4 618.2l-66-.3L512 563.4l-99.3 118.4-66.1.3c-4.4 0-8-3.5-8-8 0-1.9.7-3.7 1.9-5.2l130.1-155L340.5 359a8.32 8.32 0 01-1.9-5.2c0-4.4 3.6-8 8-8l66.1.3L512 464.6l99.3-118.4 66-.3c4.4 0 8 3.5 8 8 0 1.9-.7 3.7-1.9 5.2L553.5 514l130 155c1.2 1.5 1.9 3.3 1.9 5.2 0 4.4-3.6 8-8 8z"></path></svg>
  `
  let successSvg = `
  <svg viewBox="64 64 896 896" focusable="false" data-icon="check-circle" width="1em" height="1em" fill="currentColor" aria-hidden="true"><path d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm193.5 301.7l-210.6 292a31.8 31.8 0 01-51.7 0L318.5 484.9c-3.8-5.3 0-12.7 6.5-12.7h46.9c10.2 0 19.9 4.9 25.9 13.3l71.2 98.8 157.2-218c6-8.3 15.6-13.3 25.9-13.3H699c6.5 0 10.3 7.4 6.5 12.7z"></path></svg>
  `
  let warningSvg = `
  <svg viewBox="64 64 896 896" focusable="false" data-icon="exclamation-circle" width="1em" height="1em" fill="currentColor" aria-hidden="true"><path d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm-32 232c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v272c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V296zm32 440a48.01 48.01 0 010-96 48.01 48.01 0 010 96z"></path></svg>
  `
  // 判定类型
  switch (type) {
    case 'default':
      achilddv.innerHTML = content
      achilddv.className = 'ui-message-content'
      break
    case 'error':
      achilddv.className = 'ui-message-content ui-message-content-error'
      achilddv.innerHTML = `
      ${errorSvg}
      <span> ${content}</span>`
      break
    case 'success':
      achilddv.className = 'ui-message-content ui-message-content-success'
      achilddv.innerHTML = `
      ${successSvg}
      <span> ${content}</span>`
      break
    case 'warning':
      achilddv.className = 'ui-message-content ui-message-content-warning'
      achilddv.innerHTML = `
      ${warningSvg}
      <span> ${content}</span>`
      break
  }
  obj.className = 'ui-message-bg'

  window.document.body.appendChild(obj)
  obj.appendChild(achilddv)
  if (time === undefined || time === null) time = 3000
  let maxTime = time + 500
  // 使用500毫秒产生动画
  setTimeout(() => {
    obj.style.cssText = 'animation-name: message-hidden;'
  }, time)
  // 销毁组件
  setTimeout(() => {
    try {
      window.document.body.removeChild(obj)
    } catch (error) {}
  }, maxTime)
}

export default MessageSelf
