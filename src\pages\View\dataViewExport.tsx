import React from 'react'
import DateMenu from '@/components/DateMenu'
import { getDefaultSelectedMonth, getLeafKey, getTabByKey } from '@/utils/utils'
import { Iconfont } from 'tr-cheers'
import KeyPerformPlanFill from './subView/keyPerformPlanFill'
import PerformIndicatAssessFill from './subView/performIndicatAssessFill'
import ProfessEvaluationFill from './subView/professEvaluationFill'
import SpecialAssessmentFill from './subView/specialAssessmentFill'
import SpecialEventRewards from './subView/specialEventRewards'
import UploadSupportMaterials from './subView/uploadSupportMaterials'
import TemporaryUnconventional from './subView/temporaryUnconventional'
import { Modal } from 'antd'
import { getExportMonthWord, getMenuDate } from '@/api'
import { LoadingContext } from '@/components/load/loadingProvider'
import { withRouter } from 'tr-cheers'

@withRouter
export default class extends React.Component {
  static contextType = LoadingContext
  context: React.ContextType<typeof LoadingContext>

  state = {
    dateId: '', //时间
    //叶子节点
    leafKey: '',
    leftMenu: [],
    //Tab类型
    tabType: '1',
    tabs: [],
    //状态
    status: '01',
    //全局搜索
    globalSearch: { dateId: '', tab: '' }
  }

  componentDidMount(): void {
    //获取url参数（用于全局搜索跳转）
    let globalSearch = (this.props as any).location.state
    if (globalSearch?.dateId) {
      //获取tabType
      let tab = getTabByKey(globalSearch.tab)
      this.setState({ globalSearch, tabType: tab.id }, () => {
        //获取左侧菜单
        this.getLeftMenu()
      })
    } else {
      //获取左侧菜单
      this.getLeftMenu()
    }
    //获取tabs
    this.getTabs()
  }

  getTabs = () => {
    let tabs = [
      {
        id: '1',
        title: '重点绩效计划'
      },
      { id: '6', title: '临时性非常规工作' },
      { id: '2', title: '业绩指标考核' },
      { id: '3', title: '专业评价' },
      { id: '4', title: '专项考核' },
      { id: '7', title: '特殊事项奖励' },
      { id: '5', title: '佐证材料' }
    ]
    this.setState({ tabs })
  }

  getLeftMenu() {
    this.context.showLoading()
    getMenuDate()
      .then((res: any) => {
        if (res.data && res.data.length > 0) {
          let leftMenu = res.data
          //如果有全局搜索参数，说明是从其他页面跳转过来的
          if (this.state.globalSearch?.dateId) {
            //设置全局搜索参数
            this.setState({
              leftMenu: leftMenu,
              dateId: this.state.globalSearch.dateId,
              leafKey: this.state.globalSearch?.dateId
            })
            return
          }

          //设置dateId和叶子节点
          let leaf = null
          for (let i = 0; i < leftMenu.length; i++) {
            if (!leaf) {
              leaf = getLeafKey(leftMenu[i], true)
            } else {
              break
            }
          }
          this.setState({ leftMenu: leftMenu, leafKey: leaf.dateId, dateId: leaf.dateId })
        }
        this.context.hideLoading()
      })
      .catch(() => {
        this.context.hideLoading()
      })
  }

  //菜单树点击
  handleMenu = (e) => {
    ;(this.props as any).navigate('/dataViewExport', {
      state: null
    })
    this.setState({ dateId: e.key, leafKey: e.key })
  }

  //切换tab
  handleTab = (e) => {
    this.setState({ tabType: e.id })
  }

  exportData = () => {
    Modal.confirm({
      title: '导出绩效报告',
      content: '是否导出绩效报告？',
      onOk: () => {
        this.context.showLoading()
        getExportMonthWord({ date: this.state.dateId })
          .then((res) => {
            this.context.hideLoading()
          })
          .catch(() => {
            this.context.hideLoading()
          })
      }
    })
  }

  render() {
    return (
      <div className="main-common">
        <div className="main-left">
          {this.state.dateId && (
            <DateMenu
              menuList={this.state.leftMenu}
              dateId={this.state.dateId}
              leafKey={this.state.leafKey}
              onClick={(e) => this.handleMenu(e)}
            />
          )}
        </div>

        <div className="main-right">
          <div className="content-top" style={{ justifyContent: 'space-between' }}>
            <div style={{ display: 'flex' }}>
              {this.state.tabs.map((item) => (
                <div
                  key={item.id}
                  className={`common-tab-button ${this.state.tabType == item.id && 'common-tab-button-selected'}`}
                  style={{ marginRight: '10px' }}
                  onClick={() => this.handleTab(item)}
                >
                  {item.title}
                </div>
              ))}
            </div>
            <div style={{ display: 'flex' }}>
              <div
                className={`common-button2 ${this.state.status != '01' && 'disabled-div'}`}
                style={{ width: '155px' }}
                onClick={() => this.exportData()}
              >
                <Iconfont type="icon-download" style={{ fontSize: '22px' }} />
                <div className="common-text">
                  <span style={{ letterSpacing: '0px' }}>导出绩效报告</span>
                </div>
              </div>
            </div>
          </div>
          {this.state.leafKey && (
            <div className="content-center">
              <div style={{ height: '100%', display: this.state.tabType == '1' ? 'block' : 'none' }}>
                <KeyPerformPlanFill leafKey={this.state.leafKey} />
              </div>
              <div style={{ height: '100%', display: this.state.tabType == '2' ? 'block' : 'none' }}>
                <PerformIndicatAssessFill leafKey={this.state.leafKey} />
              </div>
              <div style={{ height: '100%', display: this.state.tabType == '3' ? 'block' : 'none' }}>
                <ProfessEvaluationFill leafKey={this.state.leafKey} globalSearch={this.state.globalSearch} />
              </div>
              <div style={{ height: '100%', display: this.state.tabType == '4' ? 'block' : 'none' }}>
                <SpecialAssessmentFill leafKey={this.state.leafKey} globalSearch={this.state.globalSearch} />
              </div>
              <div style={{ height: '100%', display: this.state.tabType == '5' ? 'block' : 'none' }}>
                <UploadSupportMaterials leafKey={this.state.leafKey} />
              </div>
              <div style={{ height: '100%', display: this.state.tabType == '6' ? 'block' : 'none' }}>
                <TemporaryUnconventional leafKey={this.state.leafKey} />
              </div>
              <div style={{ height: '100%', display: this.state.tabType == '7' ? 'block' : 'none' }}>
                <SpecialEventRewards leafKey={this.state.leafKey} />
              </div>
            </div>
          )}
        </div>
      </div>
    )
  }
}
