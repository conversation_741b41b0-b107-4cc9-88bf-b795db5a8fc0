import { getKeyCountyKpiDetails, getKeyPerformanceIndicatorstsDetails } from '@/api'
import React from 'react'
import { LoadingContext } from '@/components/load/loadingProvider'

interface IProps {
  //选中的是哪个节点
  leafKey: string
}

// 定义表格数据接口
interface PerformanceData {
  indicatorName: string
  evaluationDepartment: string | null
  baseScore: number
  licheng: number
  zhangqiu: number
  changqing: number
  pingyin: number
  jiyang: number
  shanghe: number
}

export default class extends React.Component<IProps> {
  static contextType = LoadingContext
  context: React.ContextType<typeof LoadingContext>

  state = {
    //table数据
    keyList: [],
    performanceData: [] as PerformanceData[],
    dateId: ''
  }

  componentDidMount(): void {
    this.setState(
      {
        dateId: this.props.leafKey
      },
      () => {
        // 获取列表数据
        this.getKeyList()
      }
    )
  }

  componentDidUpdate(prevProps: Readonly<IProps>, prevState: Readonly<{}>, snapshot?: any): void {
    if (this.props.leafKey != prevProps.leafKey) {
      this.setState({ dateId: this.props.leafKey }, () => {
        // 获取列表数据
        this.getKeyList()
      })
    }
  }

  getKeyList = () => {
    if (this.state.dateId) {
      this.context.showLoading()
      // this.state.dateId.split('-')[0] 是截取年份
      getKeyPerformanceIndicatorstsDetails({ dateYear: this.state.dateId.split('-')[0] })
        .then((res) => {
          // console.log('获取县公司关键业绩指标得分情况表', res)
          this.setState({ keyList: res.data.list })
          this.context.hideLoading()
        })
        .catch(() => {
          this.context.hideLoading()
        })
      // 县公司各项关键业绩指标年度得分情况表
      getKeyCountyKpiDetails({ dateYear: this.state.dateId.split('-')[0] })
        .then((res) => {
          // console.log('获取县公司各项关键业绩指标年度得分情况表', res)
          this.setState({ performanceData: res.data.list })
          this.context.hideLoading()
        })
        .catch(() => {
          this.context.hideLoading()
        })
    }
  }

  // 计算部门列的rowspan
  calculateDepartmentRowspan = (data: PerformanceData[], currentIndex: number): number => {
    const currentDepartment = data[currentIndex].evaluationDepartment
    if (!currentDepartment) return 1

    let rowspan = 1
    for (let i = currentIndex + 1; i < data.length; i++) {
      if (data[i].evaluationDepartment === currentDepartment) {
        rowspan++
      } else {
        break
      }
    }
    return rowspan
  }

  // 判断是否应该渲染部门单元格
  shouldRenderDepartmentCell = (data: PerformanceData[], currentIndex: number): boolean => {
    if (currentIndex === 0) return true

    const currentDepartment = data[currentIndex].evaluationDepartment
    const previousDepartment = data[currentIndex - 1].evaluationDepartment

    return currentDepartment !== previousDepartment
  }

  render(): React.ReactNode {
    const { performanceData } = this.state

    return (
      <div style={{ height: 'calc(100% - 10px)' }}>
        <div className="div-table" style={{ height: 'calc(100vh - 250px)', overflow: 'auto' }}>
          <h3 style={{ fontWeight: 700, fontSize: '14px', margin: '10px 0' }}>县公司关键业绩指标得分情况表</h3>
          <table className="common-table" style={{ tableLayout: 'auto', minWidth: '100%', marginBottom: '25px' }}>
            <thead>
              <tr>
                <th>单位</th>
                <th>关键业绩指标</th>
                <th>年度（70%）</th>
                <th>月度（30%）</th>
              </tr>
            </thead>
            <tbody>
              {this.state.keyList.length > 0 ? (
                this.state.keyList.map((item: any, index: number) => (
                  // 长文本列使用 whiteSpace: 'pre-wrap' 和 wordBreak: 'break-word' 确保文本换行显示
                  // 部门列使用 whiteSpace: 'nowrap' 保持单行显示
                  <tr key={index}>
                    <td>{item.unit}</td>
                    <td>{item.keyPerformanceIndicator ? Number(item.keyPerformanceIndicator).toFixed(2) : '0.00'}</td>
                    <td>{item.annualScore ? Number(item.annualScore).toFixed(2) : '0.00'}</td>
                    <td>{item.monthlyScore ? Number(item.monthlyScore).toFixed(2) : '0.00'}</td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={4} style={{ textAlign: 'center', padding: '20px' }}>
                    暂无数据
                  </td>
                </tr>
              )}
            </tbody>
          </table>

          <h3 style={{ fontWeight: 700, fontSize: '14px', margin: '10px 0' }}>县公司各项关键业绩指标年度得分情况表</h3>
          <table className="common-table" style={{ tableLayout: 'auto', minWidth: '100%', marginBottom: '55px' }}>
            <thead>
              <tr>
                <th style={{ minWidth: '200px' }}>指标名称</th>
                <th style={{ minWidth: '120px' }}>评价部门</th>
                <th style={{ minWidth: '80px' }}>基础分值</th>
                <th style={{ minWidth: '80px' }}>历城</th>
                <th style={{ minWidth: '80px' }}>章丘</th>
                <th style={{ minWidth: '80px' }}>长清</th>
                <th style={{ minWidth: '80px' }}>平阴</th>
                <th style={{ minWidth: '80px' }}>济阳</th>
                <th style={{ minWidth: '80px' }}>商河</th>
              </tr>
            </thead>
            <tbody>
              {performanceData.length > 0 ? (
                performanceData.map((item: PerformanceData, index: number) => (
                  <tr key={index}>
                    {item.indicatorName === '总分' ? (
                      <td colSpan={2} style={{ textAlign: 'center' }}>
                        总分
                      </td>
                    ) : (
                      <>
                        <td>{item.indicatorName}</td>
                        {this.shouldRenderDepartmentCell(performanceData, index) ? (
                          <td rowSpan={this.calculateDepartmentRowspan(performanceData, index)}>
                            {item.evaluationDepartment || ''}
                          </td>
                        ) : null}
                      </>
                    )}
                    <td>{item.baseScore}</td>
                    <td>{item.licheng ? Number(item.licheng).toFixed(2) : '0.00'}</td>
                    <td>{item.zhangqiu ? Number(item.zhangqiu).toFixed(2) : '0.00'}</td>
                    <td>{item.changqing ? Number(item.changqing).toFixed(2) : '0.00'}</td>
                    <td>{item.pingyin ? Number(item.pingyin).toFixed(2) : '0.00'}</td>
                    <td>{item.jiyang ? Number(item.jiyang).toFixed(2) : '0.00'}</td>
                    <td>{item.shanghe ? Number(item.shanghe).toFixed(2) : '0.00'}</td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={9} style={{ textAlign: 'center', padding: '20px' }}>
                    暂无数据
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    )
  }
}
