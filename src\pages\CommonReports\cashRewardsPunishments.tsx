import React from 'react'
import { getNumber, getUUID, getOrgNames } from '@/utils/utils'
import { Iconfont } from 'tr-cheers'
import { Select, Input, InputNumber, Modal, Checkbox } from 'antd'
import MessageSelf from '@/components/message'
import {
  deleteCashAssessReport,
  getCashAssessReportList,
  getDictListByName,
  getOrgList,
  pdasSpecialCashAssessOrgPinned,
  pdasSpecialCashAssessOrgSwap,
  pdasSpecialCashAssessReportPinned,
  pdasSpecialCashAssessReportSwap,
  saveCashAssessReport
} from '@/api'
import { LoadingContext } from '@/components/load/loadingProvider'
const { TextArea } = Input

interface IProps {
  //选中的是哪个节点
  leafKey: string
  //是否可见，true可见 false不可见
  isVisible?: boolean
  //权限 view 只能查看
  permission?: string
  //组织机构id
  orgId?: string
  showLoading?: () => void
  hideLoading?: () => void
}
export default class extends React.Component<IProps> {
  static contextType = LoadingContext
  context: React.ContextType<typeof LoadingContext>
  intervalId = null // 在这里定义 intervalId
  //保存前数据
  preKeyList: any = []

  state = {
    dateId: '',
    menuType: '',
    //考评项目列表
    kpxmList: [],
    //表格列表
    cashList: [],
    //已选复选框
    selectedRows: [],
    //部门list
    bmList: [],
    bmActiveList: []
  }

  componentDidMount(): void {
    if (this.props.leafKey.includes('-')) {
      //将其拆分
      let datas = this.props.leafKey.split('-')
      this.setState({ dateId: datas[0], menuType: datas[1] }, () => {
        //获取列表数据
        this.getCashList()
      })
      //获取部门list
      this.getBmList()
      //获取考评项目列表
      this.getKpxmList()
    }
    // 每隔2分钟（120000毫秒）执行
    this.intervalId = setInterval(this.saveAll, 130000)
  }

  componentWillUnmount() {
    // 清除定时器
    clearInterval(this.intervalId)
  }

  //定时保存
  saveAll = () => {
    let list = [...this.state.cashList]
    for (let i = 0; i < list.length; i++) {
      let item = list[i]
      if (item.isEdit) {
        if (JSON.stringify(this.preKeyList[i]) !== JSON.stringify(item)) {
          //保存大行
          this.handleSaveParent(i, 'regular')
        }
      } else {
        let smallList = item.allLineList
        let smallEdit = false
        for (let j = 0; j < smallList.length; j++) {
          let smallItem = smallList[j]
          if (smallItem.isSmallEdit) {
            smallEdit = true
            break
          }
        }

        if (smallEdit && JSON.stringify(this.preKeyList[i]) !== JSON.stringify(item)) {
          this.handleSaveParent(i, 'regular')
        }
      }
    }
    //记录当前数据
    this.preKeyList = JSON.parse(JSON.stringify(list))
  }

  componentDidUpdate(prevProps: Readonly<IProps>, prevState: Readonly<{}>, snapshot?: any): void {
    if (prevProps.leafKey != this.props.leafKey) {
      if (this.props.leafKey.includes('-')) {
        //将其拆分
        let datas = this.props.leafKey.split('-')
        this.setState({ dateId: datas[0], menuType: datas[1] }, () => {
          //获取列表数据
          this.getCashList()
        })
      }
    }
  }

  getKpxmList = () => {
    getDictListByName({ categoryType: '专项现金奖惩', type: '考评项目', orgId: this.props.orgId }).then((res) => {
      if (res?.data?.list?.length > 0) {
        this.setState({
          kpxmList: res.data.list
        })
      }
    })
  }

  //提交前校验
  submitYz = () => {
    //默认填写完成
    let flag = true
    //判断是否有正在填写的内容
    let list = this.state.cashList

    for (let i = 0; i < list.length; i++) {
      let item = list[i]
      if (item.isEdit) {
        flag = false
        Modal.warning({
          title: '提示',
          content: `【专项考核填报--现金奖惩】中，第${i + 1}行“考评项目、考评内容、考评依据”需填写完成，并保存！`,
          zIndex: 1100
        })
        break
      }
      let bmqkList = list[i].allLineList
      for (let j = 0; j < bmqkList.length; j++) {
        let bmqkItem = bmqkList[j]
        if (bmqkItem.isSmallEdit) {
          flag = false
          Modal.warning({
            title: '提示',
            content: `【专项考核填报--现金奖惩】中，第${i + 1}行"部门名称、金额"需填写完成，并保存！`,
            zIndex: 1100
          })
          break
        }
        //人员和分数同时填写或者同时不填写
        if (
          (bmqkItem.personnelNames && !bmqkItem.personnelCount) ||
          (!bmqkItem.personnelNames && bmqkItem.personnelCount)
        ) {
          flag = false
          Modal.warning({
            title: '提示',
            content: `【专项考核填报--现金奖惩】中，第${i + 1}行"人员和份数"需同时填写或不填写！`,
            zIndex: 1100
          })
          return
        }
      }
      if (!flag) break
    }
    return flag
  }

  getCashList = () => {
    let params: any = {
      date: this.state.dateId,
      orgId: this.props.orgId
    }
    if (this.props.permission == 'view') {
      params = {
        ...params,
        status: '1',
        source: 'audit'
      }
    }
    this.context.showLoading()
    getCashAssessReportList(params)
      .then((res) => {
        if (res?.data?.list?.length > 0) {
          //设置部门
          for (let i = 0; i < res.data.list.length; i++) {
            let obj = res.data.list[i]
            if (obj.assessItem && obj.assessContent) {
              obj.isEdit = false
            } else {
              obj.isEdit = true
            }

            if (!obj.allLineList || obj.allLineList.length == 0 || !obj.allLineList[0].amount) {
              //初始化一行
              obj.allLineList = [
                {
                  orgIds: [],
                  amount: '',
                  personnelNames: '',
                  personnelCount: '',
                  isSmallEdit: true
                }
              ]
              obj.hj = 0
            } else {
              for (let j = 0; j < obj.allLineList.length; j++) {
                let orgIds = []
                let lineList = obj.allLineList[j].lineList
                for (let k = 0; k < lineList.length; k++) {
                  let line = lineList[k]
                  orgIds.push(line.orgId)
                }
                obj.allLineList[j].orgIds = orgIds
                obj.allLineList[j].isSmallEdit = obj.allLineList[j].amount ? false : true
              }
              //获取考评依据
              if (!obj.reason) {
                obj.reason = obj.allLineList[0].reason
              }
              //设置合计
              let count = this.getHj(i, res.data.list)
              obj.hj = count
            }
          }
          this.setState({ cashList: res.data.list })
        } else {
          this.setState({ cashList: [] })
        }
      })
      .finally(() => {
        this.context.hideLoading()
      })
  }

  getBmList = () => {
    getOrgList('01,02,03').then((res: any) => {
      if (res?.data?.list?.length > 0) {
        //根据返回数据是否设置有效的部门列表 isActive:"1" 为有效
        let bmActiveList = []
        for (let i = 0; i < res?.data?.list?.length; i++) {
          let item = res.data.list[i]
          if (item.isActive == '1') {
            bmActiveList.push(item)
          }
        }
        this.setState({ bmList: res.data.list, bmActiveList: bmActiveList })
      }
    })
  }

  //新增一行（大行）
  handleAdd = () => {
    let list = [...this.state.cashList]
    for (let i = 0; i < list.length; i++) {
      let obj = list[i]
      if (obj.isEdit) {
        Modal.warning({
          title: '提示',
          content: '有正在编辑的内容，请完成后，再新增一行！',
          zIndex: 1100
        })
        return
      }
    }
    list.push({
      pscarId: '',
      assessItem: '',
      assessContent: '',
      reason: '',
      hj: 0,
      isEdit: true,
      //临时id
      cacheId: getUUID(),
      allLineList: [
        {
          orgIds: [],
          amount: '',
          personnelNames: '',
          personnelCount: '',
          isSmallEdit: true
        }
      ]
    })
    this.setState({ cashList: list })
  }

  //批量删除行
  handleDelete = () => {
    let _this = this
    Modal.confirm({
      title: '删除提示',
      content: '确定删除吗?',
      onOk: () => {
        //调用删除接口
        _this.context.showLoading()
        let ids = this.state.selectedRows.join(',')
        deleteCashAssessReport(ids)
          .then((res: any) => {
            MessageSelf('删除成功！', 'success')
            //删除成功后，刷新列表
            if (res?.code == '0') {
              _this.getCashList()
              _this.setState({ selectedRows: [] })
            }
            _this.context.hideLoading()
          })
          .catch(() => {
            _this.context.hideLoading()
          })
      }
    })
  }

  //全选，全不选
  onCheckAllChange = () => {
    let selectedRows = []
    if (this.state.cashList.length > 0) {
      if (this.state.cashList.length != this.state.selectedRows.length) {
        //全选
        let list = this.state.cashList
        list.map((item) => {
          selectedRows.push(item.pscarId || item.cacheId)
        })
      }
    }
    this.setState({
      selectedRows: selectedRows
    })
  }

  //复选框
  handleCheckBox = (value, key) => {
    if (value) {
      //true
      let list = [...this.state.selectedRows]
      list.push(key)
      this.setState({ selectedRows: list })
    } else {
      //false
      let list = [...this.state.selectedRows]
      let index = list.indexOf(key)
      if (index > -1) {
        list.splice(index, 1)
      }
      this.setState({ selectedRows: list })
    }
  }

  handleContent = (value, index, tdName, indexSmall?: number) => {
    let list = [...this.state.cashList]
    if (indexSmall || indexSmall == 0) {
      list[index].allLineList[indexSmall][tdName] = value
    } else {
      list[index][tdName] = value
    }

    //如果部门和金额，则需要计算合计
    if (tdName === 'orgIds' || tdName === 'amount' || tdName === 'personnelCount') {
      let count = this.getHj(index, list)
      list[index].hj = count
    }
    this.setState({ cashList: list })
  }

  //计算合计
  getHj = (index, list) => {
    let count = 0
    let bmqkList = list[index].allLineList
    for (let i = 0; i < bmqkList.length; i++) {
      let obj = bmqkList[i]
      if (obj.personnelCount) {
        count = count + obj.orgIds.length * obj.personnelCount * obj.amount
      } else {
        count = count + obj.orgIds.length * obj.amount
      }
    }
    return getNumber(count)
  }

  //保存大行
  handleSaveParent = (index, type?: string) => {
    //校验
    let item = this.state.cashList[index]
    if (!item.assessItem) {
      if (!type) {
        Modal.warning({
          title: '提示',
          content: '请选择考评项目！',
          zIndex: 1100
        })
      }

      return
    }
    if (!item.assessContent.trim()) {
      if (!type) {
        Modal.warning({
          title: '提示',
          content: '请填写考评内容！',
          zIndex: 1100
        })
      }

      return
    }
    if (!item.reason.trim()) {
      if (!type) {
        Modal.warning({
          title: '提示',
          content: '请填写考评依据！',
          zIndex: 1100
        })
      }

      return
    }

    if (type) {
      let smallList = item.allLineList
      for (let i = 0; i < smallList?.length; i++) {
        let obj = smallList[i]
        if (obj.orgIds.length <= 0) {
          return
        }
        if (!obj.amount) {
          return
        }
        //人员和分数同时填写或者同时不填写
        if ((obj.personnelNames && !obj.personnelCount) || (!obj.personnelNames && obj.personnelCount)) {
          return
        }
      }
    }
    //调用保存接口
    let data = this.setSaveParam(item, index)
    if (!type) {
      this.context.showLoading()
    }

    //接口返回数据
    saveCashAssessReport(data)
      .then((res) => {
        if (res?.data) {
          if (!type) {
            MessageSelf('保存成功！', 'success')
          }
          let list = [...this.state.cashList]
          list[index].pprId = res.data.pprId
          list[index].pscarId = res.data.pscarId
          list[index].sort = res.data.sort
          let smallList = list[index].allLineList
          for (let i = 0; i < smallList.length; i++) {
            smallList[i].rowIdx = res.data.allLineList[i]?.rowIdx
          }
          if (!type) {
            list[index].isEdit = false
          }

          this.setState({ cashList: list })
        } else {
          if (!type) {
            this.getCashList()
          }
        }
      })
      .finally(() => {
        if (!type) {
          this.context.hideLoading()
        }
      })
  }

  //编辑大行
  handleEditParent = (index) => {
    let list = [...this.state.cashList]
    list[index].isEdit = true
    this.setState({ cashList: list })
  }

  //设置保存时的参数
  setSaveParam = (obj, index) => {
    let allLineList = obj.allLineList
    let saveList = []
    for (let i = 0; i < allLineList.length; i++) {
      let item = allLineList[i]
      let lineList = []
      item.orgIds.forEach((org) => {
        lineList.push({
          orgId: org
        })
      })
      saveList.push({
        amount: item.amount,
        personnelNames: item.personnelNames,
        personnelCount: item.personnelCount,
        reason: obj.reason,
        lineList: lineList
      })
    }
    let data = {
      date: this.state.dateId,
      pscarId: obj.pscarId,
      pprId: obj.pprId,
      assessItem: obj.assessItem,
      assessContent: obj.assessContent,
      reason: obj.reason,
      sort: index == 0 ? obj.sort || 1 : parseInt(this.state.cashList[index - 1].sort + 1),
      allLineList: saveList
    }
    return data
  }

  //保存一行
  handleSaveSmall = (index, indexSmall) => {
    //校验
    let obj = this.state.cashList[index].allLineList[indexSmall]
    if (obj.orgIds.length <= 0) {
      Modal.warning({
        title: '提示',
        content: '请选择部门名称！',
        zIndex: 1100
      })
      return
    }
    if (!obj.amount) {
      Modal.warning({
        title: '提示',
        content: '请输入金额！',
        zIndex: 1100
      })
      return
    }

    //人员和分数同时填写或者同时不填写
    if ((obj.personnelNames && !obj.personnelCount) || (!obj.personnelNames && obj.personnelCount)) {
      Modal.warning({
        title: '提示',
        content: '请填写人员和份数',
        zIndex: 1100
      })
      return
    }

    //调用保存接口
    //获取保存参数
    let data = this.setSaveParam(this.state.cashList[index], index)
    this.context.showLoading()
    //接口返回数据
    saveCashAssessReport(data)
      .then((res) => {
        if (res?.data) {
          MessageSelf('保存成功！', 'success')
          let list = [...this.state.cashList]
          list[index].pprId = res.data.pprId
          list[index].pscarId = res.data.pscarId
          list[index].sort = res.data.sort
          let smallList = list[index].allLineList
          for (let i = 0; i < smallList.length; i++) {
            smallList[i].rowIdx = res.data.allLineList[i]?.rowIdx
          }
          list[index].allLineList[indexSmall].isSmallEdit = false
          this.setState({ cashList: list })
        } else {
          this.getCashList()
        }
        this.context.hideLoading()
      })
      .catch(() => {
        this.context.hideLoading()
      })
  }

  //编辑
  handleEditSmall = (index, indexSmall) => {
    let list = [...this.state.cashList]
    list[index]['allLineList'][indexSmall].isSmallEdit = true
    this.setState({ cashList: list })
  }

  //删除
  handleDeleteSmall = (index, indexSmall) => {
    let _this = this
    Modal.confirm({
      title: '删除提示',
      content: '确定删除吗?',
      onOk: () => {
        let list = [..._this.state.cashList]
        list[index]['allLineList'].splice(indexSmall, 1)
        let data = _this.setSaveParam(list[index], index)
        _this.context.showLoading()
        //接口返回数据
        saveCashAssessReport(data)
          .then((res) => {
            if (res?.data) {
              MessageSelf('删除成功！', 'success')

              //重新计算合计
              let count = _this.getHj(index, list)
              list[index].hj = count
              _this.setState({ cashList: list })
            } else {
              _this.getCashList()
            }
            _this.context.hideLoading()
          })
          .catch(() => {
            _this.context.hideLoading()
          })
      }
    })
  }

  //新增一小行
  handleAddSmall = (index) => {
    //判断有无正在编辑的内容，如果有，不能进行编辑，提示：有正在编辑的内容，请完成后，再新增一行
    let flag = false //默认没有编辑的
    let yyList = this.state.cashList[index].allLineList
    for (let i = 0; i < yyList.length; i++) {
      let obj = yyList[i]
      if (obj.isSmallEdit) {
        flag = true
        break
      }
    }
    if (flag) {
      Modal.warning({
        title: '提示',
        content: '有正在编辑的内容，请完成后，再新增一行！',
        zIndex: 1100
      })
      return
    }
    let list = [...this.state.cashList]
    list[index].allLineList.push({
      orgIds: [],
      amount: '',
      personnelNames: '',
      personnelCount: '',
      isSmallEdit: true
    })
    this.setState({ cashList: list })
  }

  //移动
  handleMove = (index, type) => {
    let list = [...this.state.cashList]
    if (type == 'up') {
      if (index == 0) {
        return
      }
      let temp = list[index]
      let temp2 = list[index - 1]
      if (!temp2?.pscarId || !temp?.pscarId) {
        return
      }
      list[index] = list[index - 1]
      list[index - 1] = temp
      this.saveSwap(temp2.pscarId, temp.pscarId)
    } else if (type == 'down') {
      if (index == list.length - 1) {
        return
      }
      let temp = list[index]
      let temp2 = list[index + 1]
      if (!temp2?.pscarId || !temp?.pscarId) {
        return
      }
      list[index] = list[index + 1]
      list[index + 1] = temp
      this.saveSwap(temp2.pscarId, temp.pscarId)
    } else if (type == 'top') {
      if (index == 0) {
        return
      }
      //置顶
      let temp = list[index]
      if (!temp?.pscarId) {
        return
      }
      list.splice(index, 1)
      list.unshift(temp)
      this.savePinned(temp.pscarId)
    } else if (type == 'bottom') {
      //暂时无置底功能
      if (index == list.length - 1) {
        return
      }
      //置底
      let temp = list[index]
      list.splice(index, 1)
      list.push(temp)
      // this.savePinned(temp.id, '2')
    }
    this.setState({ cashList: list })
  }

  saveSwap = (id1, id2) => {
    this.context.showLoading()
    pdasSpecialCashAssessReportSwap({ id1: id1, id2: id2 })
      .then((res) => {
        this.getCashList()
      })
      .finally(() => {
        this.context.hideLoading()
      })
  }

  savePinned = (id) => {
    this.context.showLoading()
    pdasSpecialCashAssessReportPinned({ pscarId: id })
      .then((res) => {
        this.getCashList()
      })
      .finally(() => {
        this.context.hideLoading()
      })
  }

  //移动小行
  handleMoveSmall = (index, indexSmall, type) => {
    let list = [...this.state.cashList]
    let listSmall = list[index].allLineList
    if (type == 'up') {
      if (indexSmall == 0) {
        return
      }
      let temp = listSmall[indexSmall]
      let temp2 = listSmall[indexSmall - 1]
      if (!list[index]?.pscarId || !temp?.rowIdx?.toString() || !temp2?.rowIdx?.toString()) {
        return
      }
      listSmall[indexSmall] = listSmall[indexSmall - 1]
      listSmall[indexSmall - 1] = temp
      this.saveSwapSmall(list[index].pscarId, temp.rowIdx, temp2.rowIdx)
    } else if (type == 'down') {
      if (indexSmall == listSmall.length - 1) {
        return
      }
      let temp = listSmall[indexSmall]
      let temp2 = listSmall[indexSmall + 1]
      if (!list[index]?.pscarId || !temp?.rowIdx?.toString() || !temp2?.rowIdx?.toString()) {
        return
      }
      listSmall[indexSmall] = listSmall[indexSmall + 1]
      listSmall[indexSmall + 1] = temp
      this.saveSwapSmall(list[index].pscarId, temp.rowIdx, temp2.rowIdx)
    } else if (type == 'top') {
      if (indexSmall == 0) {
        return
      }
      //置顶
      let temp = listSmall[indexSmall]
      if (!list[index]?.pscarId || !temp?.rowIdx?.toString()) {
        return
      }
      listSmall.splice(indexSmall, 1)
      listSmall.unshift(temp)
      this.savePinnedSmall(list[index].pscarId, temp.rowIdx)
    } else if (type == 'bottom') {
      //暂时无置底功能
      if (indexSmall == listSmall.length - 1) {
        return
      }
      //置底
      let temp = listSmall[indexSmall]
      listSmall.splice(indexSmall, 1)
      listSmall.push(temp)
    }
    this.setState({ cashList: list })
  }

  saveSwapSmall = (pscarId: string, rowIdx1: number, rowIdx2: number) => {
    this.context.showLoading()
    pdasSpecialCashAssessOrgSwap({ pscarId, rowIdx1, rowIdx2 })
      .then((res) => {
        this.getCashList()
      })
      .finally(() => {
        this.context.hideLoading()
      })
  }

  savePinnedSmall = (pscarId: string, rowIdx: number) => {
    this.context.showLoading()
    pdasSpecialCashAssessOrgPinned({ pscarId, rowIdx })
      .then((res) => {
        this.getCashList()
      })
      .finally(() => {
        this.context.hideLoading()
      })
  }

  //获取list中从后面对一个有pscarId的索引
  getNewAddIndex = (list) => {
    for (let i = list.length - 1; i >= 0; i--) {
      if (list[i]?.rowIdx?.toString()) {
        return i
      }
    }
    return 0
  }

  render(): React.ReactNode {
    return (
      <div style={{ height: '100%' }}>
        {this.props.permission != 'view' && (
          <div style={{ display: 'flex', position: 'absolute', right: '0px', top: '0px' }}>
            <div
              className={`common-button2 common-button3 ${!this.props.isVisible && 'disabled-div'}`}
              onClick={() => this.handleAdd()}
            >
              <Iconfont type="icon-xinzeng" style={{ fontSize: '20px' }} />
              <div className="common-text2">新增一行</div>
            </div>
            <div
              style={{ marginLeft: '10px' }}
              className={`common-button2 common-button3 ${
                (this.state.selectedRows.length == 0 || !this.props.isVisible) && 'disabled-div'
              }`}
              onClick={() => this.handleDelete()}
            >
              <Iconfont type="icon-shanchu" style={{ fontSize: '20px' }} />
              <div className="common-text">删除</div>
            </div>
          </div>
        )}

        <div style={{ height: '100%' }}>
          <div className="div-table">
            <table className="common-table">
              <thead>
                <tr>
                  {this.props.permission != 'view' && (
                    <th>
                      <Checkbox
                        onChange={() => this.onCheckAllChange()}
                        checked={
                          this.state.cashList.length > 0 && this.state.selectedRows.length == this.state.cashList.length
                            ? true
                            : false
                        }
                      ></Checkbox>
                    </th>
                  )}

                  <th>序号</th>
                  {this.props.permission != 'view' && <th>顺序调整</th>}
                  <th>考评项目</th>
                  <th>考评内容</th>
                  <th>考评依据</th>
                  <th>部门名称</th>
                  <th>人员</th>
                  <th>份数</th>
                  <th>金额</th>
                  <th>合计</th>
                  {this.props.permission != 'view' && <th>操作</th>}
                </tr>
              </thead>
              <tbody>
                {this.state.cashList.map((item, index) => {
                  return item.allLineList.map((obj, index2) => {
                    return (
                      <tr key={`${index}-${index2}`}>
                        {this.props.permission != 'view' && index2 == 0 && (
                          <td style={{ width: '45px' }} rowSpan={item.allLineList.length}>
                            <Checkbox
                              checked={this.state.selectedRows.includes(item.pscarId || item.cacheId)}
                              onChange={(e) => this.handleCheckBox(e.target.checked, item.pscarId || item.cacheId)}
                            ></Checkbox>
                          </td>
                        )}

                        {index2 == 0 && (
                          <td style={{ width: '45px' }} rowSpan={item.allLineList.length}>
                            {index + 1}
                          </td>
                        )}
                        {index2 == 0 && this.props.permission != 'view' && (
                          <td style={{ width: '70px' }} rowSpan={item.allLineList.length}>
                            <div className="table-operate">
                              <div
                                className={`${!this.props.isVisible && 'disabled-table-div'}`}
                                onClick={() => this.handleMove(index, 'up')}
                              >
                                <Iconfont title="上移" type="icon-shangyimian" style={{ fontSize: '16px' }} />
                              </div>
                              <div
                                style={{ paddingLeft: '3px' }}
                                className={`${!this.props.isVisible && 'disabled-table-div'}`}
                                onClick={() => this.handleMove(index, 'down')}
                              >
                                <Iconfont title="下移" type="icon-xiayimian" style={{ fontSize: '16px' }} />
                              </div>
                              <div
                                style={{ paddingLeft: '3px' }}
                                className={`${!this.props.isVisible && 'disabled-table-div'}`}
                                onClick={() => this.handleMove(index, 'top')}
                              >
                                <Iconfont title="置顶" type="icon-zhidingmian" style={{ fontSize: '16px' }} />
                              </div>
                            </div>
                          </td>
                        )}
                        {index2 == 0 && (
                          <td style={{ width: '160px' }} rowSpan={item.allLineList.length}>
                            {item.isEdit && this.props.isVisible ? (
                              <Select
                                placeholder="请选择考评项目"
                                value={item.assessItem}
                                allowClear
                                showSearch
                                optionFilterProp="children"
                                filterOption={(input: any, option: any) => option.children.indexOf(input) >= 0}
                                style={{ width: '150px' }}
                                onChange={(selected) => {
                                  this.handleContent(selected, index, 'assessItem')
                                }}
                              >
                                {this.state.kpxmList.map((dict, dictIndex) => (
                                  <Select.Option key={dictIndex} value={dict.dictKey}>
                                    {dict.value}
                                  </Select.Option>
                                ))}
                              </Select>
                            ) : (
                              this.state.kpxmList?.find((dict) => dict.dictKey == item?.assessItem)?.value
                            )}
                          </td>
                        )}
                        {index2 == 0 && (
                          <td rowSpan={item.allLineList.length}>
                            {item.isEdit && this.props.isVisible ? (
                              <div style={{ display: 'flex', alignItems: 'center' }}>
                                <TextArea
                                  autoSize
                                  maxLength={1000}
                                  // showCount
                                  value={item.assessContent}
                                  placeholder="请填写考评内容"
                                  onChange={(obj) => this.handleContent(obj.target.value, index, 'assessContent')}
                                />
                              </div>
                            ) : (
                              item.assessContent
                            )}
                          </td>
                        )}
                        {index2 == 0 && (
                          <td rowSpan={item.allLineList.length}>
                            {item.isEdit && this.props.isVisible ? (
                              <div style={{ display: 'flex', alignItems: 'center' }}>
                                <TextArea
                                  autoSize
                                  maxLength={50}
                                  // showCount
                                  value={item.reason}
                                  placeholder="请填写考评依据"
                                  onChange={(e) => this.handleContent(e.target.value, index, 'reason')}
                                />
                                <div className="row-opera" onClick={() => this.handleSaveParent(index)}>
                                  <Iconfont type="icon-icon_duihao-mian" style={{ fontSize: '16px' }} />
                                  <span className="operate-text">保存</span>
                                </div>
                              </div>
                            ) : this.props.isVisible ? (
                              <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                                <div className="row-text">{item.reason}</div>
                                <div className="row-opera" onClick={() => this.handleEditParent(index)}>
                                  <Iconfont type="icon-bianji3" style={{ fontSize: '16px' }} />
                                  <span className="operate-text">编辑</span>
                                </div>
                              </div>
                            ) : (
                              item.reason
                            )}
                          </td>
                        )}
                        <td style={{ width: '130px' }}>
                          {obj.isSmallEdit ? (
                            <Select
                              allowClear
                              value={obj.orgIds}
                              style={{ width: '120px' }}
                              onChange={(selected) => {
                                this.handleContent(selected ? [selected] : [], index, 'orgIds', index2)
                              }}
                              showSearch
                              optionFilterProp="children"
                              filterOption={(input: any, option: any) => option.children.indexOf(input) >= 0}
                            >
                              {this.state.bmActiveList.map((zditem, zdindex) => (
                                <Select.Option key={zdindex} value={zditem.id}>
                                  {zditem.orgName}
                                </Select.Option>
                              ))}
                            </Select>
                          ) : (
                            getOrgNames(obj.orgIds, this.state.bmList)
                          )}
                        </td>
                        <td style={{ width: '180px' }}>
                          {obj.isSmallEdit ? (
                            <Input
                              value={obj.personnelNames}
                              maxLength={200}
                              onChange={(e) => {
                                this.handleContent(e.target.value, index, 'personnelNames', index2)
                              }}
                            />
                          ) : (
                            obj.personnelNames
                          )}
                        </td>
                        <td style={{ width: '90px' }}>
                          {obj.isSmallEdit ? (
                            <InputNumber
                              controls={false}
                              precision={0}
                              min={0}
                              max={10000}
                              value={obj.personnelCount}
                              onChange={(value) => {
                                this.handleContent(value, index, 'personnelCount', index2)
                              }}
                            />
                          ) : (
                            obj.personnelCount
                          )}
                        </td>
                        <td style={{ width: '90px' }}>
                          {obj.isSmallEdit ? (
                            <InputNumber
                              controls={false}
                              precision={0}
                              value={obj.amount}
                              onChange={(value) => {
                                this.handleContent(value, index, 'amount', index2)
                              }}
                            />
                          ) : (
                            obj.amount
                          )}
                        </td>
                        {index2 == 0 && (
                          <td style={{ width: '45px' }} rowSpan={item.allLineList.length}>
                            {item.hj}
                          </td>
                        )}

                        {this.props.permission != 'view' && (
                          <td style={{ width: '150px' }}>
                            <div className="table-operate" style={{ justifyContent: 'left' }}>
                              {obj.isSmallEdit ? (
                                <div
                                  className={`${!this.props.isVisible && 'disabled-table-div'}`}
                                  onClick={() => this.handleSaveSmall(index, index2)}
                                >
                                  <Iconfont type="icon-icon_duihao-mian" style={{ fontSize: '16px' }} />
                                  <span className="operate-text">保存</span>
                                </div>
                              ) : (
                                <div
                                  className={`${!this.props.isVisible && 'disabled-table-div'}`}
                                  onClick={() => this.handleEditSmall(index, index2)}
                                >
                                  <Iconfont type="icon-bianji3" style={{ fontSize: '16px' }} />
                                  <span className="operate-text">编辑</span>
                                </div>
                              )}

                              {this.getNewAddIndex(item.allLineList) != index2 && (
                                <div
                                  className={`${!this.props.isVisible && 'disabled-table-div'}`}
                                  style={{ paddingLeft: '10px' }}
                                  onClick={() => this.handleDeleteSmall(index, index2)}
                                >
                                  <Iconfont type="icon-shanchu" style={{ fontSize: '16px' }} />
                                  <span className="operate-text">删除</span>
                                </div>
                              )}

                              {this.getNewAddIndex(item.allLineList) == index2 && (
                                <div
                                  className={`${!this.props.isVisible && 'disabled-table-div'}`}
                                  style={{ paddingLeft: '10px' }}
                                  onClick={() => this.handleAddSmall(index)}
                                >
                                  <Iconfont type="icon-xinzeng" style={{ fontSize: '16px' }} />
                                  <span className="operate-text">新增一行</span>
                                </div>
                              )}
                            </div>
                            <div className="table-operate" style={{ justifyContent: 'center', paddingTop: 5 }}>
                              <div
                                className={`${!this.props.isVisible && 'disabled-table-div'}`}
                                onClick={() => this.handleMoveSmall(index, index2, 'up')}
                              >
                                <Iconfont title="上移" type="icon-shangyimian" style={{ fontSize: '16px' }} />
                              </div>
                              <div
                                style={{ paddingLeft: '3px' }}
                                className={`${!this.props.isVisible && 'disabled-table-div'}`}
                                onClick={() => this.handleMoveSmall(index, index2, 'down')}
                              >
                                <Iconfont title="下移" type="icon-xiayimian" style={{ fontSize: '16px' }} />
                              </div>
                              <div
                                style={{ paddingLeft: '3px' }}
                                className={`${!this.props.isVisible && 'disabled-table-div'}`}
                                onClick={() => this.handleMoveSmall(index, index2, 'top')}
                              >
                                <Iconfont title="置顶" type="icon-zhidingmian" style={{ fontSize: '16px' }} />
                              </div>
                            </div>
                          </td>
                        )}
                      </tr>
                    )
                  })
                })}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    )
  }
}
