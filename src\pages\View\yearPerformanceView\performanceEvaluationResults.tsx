import { getPerformanceEvaluationResultsDetails } from '@/api'
import React from 'react'
import { LoadingContext } from '@/components/load/loadingProvider'

interface IProps {
  //选中的是哪个节点
  leafKey: string
}

export default class extends React.Component<IProps> {
  static contextType = LoadingContext
  context: React.ContextType<typeof LoadingContext>

  state = {
    //table数据
    keyList: [],
    dateId: ''
  }

  componentDidMount(): void {
    this.setState({ dateId: this.props.leafKey }, () => {
      // 获取列表数据
      this.getKeyList()
    })
  }

  componentDidUpdate(prevProps: Readonly<IProps>, prevState: Readonly<{}>, snapshot?: any): void {
    if (this.props.leafKey != prevProps.leafKey) {
      this.setState({ dateId: this.props.leafKey }, () => {
        // 获取列表数据
        this.getKeyList()
      })
    }
  }

  getKeyList = () => {
    if (this.state.dateId) {
      this.context.showLoading()
      // this.state.dateId.split('-')[0] 是截取年份
      getPerformanceEvaluationResultsDetails({ dateYear: this.state.dateId.split('-')[0] })
        .then((res) => {
          // console.log('获取县公司业绩考核综合得分及排名情况数据', res)
          this.setState({ keyList: res.data.list })
          this.context.hideLoading()
        })
        .catch(() => {
          this.context.hideLoading()
        })
    }
  }
  render(): React.ReactNode {
    // console.log('当前dateId值', this.state.dateId.split('-')[0])

    return (
      <div style={{ height: 'calc(100% - 10px)' }}>
        <div className="div-table" style={{ height: 'calc(100vh - 250px)', overflow: 'auto' }}>
          <table className="common-table" style={{ tableLayout: 'auto', minWidth: '100%', marginBottom: '25px' }}>
            <thead>
              <tr>
                <th>单位</th>
                <th>关键业绩指标</th>
                <th>党建工作考核</th>
                <th>安全管理工作考核</th>
                <th>“红线”指标</th>
                <th>公司领导评价</th>
                <th>专业评价</th>
                <th>重点工作考核</th>
                <th>专项考核</th>
                <th>特殊事项奖励</th>
                <th>总分</th>
              </tr>
            </thead>
            <tbody>
              {this.state.keyList.length > 0 ? (
                this.state.keyList.map((item: any, index: number) => (
                  // 长文本列使用 whiteSpace: 'pre-wrap' 和 wordBreak: 'break-word' 确保文本换行显示
                  // 部门列使用 whiteSpace: 'nowrap' 保持单行显示
                  <tr key={index}>
                    <td>{item.unit}</td>
                    <td>{item.keyPerformanceScore ? Number(item.keyPerformanceScore).toFixed(2) : '0.00'}</td>
                    <td>{item.partyBuildingScore ? Number(item.partyBuildingScore).toFixed(2) : '0.00'}</td>
                    <td>{item.safetyManagementScore ? Number(item.safetyManagementScore).toFixed(2) : '0.00'}</td>
                    <td>{item.redLineScore ? Number(item.redLineScore).toFixed(2) : '0.00'}</td>
                    <td>
                      {item.leadershipEvaluationScore ? Number(item.leadershipEvaluationScore).toFixed(2) : '0.00'}
                    </td>
                    <td>
                      {item.professionalEvaluationScore ? Number(item.professionalEvaluationScore).toFixed(2) : '0.00'}
                    </td>
                    <td>{item.keyTasksScore ? Number(item.keyTasksScore).toFixed(2) : '0.00'}</td>
                    <td>{item.specialProjectScore ? Number(item.specialProjectScore).toFixed(2) : '0.00'}</td>
                    <td>{item.specialRewardsScore ? Number(item.specialRewardsScore).toFixed(2) : '0.00'}</td>
                    <td>{item.totalScore ? Number(item.totalScore).toFixed(2) : '0.00'}</td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={11} style={{ textAlign: 'center', padding: '20px' }}>
                    暂无数据
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    )
  }
}
