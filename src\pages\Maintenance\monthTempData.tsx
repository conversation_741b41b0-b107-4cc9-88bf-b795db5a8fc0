import React from 'react'
import DateMenu from '@/components/DateMenu'
import { Iconfont } from 'tr-cheers'
import { getLeafKey } from '@/utils/utils'
import { Input, InputNumber, Modal, Radio, Select } from 'antd'
import MessageSelf from '@/components/message'
import {
  deletePerfTemplMonth,
  getDictList,
  getMenuDate,
  getOrgList,
  getPerfTemplMonthList,
  savePerfTemplMonth,
  submitPerfTemplMonth
} from '@/api'
import { LoadingContext } from '@/components/load/loadingProvider'
const { TextArea } = Input

export default class extends React.Component {
  static contextType = LoadingContext
  context: React.ContextType<typeof LoadingContext>

  state = {
    dateId: '', //时间
    //叶子节点
    leafKey: '',
    leftMenu: [],
    dataList: [],
    //职能部门列表
    znbmList: [],
    //重点工作类型列表
    zdgzlxList: [],
    //是否可以更改，默认可以更改
    isDisabled: false
  }

  componentDidMount(): void {
    //获取职能部门列表
    this.getZnbmList()
    //获取左侧菜单
    this.getLeftMenu()
    //获取重点工作类型
    this.getZdgzlxList()
  }

  getZdgzlxList = () => {
    getDictList('TEMPL_WORK_TYPE').then((res: any) => {
      if (res?.data?.list?.length > 0) {
        this.setState({ zdgzlxList: res.data.list })
      }
    })
  }

  //获取职能部门
  getZnbmList() {
    getOrgList('01,02,03', '1').then((res: any) => {
      if (res?.data?.list?.length > 0) {
        this.setState({ znbmList: res.data.list })
      }
    })
  }

  getLeftMenu() {
    this.context.showLoading()
    getMenuDate()
      .then((res: any) => {
        if (res.data && res.data.length > 0) {
          let leftMenu = res.data
          //设置dateId和叶子节点
          let leaf = null
          for (let i = 0; i < leftMenu.length; i++) {
            if (!leaf) {
              leaf = getLeafKey(leftMenu[i])
            } else {
              break
            }
          }
          this.setState({ leftMenu: leftMenu, leafKey: leaf.dateId, dateId: leaf.dateId }, () => {
            //获取表格内容
            this.getTableList()
          })
        }
      })
      .finally(() => {
        this.context.hideLoading()
      })
  }

  //菜单树点击
  handleMenu = (e) => {
    this.setState({ dateId: e.key, leafKey: e.key }, () => {
      //获取表格内容
      this.getTableList()
    })
  }
  getTableList() {
    let params = {
      date: this.state.dateId
    }
    this.context.showLoading()
    //获取表格内容
    getPerfTemplMonthList(params)
      .then((res: any) => {
        if (res?.data?.list?.length > 0) {
          //根据第一条数据判断，是否可以更改
          let status = res.data.list[0].status
          //设置职能部门名称，重点工作类型名称
          for (let i = 0; i < res.data.list.length; i++) {
            let obj = res.data.list[i]
            this.initData(obj)
          }
          this.setState({ dataList: res.data.list, isDisabled: status == '0' ? false : true })
        } else {
          this.setState({ dataList: [], isDisabled: false })
        }
      })
      .finally(() => {
        this.context.hideLoading()
      })
  }

  //新增一行
  handleAdd = () => {
    //判断有无正在编辑的内容，如果有，不能进行编辑，提示：有正在编辑的内容，请完成后，再新增一行
    let flag = false //默认没有编辑的
    for (let i = 0; i < this.state.dataList.length; i++) {
      let obj = this.state.dataList[i]
      if (obj.isEdit) {
        flag = true
        break
      }
    }
    if (flag) {
      Modal.warning({
        title: '提示',
        content: '有正在编辑的内容，请完成后，再新增一行！',
        zIndex: 1100
      })
      return
    }
    let list = [...this.state.dataList]
    list.push({
      pptmId: '',
      planName: '',
      znbm: '',
      znbmCode: [],
      workTypeVal: '',
      workType: '',
      customScoreFlag: 0, //是否自定义，默认为否
      customScore: '', //自定义分值
      isEdit: true //可编辑
    })
    this.setState({ dataList: list })
  }

  //编辑框内容
  handleContent = (value, index, tdName) => {
    let list = [...this.state.dataList]
    //判断，如果是职能部门，只能选择2个
    if (tdName === 'znbmCode') {
      if (value.length > 2) {
        let newValue = []
        newValue.push(value[value.length - 2])
        newValue.push(value[value.length - 1])
        value = newValue
      }
    }
    if (tdName == 'workType') {
      //重点工作类型，如果自定义类型是否，显示默认分值
      if (value) {
        if (list[index]['customScoreFlag'] == 0) {
          if (value == '01') {
            //A
            list[index]['customScore'] = 1.3
          } else if (value == '02') {
            //B
            list[index]['customScore'] = 1.1
          }
        }
      } else {
        list[index]['customScore'] = ''
      }
    }
    //是否自定义类型
    if (tdName == 'customScoreFlag') {
      if (value == 0) {
        //否
        if (list[index]['workType']) {
          if (list[index]['workType'] == '01') {
            list[index]['customScore'] = 1.3
          } else if (list[index]['workType'] == '02') {
            list[index]['customScore'] = 1.1
          } else {
            list[index]['customScore'] = ''
          }
        }
      }
    }

    list[index][tdName] = value
    this.setState({ dataList: list })
  }

  //删除
  handleDelete = (index) => {
    let _this = this
    Modal.confirm({
      title: '删除提示',
      content: '确定删除吗?',
      onOk: () => {
        //根据id判断是否调用删除接口
        if (_this.state.dataList[index].pptmId) {
          _this.context.showLoading()
          //调用删除接口
          deletePerfTemplMonth(_this.state.dataList[index].pptmId)
            .then((res: any) => {
              if (res?.code == '0') {
                MessageSelf('删除成功！', 'success')
                //删除成功后再页面删除掉
                //js指定删除数组的哪行
                let list = [..._this.state.dataList]
                list.splice(index, 1)
                _this.setState({ dataList: list })
              }
            })
            .finally(() => {
              _this.context.hideLoading()
            })
        } else {
          //js指定删除数组的哪行
          let list = [..._this.state.dataList]
          list.splice(index, 1)
          this.setState({ dataList: list })
        }
      }
    })
  }

  //初始化一条数据
  initData = (obj) => {
    let znbms = []
    let znbmCode = []
    if (obj.orgId1) {
      znbmCode.push(obj.orgId1)
      znbms.push(obj.orgName1)
    }
    if (obj.orgId2) {
      znbmCode.push(obj.orgId2)
      znbms.push(obj.orgName2)
    }
    let znbm = znbms.join('、')
    obj.znbm = znbm
    obj.znbmCode = znbmCode
    return obj
  }
  //保存
  handleSave = (index) => {
    //判断必填
    let obj = this.state.dataList[index]
    if (!obj.planName.trim()) {
      Modal.warning({
        title: '提示',
        content: '请输入计划名称！',
        zIndex: 1100
      })
      return
    }
    if (obj.znbmCode.length <= 0) {
      Modal.warning({
        title: '提示',
        content: '请选择职能部门！',
        zIndex: 1100
      })
      return
    }
    if (!obj.workType) {
      Modal.warning({
        title: '提示',
        content: '请选择重点工作类型！',
        zIndex: 1100
      })
      return
    }
    //判断是否是自定义类型
    if (obj.customScoreFlag == 1) {
      if (!obj.customScore) {
        Modal.warning({
          title: '提示',
          content: '请输入自定义类型分值！',
          zIndex: 1100
        })
        return
      }
    }
    if (obj.customScoreFlag == 0) {
      //否的时候，设置默认值
      if (obj.workType) {
        if (obj.workType == '01') {
          obj.customScore = 1.3
        }
        if (obj.workType == '02') {
          obj.customScore = 1.1
        }
      }
    }

    //调用保存接口
    let saveData = {
      pptmId: obj.pptmId,
      date: this.state.dateId,
      planName: obj.planName, //计划名称
      orgId1: obj.znbmCode[0], //职能部门1
      orgId2: obj.znbmCode.length > 1 ? obj.znbmCode[1] : '', //职能部门2，可不填
      workType: obj.workType, //重点工作类型  字典code”TEMPL_WORK_TYPE”对应的key
      customScoreFlag: obj.customScoreFlag,
      customScore: obj.customScore,
      status: obj.status || '0' //模板状态，全部传“0”
    }
    this.context.showLoading()
    savePerfTemplMonth(saveData)
      .then((res) => {
        this.getSaveData(res.data, index)
      })
      .finally(() => {
        this.context.hideLoading()
      })
  }

  getSaveData(obj, index) {
    obj = this.initData(obj)
    let list = [...this.state.dataList]
    list[index] = obj
    list[index].isEdit = false
    this.setState({ dataList: list })
  }

  //处理编辑
  handleEdit = (index) => {
    let list = [...this.state.dataList]
    list[index].isEdit = true
    this.setState({ dataList: list })
  }

  //提交
  handleSubmit = () => {
    //判断是否有未保存的数据
    let list = [...this.state.dataList]
    for (let i = 0; i < list.length; i++) {
      if (list[i].isEdit) {
        Modal.warning({
          title: '提示',
          content: '第' + (i + 1) + '行请先保存后再提交！',
          zIndex: 1100
        })
        return
      }
    }
    let _this = this
    Modal.confirm({
      title: '提交提示',
      content: '确定提交吗?提交后，数据将不能更改！',
      onOk: () => {
        //调用提交接口
        _this.context.showLoading()
        submitPerfTemplMonth({ date: this.state.dateId })
          .then((res: any) => {
            if (res.code == '0') {
              _this.getTableList()
            }
          })
          .finally(() => {
            _this.context.hideLoading()
          })
      }
    })
  }

  //移动
  handleMove = (index, type) => {
    let list = [...this.state.dataList]
    if (type == 'up') {
      if (index == 0) {
        return
      }
      let temp = list[index]
      list[index] = list[index - 1]
      list[index - 1] = temp
    } else if (type == 'down') {
      if (index == list.length - 1) {
        return
      }
      let temp = list[index]
      list[index] = list[index + 1]
      list[index + 1] = temp
    } else if (type == 'top') {
      if (index == 0) {
        return
      }
      //置顶
      let temp = list[index]
      list.splice(index, 1)
      list.unshift(temp)
    } else if (type == 'bottom') {
      if (index == list.length - 1) {
        return
      }
      //置底
      let temp = list[index]
      list.splice(index, 1)
      list.push(temp)
    }
    this.setState({ dataList: list })
  }
  render() {
    return (
      <div className="main-common">
        <div className="main-left">
          {this.state.dateId && (
            <DateMenu
              menuList={this.state.leftMenu}
              dateId={this.state.dateId}
              leafKey={this.state.leafKey}
              onClick={(e) => this.handleMenu(e)}
            />
          )}
        </div>
        <div className="main-right">
          <div className="content-top">
            <div
              className={`common-button2 ${this.state.isDisabled && 'disabled-div'}`}
              onClick={() => this.handleSubmit()}
            >
              <Iconfont type="icon-wodetijiao" style={{ fontSize: '28px' }} />
              <div className="common-text">提交</div>
            </div>
          </div>
          <div className="content-center">
            <div style={{ display: 'flex', justifyContent: 'end' }}>
              <div
                className={`common-button2 common-button3 ${this.state.isDisabled && 'disabled-div'}`}
                onClick={() => this.handleAdd()}
              >
                <Iconfont type="icon-xinzeng" style={{ fontSize: '20px' }} />
                <div className="common-text2">新增一行</div>
              </div>
            </div>

            <div className="div-table">
              <table className="common-table">
                <thead>
                  <tr>
                    <th>序号</th>
                    {/* {!this.state.isDisabled && <th>顺序操作</th>} */}
                    <th>计划名称</th>
                    <th>职能部门</th>
                    <th>重点工作类型</th>
                    <th>自定义类型分</th>
                    <th>操作</th>
                  </tr>
                </thead>
                <tbody>
                  {this.state.dataList.map((item, index) => {
                    return (
                      <tr key={item.pptmId}>
                        <td style={{ width: '45px' }}>{index + 1}</td>
                        {/* {!this.state.isDisabled && (
                          <td style={{ width: '80px' }}>
                            <div className="table-operate">
                              <div onClick={() => this.handleMove(index, 'up')}>
                                <Iconfont title="上移" type="icon-shangyimian" style={{ fontSize: '16px' }} />
                              </div>
                              <div style={{ paddingLeft: '3px' }} onClick={() => this.handleMove(index, 'down')}>
                                <Iconfont title="下移" type="icon-xiayimian" style={{ fontSize: '16px' }} />
                              </div>
                              <div style={{ paddingLeft: '3px' }} onClick={() => this.handleMove(index, 'top')}>
                                <Iconfont title="置顶" type="icon-zhidingmian" style={{ fontSize: '16px' }} />
                              </div>
                              <div style={{ paddingLeft: '3px' }} onClick={() => this.handleMove(index, 'bottom')}>
                                <Iconfont title="置底" type="icon-zhidimian" style={{ fontSize: '16px' }} />
                              </div>
                            </div>
                          </td>
                        )} */}
                        <td>
                          {item.isEdit ? (
                            <TextArea
                              disabled={this.state.isDisabled}
                              autoSize
                              maxLength={1000}
                              // showCount
                              value={item.planName}
                              placeholder="请填写计划名称"
                              onChange={(obj) => this.handleContent(obj.target.value, index, 'planName')}
                            />
                          ) : (
                            item.planName
                          )}
                        </td>
                        <td style={{ width: '170px' }}>
                          {item.isEdit ? (
                            <Select
                              disabled={this.state.isDisabled}
                              allowClear
                              value={item.znbmCode}
                              mode="multiple"
                              style={{ width: '160px' }}
                              placeholder="最多选择2个"
                              onChange={(selected) => {
                                this.handleContent(selected, index, 'znbmCode')
                              }}
                              showSearch
                              optionFilterProp="children"
                              filterOption={(input: any, option: any) => option.children.indexOf(input) >= 0}
                            >
                              {this.state.znbmList.map((item, index) => (
                                <Select.Option key={index} value={item.id}>
                                  {item.orgName}
                                </Select.Option>
                              ))}
                            </Select>
                          ) : (
                            item.znbm
                          )}
                        </td>
                        <td style={{ width: '95px' }}>
                          {item.isEdit ? (
                            <Select
                              disabled={this.state.isDisabled}
                              allowClear
                              value={item.workType}
                              style={{ width: '85px' }}
                              onChange={(selected) => {
                                this.handleContent(selected, index, 'workType')
                              }}
                              showSearch
                              optionFilterProp="children"
                              filterOption={(input: any, option: any) => option.children.indexOf(input) >= 0}
                            >
                              {this.state.zdgzlxList.map((item, index) => (
                                <Select.Option key={index} value={item.dictKey}>
                                  {item.dictVal}
                                </Select.Option>
                              ))}
                            </Select>
                          ) : (
                            item.workTypeVal
                          )}
                        </td>
                        <td style={{ width: '140px' }}>
                          {item.isEdit ? (
                            <div style={{ display: 'flex', alignItems: 'center' }}>
                              <div>
                                <Radio.Group
                                  value={item.customScoreFlag}
                                  onChange={(obj) => this.handleContent(obj.target.value, index, 'customScoreFlag')}
                                >
                                  <Radio value={1}>是</Radio>
                                  <Radio value={0}>否</Radio>
                                </Radio.Group>
                              </div>
                              <InputNumber
                                disabled={item.customScoreFlag === 0}
                                value={item.customScore}
                                style={{ width: '50px' }}
                                controls={false}
                                precision={1}
                                min={0}
                                onChange={(value) => {
                                  this.handleContent(value, index, 'customScore')
                                }}
                              />
                            </div>
                          ) : (
                            <span>{item.customScoreFlag === 1 ? <span>是（{item.customScore}）</span> : '否'}</span>
                          )}
                        </td>
                        <td style={{ width: '130px' }}>
                          <div className="table-operate">
                            {item.isEdit ? (
                              <div
                                // className={`${this.state.isDisabled && 'disabled-table-div'}`}
                                onClick={() => this.handleSave(index)}
                              >
                                <Iconfont type="icon-icon_duihao-mian" style={{ fontSize: '16px' }} />
                                <span className="operate-text">保存</span>
                              </div>
                            ) : (
                              <div
                                // className={`${this.state.isDisabled && 'disabled-table-div'}`}
                                onClick={() => this.handleEdit(index)}
                              >
                                <Iconfont type="icon-bianji3" style={{ fontSize: '16px' }} />
                                <span className="operate-text">编辑</span>
                              </div>
                            )}

                            <div
                              className={`${this.state.isDisabled && 'disabled-table-div'}`}
                              onClick={() => this.handleDelete(index)}
                            >
                              <Iconfont type="icon-shanchu" style={{ fontSize: '16px' }} />
                              <span className="operate-text">删除</span>
                            </div>
                          </div>
                        </td>
                      </tr>
                    )
                  })}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    )
  }
}
