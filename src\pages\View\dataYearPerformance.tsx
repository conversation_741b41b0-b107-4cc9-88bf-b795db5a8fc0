import React from 'react'
import DateMenu from '@/components/DateMenu'
import { getDefaultSelectedMonth, getLeafKey, getTabByKey } from '@/utils/utils'
import { Iconfont } from 'tr-cheers'
// 市公司Tabs
import AssessmentFulfillment from './yearPerformanceView/assessmentFulfillment'
import ImportantWork from './yearPerformanceView/assessmentScoreAllocation'
import AssessmentScoreAllocation from './yearPerformanceView/importantWork'
import YearComprehensive from './yearPerformanceView/yearComprehensive'
// 县公司Tabs
import PerformanceEvaluationResults from './yearPerformanceView/performanceEvaluationResults'
import AssessmentSharingDetails from './yearPerformanceView/assessmentSharingDetails'
import CompanyScoreDetails from './yearPerformanceView/companyScoreDetails'
import { Modal } from 'antd'
import { getExportMonthWord, getDateMenuYearView } from '@/api'
import { LoadingContext } from '@/components/load/loadingProvider'
import { withRouter } from 'tr-cheers'

@withRouter
export default class extends React.Component {
  static contextType = LoadingContext
  context: React.ContextType<typeof LoadingContext>

  state = {
    dateId: '', //时间
    //叶子节点
    leafKey: '',
    leftMenu: [],
    //Tab类型
    tabType: '1',
    tabs: [],
    //状态
    status: '01',
    //全局搜索
    globalSearch: { dateId: '', tab: '' }
  }
  // 县公司 Tabs
  countyTabs = [
    { id: '1', title: '县公司业绩考核结果' },
    { id: '2', title: '县公司业绩考核分享明细' },
    { id: '3', title: '各县公司得分明细' }
  ]
  componentDidMount(): void {
    //获取url参数（用于全局搜索跳转）
    let globalSearch = (this.props as any).location.state
    if (globalSearch?.dateId) {
      //获取tabType
      let tab = getTabByKey(globalSearch.tab)
      this.setState({ globalSearch, tabType: tab.id }, () => {
        //获取左侧菜单
        this.getLeftMenu()
      })
    } else {
      //获取左侧菜单
      this.getLeftMenu()
    }
    //获取tabs
    this.getTabs()
  }

  getTabs = () => {
    let tabs = [
      {
        id: '1',
        title: '年度绩效考核兑现分值'
      },
      { id: '3', title: '"十大行动"重点工作' },
      { id: '2', title: '业绩指标考核分值二次分配情况' },

      { id: '4', title: '原集体企业年度综合考评分档情况' }
    ]
    this.setState({ tabs })
  }

  getLeftMenu() {
    this.context.showLoading()
    getDateMenuYearView()
      .then((res: any) => {
        // console.log('获取左侧菜单数据333', res)
        if (res.data && res.data && res.data.length > 0) {
          let leftMenu = res.data
          //如果有全局搜索参数，说明是从其他页面跳转过来的
          if (this.state.globalSearch?.dateId) {
            //设置全局搜索参数
            this.setState({
              leftMenu: leftMenu,
              dateId: this.state.globalSearch.dateId,
              leafKey: this.state.globalSearch?.dateId
            })
            this.context.hideLoading()
            return
          }

          /**
           * 递归查找第一个叶子节点（subMenuVoList为空的节点）
           * @param node 菜单树的根节点或节点数组
           * @returns 第一个叶子节点对象，找不到则返回null
           */
          function findFirstLeafNode(node: any): any | null {
            if (Array.isArray(node)) {
              // 如果是数组，遍历每个元素
              for (let item of node) {
                const found = findFirstLeafNode(item)
                if (found) return found
              }
              return null
            } else if (node) {
              // 如果没有子节点，就是叶子节点
              if (!node.subMenuVoList || node.subMenuVoList.length === 0) {
                return node
              }
              // 否则递归子节点
              return findFirstLeafNode(node.subMenuVoList)
            }
            return null
          }
          let leaf = findFirstLeafNode(leftMenu)
          if (leaf) {
            this.setState({ leftMenu, leafKey: leaf.dateId, dateId: leaf.dateId })
          }
          // let leaf = leftMenu[0]?.subMenuVoList[0]
          // console.log('leaf', leaf)
          // this.setState({ leftMenu: leftMenu, leafKey: leaf.dateId, dateId: leaf.dateId })
        }
        this.context.hideLoading()
      })
      .catch(() => {
        this.context.hideLoading()
      })
  }

  //菜单树点击
  handleMenu = (e) => {
    ;(this.props as any).navigate('/dataYearPerformance', {
      state: null
    })
    this.setState({ dateId: e.key, leafKey: e.key })
  }

  //切换tab
  handleTab = (e) => {
    this.setState({ tabType: e.id })
  }

  exportData = () => {
    Modal.confirm({
      title: '导出年度报告',
      content: '是否导出年度报告？',
      onOk: () => {
        this.context.showLoading()
        getExportMonthWord({ date: this.state.dateId })
          .then((res) => {
            this.context.hideLoading()
          })
          .catch(() => {
            this.context.hideLoading()
          })
      }
    })
  }

  render() {
    // console.log('当前leafKey值:', this.state.leafKey, '当前dateId值', this.state.dateId)
    return (
      <div className="main-common">
        <div className="main-left">
          {this.state.dateId && (
            <DateMenu
              menuList={this.state.leftMenu}
              dateId={this.state.dateId}
              leafKey={this.state.leafKey}
              onClick={(e) => this.handleMenu(e)}
            />
          )}
        </div>

        <div className="main-right">
          {/* // 以dateId区分市公司和县公司 ，只判断后缀  sgs 市公司  xgs 县公司 */}
          {this.state.leafKey && (
            <div className="content-center">
              {this.state.leafKey.endsWith('sgs') && (
                <div>
                  {/* 头部 */}
                  <div className="content-top" style={{ justifyContent: 'space-between' }}>
                    <div style={{ display: 'flex' }}>
                      {this.state.tabs.map((item) => (
                        <div
                          key={item.id}
                          className={`common-tab-button2 ${
                            this.state.tabType == item.id && 'common-tab-button-selected'
                          }`}
                          style={{ marginRight: '10px' }}
                          onClick={() => this.handleTab(item)}
                        >
                          {item.title}
                        </div>
                      ))}
                    </div>
                    <div style={{ display: 'flex' }}>
                      <div
                        className={`common-button2 ${this.state.status != '01' && 'disabled-div'}`}
                        style={{ width: '155px' }}
                        onClick={() => this.exportData()}
                      >
                        <Iconfont type="icon-download" style={{ fontSize: '22px' }} />
                        <div className="common-text">
                          <span style={{ letterSpacing: '0px' }}>导出年度报告</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  {/* 内容 */}
                  <div className="content-center">
                    <div style={{ height: '100%', display: this.state.tabType == '1' ? 'block' : 'none' }}>
                      {/* 各部门（单位）年度绩效考核兑现分值明细 */}
                      <AssessmentFulfillment leafKey={this.state.leafKey} />
                    </div>
                    <div style={{ height: '100%', display: this.state.tabType == '2' ? 'block' : 'none' }}>
                      {/* “十大行动”重点工作考核情况 */}
                      <ImportantWork leafKey={this.state.leafKey} />
                    </div>
                    <div style={{ height: '100%', display: this.state.tabType == '3' ? 'block' : 'none' }}>
                      {/* <ProfessEvaluationFill leafKey={this.state.leafKey} globalSearch={this.state.globalSearch} /> */}
                      {/* 业绩指标考核分值二次分配情况 */}
                      <AssessmentScoreAllocation leafKey={this.state.leafKey} />
                    </div>
                    <div style={{ height: '100%', display: this.state.tabType == '4' ? 'block' : 'none' }}>
                      {/* <SpecialAssessmentFill leafKey={this.state.leafKey} globalSearch={this.state.globalSearch} /> */}
                      {/* 原集体企业年度综合考评分档情况 */}
                      <YearComprehensive leafKey={this.state.leafKey} />
                    </div>
                  </div>
                </div>
              )}
              {this.state.leafKey.endsWith('xgs') && (
                <div>
                  {/* 县公司头部 */}
                  <div className="content-top" style={{ justifyContent: 'space-between' }}>
                    <div style={{ display: 'flex' }}>
                      {this.countyTabs.map((item) => (
                        <div
                          key={item.id}
                          className={`common-tab-button2 ${
                            this.state.tabType == item.id && 'common-tab-button-selected'
                          }`}
                          style={{ marginRight: '10px' }}
                          onClick={() => this.setState({ tabType: item.id })}
                        >
                          {item.title}
                        </div>
                      ))}
                    </div>
                    <div style={{ display: 'flex' }}>
                      <div
                        className={`common-button2 ${this.state.status != '01' && 'disabled-div'}`}
                        style={{ width: '155px' }}
                        onClick={() => this.exportData()}
                      >
                        <Iconfont type="icon-download" style={{ fontSize: '22px' }} />
                        <div className="common-text">
                          <span style={{ letterSpacing: '0px' }}>导出年度报告</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  {/* 县公司内容区 */}
                  <div className="content-center">
                    <div style={{ height: '100%', display: this.state.tabType == '1' ? 'block' : 'none' }}>
                      {/* 县公司业绩考核结果内容 */}
                      <PerformanceEvaluationResults leafKey={this.state.leafKey} />
                    </div>
                    <div style={{ height: '100%', display: this.state.tabType == '2' ? 'block' : 'none' }}>
                      {/* 县公司业绩考核分享明细内容 */}
                      <AssessmentSharingDetails leafKey={this.state.leafKey} />
                    </div>
                    <div style={{ height: '100%', display: this.state.tabType == '3' ? 'block' : 'none' }}>
                      {/* 各县公司得分明细内容 */}
                      <CompanyScoreDetails leafKey={this.state.leafKey} />
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    )
  }
}
