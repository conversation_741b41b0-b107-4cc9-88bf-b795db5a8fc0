import axios from 'axios'

export function login(params) {
  return axios.post('/api/v1/ca/session', params)
}
/**
 * 判断是否有tab页面
 * @param params
 * @returns
 */
export function getSubMenu(params) {
  return axios.get('/api/v1/ca/auth/subMenu', { params })
}

/**
 * 获取字典列表
 * @returns
 */
export function getDictList(dictCode) {
  return axios.get('/api/v1/pdas/pdasDict/list?dictCode=' + dictCode)
}

/**
 * 获取当前用户信息
 * @param id
 * @returns
 */
export function getUserInfo(id) {
  return axios.get('/api/v1/pdas/commonApi/getUserInfo?id=' + id)
}

/**
 * 获取验证码
 * @returns
 */
export function getCode() {
  return axios.get('/api/v1/ca/captcha/getCode')
}

/**
 * 获取指定类型组织机构列表
 * 查询指定类型的部门列表，查多个用英文,分割，例：“01,02,03”，不填写查询系统内所有部门，01（组织部等六个部门），02（33个普通部门），03（六个县公司）
 * @param dictCode
 * @returns
 */
export function getOrgList(orgTypes?, isActive?) {
  return axios.get('/api/v1/pdas/pdasDict/orgList', { params: { orgTypes, isActive } })
}

//获取年月时间菜单树
export function getMenuDate() {
  return axios.get('/api/v1/pdas/commonApi/dateMenuTempl')
}
//年度绩效--获取年度查看页菜单
export function getDateMenuYearView() {
  return axios.get('/api/v1/pdas/commonApi/dateMenuYearView')
}
//年度绩效-- 各部门（单位）年度绩效考核兑现分值明细
export function getYearDashboardPerfDetails(params) {
  return axios.get('/api/v1/pdas/pdasYearDashboardPerfDetails/list', { params })
}
//年度绩效-- “十大行动”重点工作考核情况
export function getImportantWorkDetails(params) {
  return axios.get('/api/v1/pdas/pdasYearDashboardKeyAction/list', { params })
}
//年度绩效--业绩指标考核分值二次分配情况- 关键业绩指标
export function getKeyPerformanceDetails(params) {
  return axios.get('/api/v1/pdas/pdasYearDashboardKeyPerformance/list', { params })
}
//年度绩效--业绩指标考核分值二次分配情况- 重点专项任务
export function getKeynoteWorkDetails(params) {
  return axios.get('/api/v1/pdas/pdasYearDashboardSpecialTask/list', { params })
}
//年度绩效--业绩指标考核分值二次分配情况- 科技创新奖励加分
export function getTechnologicalInnovationDetails(params) {
  return axios.get('/api/v1/pdas/pdasYearDashboardInnovationReward/list', { params })
}
//年度绩效--业绩指标考核分值二次分配情况- 党建工作考核
export function getPartyConstructionDetails(params) {
  return axios.get('/api/v1/pdas/pdasYearDashboardPartyBuilding/list', { params })
}
//年度绩效--原集体企业年度综合考评分档情况
export function getYearComprehensiveDetails(params) {
  return axios.get('/api/v1/pdas/pdasYearDashboardCollectiveEvaluation/list', { params })
}
//年度绩效--县公司业绩考核综合得分及排名情况
export function getPerformanceEvaluationResultsDetails(params) {
  return axios.get('/api/v1/pdas/pdasYearDashboardCountyCompany/list', { params })
}
//年度绩效--县公司关键业绩指标得分情况表
export function getKeyPerformanceIndicatorstsDetails(params) {
  return axios.get('/api/v1/pdas/pdasYearDashboardCountyKpi/list', { params })
}
//年度绩效--县公司各项关键业绩指标年度得分情况表
export function getKeyCountyKpiDetails(params) {
  return axios.get('/api/v1/pdas/pdasYearDashboardCountyKpiDetails/list', { params })
}
//年度绩效--县公司党建工作考核得分情况表
export function getPartyBuildingDetails(params) {
  return axios.get('/api/v1/pdas/pdasYearDashboardPartyBuildingDetails/list', { params })
}
//年度绩效--县公司安全管理工作考核得分情况表
export function getSafetyManagementDetails(params) {
  return axios.get('/api/v1/pdas/pdasYearDashboardSafetyManagement/list', { params })
}
//年度绩效--县公司公司领导评价得分情况表
export function getLeadershipDetails(params) {
  return axios.get('/api/v1/pdas/pdasYearDashboardLeadershipEvaluation/list', { params })
}
//年度绩效--县公司专业评价得分情况表
export function getProfessionalEvaluationDetails(params) {
  return axios.get('/api/v1/pdas/pdasYearDashboardProfessionalEvaluation/list', { params })
}
//年度绩效--县公司重点工作考核得分情况表
export function getServiceRatingDetails(params) {
  return axios.get('/api/v1/pdas/pdasYearDashboardKeyTasks/list', { params })
}
//年度绩效--县公司2024年“十大行动”重点工作评价得分情况表
export function getYearDashboardKeyActions2024Details(params) {
  return axios.get('/api/v1/pdas/pdasYearDashboardKeyActions2024/list', { params })
}
//年度绩效--县公司专项考核得分情况表
export function getSpecialAssessmentDetails(params) {
  return axios.get('/api/v1/pdas/pdasYearDashboardSpecialProjects/list', { params })
}
//年度绩效--县公司特殊事项奖励得分情况表
export function getSpecialMatterDetails(params) {
  return axios.get('/api/v1/pdas/pdasYearDashboardSpecialRewards/list', { params })
}
//年度绩效--各县公司得分明细--关键业绩指标
export function getKeyKpiSummaryrDetails(params) {
  return axios.get('/api/v1/pdas/pdasYearDashboardKeyKpiSummary/list', { params })
}
//年度绩效--各县公司得分明细--党建工作考核
export function getPartyBuildingRankingDetails(params) {
  return axios.get('/api/v1/pdas/pdasYearDashboardPartyBuildingRanking/list', { params })
}
//年度绩效--各县公司得分明细--安全管理工作考核
export function getSafetyManagementRankingDetails(params) {
  return axios.get('/api/v1/pdas/pdasYearDashboardSafetyManagementRanking/list', { params })
}
//年度绩效--各县公司得分明细--红线指标
export function getRedLineIndicatorsDetails(params) {
  return axios.get('/api/v1/pdas/pdasYearDashboardRedLineIndicators/list', { params })
}
//年度绩效--各县公司得分明细--公司领导评价
export function getCompanyLeadershipDetails(params) {
  return axios.get('/api/v1/pdas/pdasYearDashboardCompanyLeadership/list', { params })
}
//年度绩效--各县公司得分明细--重点工作考核
export function getKeyWorkAssessmentDetails(params) {
  return axios.get('/api/v1/pdas/pdasYearDashboardKeyWorkAssessment/list', { params })
}
//年度绩效--各县公司得分明细--特殊事项奖励
export function getSpecialRewardsDetails(params) {
  return axios.get('/api/v1/pdas/pdasYearDashboardCountySpecialRewards/list', { params })
}
//年度绩效--各县公司得分明细--月度考核结果平均分
export function getMonthlyAssessmentAvgDetails(params) {
  return axios.get('/api/v1/pdas/pdasYearDashboardMonthlyAssessmentAvg/list', { params })
}

/**
 *模板保存
 */
export function savePerfTemplMonth(data) {
  return axios.post('/api/v1/pdas/pdasPerfTemplMonth', data)
}
/**
 * 模版列表
 * @param params
 */

export function getPerfTemplMonthList(params) {
  return axios.get('/api/v1/pdas/pdasPerfTemplMonth/list', { params })
}

/**
 * 模版删除
 * @param data
 * @returns
 */
export function deletePerfTemplMonth(ids) {
  return axios.delete('/api/v1/pdas/pdasPerfTemplMonth?ids=' + ids)
}

/**
 * 模版提交
 * @param data
 * @returns
 */
export function submitPerfTemplMonth(data) {
  return axios.post('/api/v1/pdas/pdasPerfTemplMonth/submit', data)
}

/**
 * 重点绩效列表
 * @param params
 * @returns
 */
export function getMeargeTemplList(params) {
  return axios.get('/api/v1/pdas/pdasPrefPlanReport/meargeTemplList', { params })
}

/**
 * 保存（删除）重点绩效
 * @param data
 * @returns
 */
export function savePrefPlanReport(data) {
  return axios.post('/api/v1/pdas/pdasPrefPlanReport', data)
}

/**
 * 月度绩效填报左侧菜单树
 * @param params
 * @returns
 */
export function getDateMenuReport() {
  return axios.get('/api/v1/pdas/commonApi/dateMenuReport')
}

/**
 * 业绩指标考核列表
 * @param params  {date: '202101'}
 * @returns
 */
export function getPerfAssessList(params) {
  return axios.get('/api/v1/pdas/pdasIndicatorAssessReport/list', { params })
}

/**
 * 业绩指标名称
 * @returns
 */
export function getDictListByName(params) {
  return axios.get('/api/v1/pdas/pdasBusinessCategoriesDict/list', { params })
}

/**
 * 业绩指标考核-保存一行
 * @param data
 * @returns
 */
export function savePerfAssess(data) {
  return axios.post('/api/v1/pdas/pdasIndicatorAssessReport', data)
}

/**
 * 业绩指标考核-保存一行内的单位
 * @param data
 * @returns
 */
export function savePerfAssessOrg(data) {
  return axios.post('/api/v1/pdas/pdasIndicatorAssessOrg/addList', data)
}

/**
 * 业绩指标考核-删除一行的单位
 * @param ids
 * @returns
 */
export function deletePerfAssessOrg(ids) {
  return axios.delete('/api/v1/pdas/pdasIndicatorAssessOrg?ids=' + ids)
}

/**
 * 专项考核-分值考核列表
 * @param params
 * @returns
 */
export function getScoreAssessList(params) {
  return axios.get('/api/v1/pdas/pdasSpecialScoreAssessReport/list', { params })
}

/**
 * 删除业绩指标考核
 * @param ids
 * @returns
 */
export function deletePerfAssess(params) {
  return axios.delete('/api/v1/pdas/pdasIndicatorAssessReport', { params })
}

/**
 * 专项考核-分值考核保存
 * @param data
 * @returns
 */
export function saveScoreAssess(data) {
  return axios.post('/api/v1/pdas/pdasSpecialScoreAssessReport', data)
}

/**
 * 专项考核-分值考核删除
 * @param data
 * @returns
 */
export function deleteScoreAssess(ids) {
  return axios.delete('/api/v1/pdas/pdasSpecialScoreAssessReport?ids=' + ids)
}

/**
 * 专业评价-市公司列表
 * @param params
 * @returns
 */
export function getCityList(params) {
  return axios.get('/api/v1/pdas/pdasProfEvalReportCity/list', { params })
}

/**
 * 专业评价-市公司保存考评内容
 * @param data
 * @returns
 */
export function saveCity(data) {
  return axios.post('/api/v1/pdas/pdasProfEvalReportCity', data)
}

/** 专业评价-市公司删除一行
 * @param params
 * @returns
 */
export function deleteCity(params) {
  return axios.delete('/api/v1/pdas/pdasProfEvalReportCity', { params })
}

/** 专业评价-市公司保存部门
 * @param data
 * @returns
 */
export function saveCityOrg(data) {
  return axios.post('/api/v1/pdas/pdasProfEvalReportCityOrg/addList', data)
}

/**
 * 专业评价-市公司删除部门
 * @param params
 * @returns
 */
export function deleteCityOrg(params) {
  return axios.delete('/api/v1/pdas/pdasProfEvalReportCityOrg', { params })
}

/**
 * 专项考核-现金奖惩列表
 * @param params
 * @returns
 */
export function getCashAssessReportList(params) {
  return axios.get('/api/v1/pdas/pdasSpecialCashAssessReport/list', { params })
}

/**
 * 专项考核-现金奖惩保存
 * @param data
 * @returns
 */
export function saveCashAssessReport(data) {
  return axios.post('/api/v1/pdas/pdasSpecialCashAssessReport', data)
}

/**
 * 专项考核-现金奖惩删除
 * @param data
 * @returns
 */
export function deleteCashAssessReport(ids) {
  return axios.delete('/api/v1/pdas/pdasSpecialCashAssessReport?ids=' + ids)
}

/**
 * 特殊事项奖励列表
 * @returns
 */
export function pdasSpecialRewardList(date: string) {
  return axios.get('/api/v1/pdas/pdasSpecialReward/list?date=' + date)
}

/**
 * 删除特殊事项奖励的一行
 * @param data
 * @returns
 */
export function postPdasSpecialReward(data) {
  return axios.post('/api/v1/pdas/pdasSpecialReward', data)
}

/**
 * 删除特殊事项奖励的一行
 * @param data
 * @returns
 */
export function delPdasSpecialReward(ids: Array<string>) {
  return axios.delete('/api/v1/pdas/pdasSpecialReward?ids=' + ids.join())
}

/**
 * 佐证材料列表
 * @param params
 * @returns
 */
export function getEvidenceMaterialList(params) {
  return axios.get('/api/v1/pdas/pdasEvidenceMaterial/list', { params })
}

/**
 * 佐证材料上传
 * @param data
 * @returns
 */
export function uploadEvidenceMaterial(date, files) {
  let form = new FormData()
  form.append('date', date)
  for (let i = 0; i < files.length; i++) {
    form.append('file', files[i])
  }
  return axios.post('/api/v1/pdas/pdasEvidenceMaterial/upload', form)
}

/**
 * 佐证材料下载
 * @param params
 * @returns
 */
export function downLoadEvidenceMaterial(fileId) {
  return axios.get('/api/v1/pdas/pdasEvidenceMaterial/fileDownload?fileId=' + fileId, { responseType: 'blob' })
}

/**
 * 佐证材料删除
 * @param ids
 * @returns
 */
export function deleteEvidenceMaterial(ids) {
  return axios.delete('/api/v1/pdas/pdasEvidenceMaterial?ids=' + ids)
}

/**
 * 临时性非常规工作保存
 * @param data
 * @returns
 */
export function saveTempNonRoutineWork(data) {
  return axios.post('/api/v1/pdas/pdasTempNonRoutineWorkReport', data)
}

/**
 * 临时性非常规工作删除
 * @param ids
 * @returns
 */
export function deleteTempNonRoutineWork(ids) {
  return axios.delete('/api/v1/pdas/pdasTempNonRoutineWorkReport?ids=' + ids)
}

/**
 * 临时性非常规工作列表
 * @param params
 * @returns
 */
export function getTempNonRoutineWorkList(params) {
  return axios.get('/api/v1/pdas/pdasTempNonRoutineWorkReport/list', { params })
}

/**
 * 获取归口部门
 * @param params
 * @returns
 */
export function getDeptRelationsList() {
  return axios.get('/api/v1/pdas/pdasDeptRelations/list')
}

/**
 * 临时性非常规工作提交
 * @param data
 * @returns
 */
export function submitTempNonRoutineWork(data) {
  return axios.post('/api/v1/pdas/pdasTempNonRoutineWork/submit', data)
}

/**
 * 临时性非常规工作撤回
 * @param data
 * @returns
 */
export function resetTempNonRoutineWork(data) {
  return axios.post('/api/v1/pdas/pdasTempNonRoutineWork/recall', data)
}

/**
 * 临时性工作审核页日期菜单
 * @param params
 * @returns
 */
export function getMenuTemplAudit(userId) {
  return axios.get('/api/v1/pdas/commonApi/dateMenuTemplAudit?id=' + userId)
}
/**
 * 临时性工作审核列表
 * @param params
 * @returns
 */
export function getTemplAuditList(params) {
  return axios.get('/api/v1/pdas/pdasTempNonRoutineWorkReport/auditList', { params })
}

/**
 * 临时性非常规工作审核通过
 * @param data
 * @returns
 */
export function passTempNonRoutineWork(data) {
  return axios.post('/api/v1/pdas/pdasTempNonRoutineWorkReport/pass', data)
}

/**
 * 临时性非常规工作审核驳回
 * @param data
 * @returns
 */
export function rejectTempNonRoutineWork(data) {
  return axios.post('/api/v1/pdas/pdasTempNonRoutineWorkReport/reject', data)
}

// 专业评价-县公司列表
export function getProfEvalReportCountyList(params) {
  return axios.get('/api/v1/pdas/pdasProfEvalReportCounty/list', { params })
}

// 专业评价=县公司填报
export function saveCounty(data) {
  return axios.post('/api/v1/pdas/pdasProfEvalReportCounty', data)
}

// 专业评价-县公司删除一行
export function deleteCounty(params) {
  return axios.delete('/api/v1/pdas/pdasProfEvalReportCounty', { params })
}

// 绩效审批日期菜单树
export function getDateMenuAudit() {
  return axios.get('/api/v1/pdas/commonApi/dateMenuAudit')
}

// 获取填报状态
export function getPrefReportStatus(params) {
  return axios.get('/api/v1/pdas/pdasPrefReport/status', { params })
}

// 填报提交
export function saveReportSubmit(data) {
  return axios.post('/api/v1/pdas/pdasPrefReport/submit', data)
}

// 填报撤销
export function saveReportRecall(data) {
  return axios.post('/api/v1/pdas/pdasPrefReport/recall', data)
}

/**
 * 绩效审核通过
 * @param data
 * @returns
 */
export function passPrefReport(data) {
  return axios.post('/api/v1/pdas/pdasPrefReport/pass', data)
}

/**
 * 绩效审核驳回
 * @param data
 * @returns
 */
export function rejectPrefReport(data) {
  return axios.post('/api/v1/pdas/pdasPrefReport/reject', data)
}

// 重点工作绩效 查看
export function getPrefPlanReportViewList(params) {
  return axios.get('/api/v1/pdas/pdasPrefPlanReport/viewList', { params })
}

// 临时性非常规工作 查看
export function getTempNonRoutineWorkReportViewList(params) {
  return axios.get('/api/v1/pdas/pdasTempNonRoutineWorkReport/viewList', { params })
}

// 业绩指标考核 查看
export function getIndicatorAssessReportViewList(params) {
  return axios.get('/api/v1/pdas/pdasIndicatorAssessReport/viewList', { params })
}

// 县公司评价 查看
export function getProfEvalReportCountyViewList(params) {
  return axios.get('/api/v1/pdas/pdasProfEvalReportCounty/viewList', { params })
}

// 市公司评价 查看
export function getProfEvalReportCityList(params) {
  return axios.get('/api/v1/pdas/pdasProfEvalReportCity/viewList', { params })
}

// 分值考核 查看
export function getSpecialScoreAssessReportViewList(params) {
  return axios.get('/api/v1/pdas/pdasSpecialScoreAssessReport/viewList', { params })
}

// 现金奖惩 查看
export function getSpecialCashAssessReportViewList(params) {
  return axios.get('/api/v1/pdas/pdasSpecialCashAssessReport/viewList', { params })
}

// 佐证材料 查看
export function getEvidenceMaterialViewList(params) {
  return axios.get('/api/v1/pdas/pdasEvidenceMaterial/viewList', { params })
}

// 特殊事项奖励 查看
export function getSpecialRewardViewList(params) {
  return axios.get('/api/v1/pdas/pdasSpecialReward/viewList', { params })
}

// 导出报告
export function getExportMonthWord(params) {
  return axios.get('/api/v1/pdas/commonApi/exportMonthWord', { params: params, responseType: 'blob' })
}

//临时性非常规工作审核作废
export function getTempNonRoutineWorkReportInvalid(data) {
  return axios.post('/api/v1/pdas/pdasTempNonRoutineWorkReport/invalid', data)
}

//绩效填报状态列表接口
export function getPrefReportAuditStatusList(params) {
  return axios.get('/api/v1/pdas/pdasPrefReport/auditStatusList', { params })
}

// 填报导出
export function getexportMonthExcel(params) {
  return axios.get('/api/v1/pdas/commonApi/exportMonthExcel', { params: params, responseType: 'blob' })
}

//上传绩效评价文件
export function uploadPerfPublish(date, file) {
  let form = new FormData()
  form.append('date', date)
  form.append('file', file)
  return axios.post('/api/v1/pdas/pdasPerfPublish/upload', form)
}

//绩效评价列表
export function getPerfPublishList(params) {
  return axios.get('/api/v1/pdas/pdasPerfPublish/list', { params })
}

//绩效评价发布
export function getPerfPublish(data) {
  return axios.post('/api/v1/pdas/pdasPerfPublish/publish', data)
}

//绩效评价撤回
export function getPerfPublishRetract(data) {
  return axios.post('/api/v1/pdas/pdasPerfPublish/retract', data)
}

//绩效评价删除
export function deletePerfPublish(ids) {
  return axios.delete('/api/v1/pdas/pdasPerfPublish?ids=' + ids)
}

//全局搜索
export function getGloablSearch(data) {
  return axios.post('/api/v1/pdas/commonApi/gloablSearch', data)
}

//审核列表页驳回接口
export function prefReportAuditListReject(data) {
  return axios.post('/api/v1/pdas/pdasPrefReport/auditListReject', data)
}

//业绩指标数据上移下移(大行)
export function indicatorAssessReportSwap(data) {
  return axios.post('/api/v1/pdas/pdasIndicatorAssessReport/swap', data)
}

//业绩指标数据置顶置底(大行)
export function indicatorAssessReportPinned(data) {
  return axios.post('/api/v1/pdas/pdasIndicatorAssessReport/pinned', data)
}

//业绩指标数据上移下移(小行)
export function indicatorAssessOrgSwap(data) {
  return axios.post('/api/v1/pdas/pdasIndicatorAssessOrg/swap', data)
}

//业绩指标数据置顶置底(小行)
export function indicatorAssessOrgPinned(data) {
  return axios.post('/api/v1/pdas/pdasIndicatorAssessOrg/pinned', data)
}

//市公司评价上移下移(大行)
export function pdasProfEvalReportCitySwap(data) {
  return axios.post('/api/v1/pdas/pdasProfEvalReportCity/swap', data)
}

//市公司评价置顶置底(大行)
export function pdasProfEvalReportCityPinned(data) {
  return axios.post('/api/v1/pdas/pdasProfEvalReportCity/pinned', data)
}

//市公司评价上移下移(小行)
export function pdasProfEvalReportCityOrgSwap(data) {
  return axios.post('/api/v1/pdas/pdasProfEvalReportCityOrg/swap', data)
}

//市公司评价置顶置底(小行)
export function pdasProfEvalReportCityOrgPinned(data) {
  return axios.post('/api/v1/pdas/pdasProfEvalReportCityOrg/pinned', data)
}

//分值考核上移下移(大行)
export function pdasSpecialScoreAssessReportSwap(data) {
  return axios.post('/api/v1/pdas/pdasSpecialScoreAssessReport/swap', data)
}

//分值考核置顶置底(大行)
export function pdasSpecialScoreAssessReportPinned(data) {
  return axios.post('/api/v1/pdas/pdasSpecialScoreAssessReport/pinned', data)
}

//分值考核上移下移(小行)
export function pdasSpecialScoreAssessOrgSwap(data) {
  return axios.post('/api/v1/pdas/pdasSpecialScoreAssessOrg/swap', data)
}

//分值考核置顶置底(小行)
export function pdasSpecialScoreAssessOrgPinned(data) {
  return axios.post('/api/v1/pdas/pdasSpecialScoreAssessOrg/pinned', data)
}

//现金奖惩上移下移(大行)
export function pdasSpecialCashAssessReportSwap(data) {
  return axios.post('/api/v1/pdas/pdasSpecialCashAssessReport/swap', data)
}

//现金奖惩置顶置底(大行)
export function pdasSpecialCashAssessReportPinned(data) {
  return axios.post('/api/v1/pdas/pdasSpecialCashAssessReport/pinned', data)
}

//现金奖惩上移下移(小行)
export function pdasSpecialCashAssessOrgSwap(data) {
  return axios.post('/api/v1/pdas/pdasSpecialCashAssessOrg/swap', data)
}

//现金奖惩置顶置底(小行)
export function pdasSpecialCashAssessOrgPinned(data) {
  return axios.post('/api/v1/pdas/pdasSpecialCashAssessOrg/pinned', data)
}

//临时性非常规上移下移(大行)
export function pdasTempNonRoutineWorkReportSwap(data) {
  return axios.post('/api/v1/pdas/pdasTempNonRoutineWorkReport/swap', data)
}

//临时性非常规置顶置底(大行)
export function pdasTempNonRoutineWorkReportPinned(data) {
  return axios.post('/api/v1/pdas/pdasTempNonRoutineWorkReport/pinned', data)
}

//临时性非常规上移下移(小行)
export function pdasTempNonRoutineWorkScoreOrgSwap(data) {
  return axios.post('/api/v1/pdas/pdasTempNonRoutineWorkScoreOrg/swap', data)
}

//临时性非常规置顶置底(小行)
export function pdasTempNonRoutineWorkScoreOrgPinned(data) {
  return axios.post('/api/v1/pdas/pdasTempNonRoutineWorkScoreOrg/pinned', data)
}

//重点绩效计划上移下移(小行)
export function pdasPrefPlanCollabOrgSwap(data) {
  return axios.post('/api/v1/pdas/pdasPrefPlanCollabOrg/swap', data)
}

//重点绩效计划上置顶置底(小行)
export function pdasPrefPlanCollabOrgPinned(data) {
  return axios.post('/api/v1/pdas/pdasPrefPlanCollabOrg/pinned', data)
}
