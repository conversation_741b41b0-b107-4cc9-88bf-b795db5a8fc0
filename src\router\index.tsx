import React from 'react'
import { Navigate } from 'react-router-dom'
import Layout from '../components/Layout'

const Sys403 = React.lazy(() => import('../pages/_System/403'))
const Sys404 = React.lazy(() => import('../pages/_System/404'))
const Login = React.lazy(() => import('../pages/Login'))
const Index = React.lazy(() => import('../pages/Index'))

//月度绩效审批
const PerformanceApproval = React.lazy(() => import('../pages/Approval/performanceApproval'))

//特殊事项奖励
const SpecialEventRewards = React.lazy(() => import('../pages/Approval/specialEventRewards'))
//临时性非常规工作审批
const SpecialTempUnconlApproval = React.lazy(() => import('../pages/Approval/specialTempUnconlApproval'))
//月度绩效数据填报
const PerformanceDataReporting = React.lazy(() => import('../pages/FillReport/performanceDataReporting'))
//月度模版数据维护
const MonthTempData = React.lazy(() => import('../pages/Maintenance/monthTempData'))
//月度绩效数据查看与导出
const DataViewExport = React.lazy(() => import('../pages/View/dataViewExport'))
//年度绩效数据
const DataYearPerformance = React.lazy(() => import('../pages/View/dataYearPerformance'))
//全局搜索
const GlobalSearch = React.lazy(() => import('../pages/Search'))

//绩效文件发布
const PerformEvaluationDocumentRelease = React.lazy(
  () => import('../pages/PerformEvaluationDocument/performEvaluationDocumentRelease')
)
//绩效文件下载
const PerformEvaluationDocumentDownload = React.lazy(
  () => import('../pages/PerformEvaluationDocument/performEvaluationDocumentDownload')
)

// 异步
function Suspense(component) {
  return <React.Suspense fallback={<></>}>{component}</React.Suspense>
}

const routers = [
  {
    path: '/',
    element: <Layout />,
    children: [
      // {
      //   index: true,
      //   // element: <Navigate to="/performanceDataReporting" />
      // },
      {
        path: '/index',
        element: Suspense(<Index />)
      },
      {
        path: '/*',
        element: <Navigate to="/404" />
      },
      //月度绩效审批
      {
        path: '/performanceApproval',
        element: Suspense(<PerformanceApproval />)
      },
      //特殊事项奖励
      {
        path: '/specialEventRewards',
        element: Suspense(<SpecialEventRewards />)
      },
      //临时性非常规工作审批
      {
        path: '/specialTempUnconlApproval',
        element: Suspense(<SpecialTempUnconlApproval />)
      },
      //月度绩效数据填报
      {
        path: '/performanceDataReporting',
        element: Suspense(<PerformanceDataReporting />)
      },
      //月度模版数据维护
      {
        path: '/monthTempData',
        element: Suspense(<MonthTempData />)
      },
      //月度绩效数据查看与导出
      {
        path: '/dataViewExport',
        element: Suspense(<DataViewExport />)
      },
      //年度绩效数据
      {
        path: '/dataYearPerformance',
        element: Suspense(<DataYearPerformance />)
      },
      //绩效文件发布
      {
        path: '/performEvaluationDocumentRelease',
        element: Suspense(<PerformEvaluationDocumentRelease />)
      },
      //绩效文件下载
      {
        path: '/performEvaluationDocumentDownload',
        element: Suspense(<PerformEvaluationDocumentDownload />)
      },
      //全局搜索页面
      {
        path: '/globalSearch',
        element: Suspense(<GlobalSearch />)
      }
    ]
  },
  {
    path: '/login',
    element: Suspense(<Login />)
  },
  {
    path: '/403',
    element: Suspense(<Sys403 />)
  },
  {
    path: '/404',
    element: Suspense(<Sys404 />)
  }
]

export default routers
