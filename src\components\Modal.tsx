import React from 'react'
import ReactDom from 'react-dom'
import { Radio } from 'antd'

interface IProps {
  children?: React.ReactNode
  onClose?: () => void
  visible: boolean
  marginTop?: string
  width?: string
  height?: string
  title?: React.ReactNode
  paddingTop?: string
  container?: HTMLElement
}

export default class Modal extends React.Component<IProps> {
  state = {
    RadioValue: 'orgSort ASC'
  }
  hideModal(e) {
    e.stopPropagation()
    if (this.props.onClose) this.props.onClose()
    this.setState({
      RadioValue: 'orgSort ASC'
    })
  }

  render(): React.ReactNode {
    if (this.props.visible) {
      return ReactDom.createPortal(
        <div className="tr__modal">
          <div className="tr-modal-mask" onClick={this.hideModal.bind(this)}></div>
          <div
            className="tr-modal-body"
            style={{
              marginTop: this.props.marginTop || '100px',
              width: this.props.width || '50%',
              height: this.props.height || '500px'
            }}
          >
            <div
              style={{
                borderBottom: '1px solid #f5dbd3'
              }}
            >
              <div className="tr-modal-title">{this.props.title}</div>
              <span className="tr-modal-close" onClick={this.hideModal.bind(this)}>
                ✕
              </span>
            </div>

            <div
              style={{
                padding: '15px',
                paddingTop: this.props.paddingTop || '15px',
                height: 'calc(100% - 50px)',
                position: 'relative',
                overflow: 'auto'
              }}
            >
              {this.props.children}
            </div>
          </div>
        </div>,
        this.props.container || document.body
      )
    } else {
      return null
    }
  }
}
