.login-page {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
  width: 100%;
  background: url('@/assets/images/bg.jpg');
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;

  .login-group {
    height: 550px;
    width: 60%;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 10px;
    box-shadow: 0px 0px 10px black;

    .login-left {
      height: 100%;
      width: 50%;
      flex: 1;
      border-radius: 10px 0px 0px 10px;
      background: url('@/assets/images/zuobiampeitu.png');
      background-size: cover;
    }

    .login-right {
      height: 100%;
      width: 50%;
      flex: 1;
      background-color: #fff;
      border-radius: 0px 10px 10px 0px;
      .login-main {
        border-radius: 0px 10px 10px 0px;
        height: 100%;
        padding: 50px;
        box-sizing: border-box;
        background-color: #fff;
        .form-head {
          height: 53px;
          width: 85%;
          display: flex;
        }
        .left-title {
          width: calc(100% - 78px);
          height: 53px;
        }
        .top-title {
          height: 20px;
          font-size: 18px;
          line-height: 20px;
          letter-spacing: 2px;
        }
        .btm-title {
          margin-top: 5px;
          height: 26px;
          font-size: 28px;
          line-height: 28px;
          font-weight: bold;
          color: #00368d;
          letter-spacing: 3px;
        }
        .right-img {
          width: 78px;
          height: 53px;
          background-image: url('@/assets/images/jiantou-2.png');
          background-size: 100%;
          background-repeat: no-repeat;
        }
        .login-type {
          width: 100%;
          height: 25px;
          margin-top: 58px;
          font-size: 18px;
          line-height: 18px;
          display: flex;
          align-items: center;
        }
        .tag {
          width: 8px;
          height: 18px;
          background: linear-gradient(180deg, #1a70d9, #1354a5);
          margin-right: 10px;
          border-radius: 5px;
        }
        .form-item {
          width: 100%;
          margin-top: 22px;
          border: 1px solid #83aae9;
          border-radius: 5px;
        }
        .self-input {
          display: flex;
        }
        .user {
          width: 53px;
          height: 45px;
          background-image: url('@/assets/images/user-2.png'), linear-gradient(180deg, #cde4ff, #e5f3ff);
          background-repeat: no-repeat;
          background-position: center;
          border-radius: 5px 0px 0px 5px;
        }
        .code {
          width: 53px;
          height: 45px;
          background-image: url('@/assets/images/yanzhengma.png'), linear-gradient(180deg, #cde4ff, #e5f3ff);
          background-repeat: no-repeat;
          background-position: center;
          border-radius: 5px 0px 0px 5px;
        }
        .pwd {
          width: 53px;
          height: 45px;
          background-image: url('@/assets/images/pwd-2.png'), linear-gradient(180deg, #cde4ff, #e5f3ff);
          background-repeat: no-repeat;
          background-position: center;
          border-radius: 5px 0px 0px 5px;
        }
        .ant-input-affix-wrapper-lg {
          padding: 0px 11px 0px 0px;
        }
        .ant-input-affix-wrapper > input.ant-input {
          padding-left: 11px;
        }
      }
    }
    .ant-input:focus,
    .ant-input-focused {
      box-shadow: none;
    }
  }
}
