const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

function GetVersionFromGit() {
  return new Promise((resolve, reject) => {
    try {    
      const version = execSync('git branch --show-current', {env: process.env, encoding: 'utf-8'});
      const commitId = execSync('git rev-parse HEAD', {env: process.env, encoding: 'utf-8' });
      if (version.trim() == '' || commitId.trim() == '') {
        console.log('通过git获取失败') 
        reject()
      } else {
        resolve({version: version.trim(), commitId: commitId.trim()})
      }
    } catch (error) {
      console.log('通过git获取失败') 
      reject()
    }
  })
}

function GetVersionFromFile() {
  return new Promise((resolve, reject) => {
    try {
      const gitPath = path.join(process.env.PWD, '.git')
      const head = fs.readFileSync(path.join(gitPath, 'HEAD'), {encoding: 'utf-8'})
      const commitId = fs.readFileSync(path.join(gitPath, 'ORIG_HEAD'), {encoding: 'utf-8'})
      const version = head.split('/').pop()
      if (version.trim() == '' || commitId.trim() == '') {
        console.log('通过文件获取失败') 
        reject()
      } else {
        resolve({version: version.trim(), commitId: commitId.trim()})
      }
    } catch (error) {
      console.log('通过文件获取失败') 
      reject()
    }
  })
}

function GetVersion() {
  GetVersionFromGit().then(res => {
    WriteEnvFile(res)
  }).catch(() => {
    GetVersionFromFile().then(res => {
      WriteEnvFile(res)
    }).catch(() => {
      console.warn('版本获取失败')
    })
  })
}

function WriteEnvFile(data) {
  const envPath = path.join(process.env.PWD, '.env')
  const text = `VITE_APP_VERSION = "${data.version}"
  VITE_APP_COMMIT_ID = "${data.commitId}"`
  fs.writeFileSync(envPath, text)
}

GetVersion()