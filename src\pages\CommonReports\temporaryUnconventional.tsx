import { getOrgList, getTempNonRoutineWorkList } from '@/api'
import { getNumber, getOrgNames } from '@/utils/utils'
import React from 'react'
import { LoadingContext } from '@/components/load/loadingProvider'

interface IProps {
  //选中的是哪个节点
  leafKey: string
  //机构id
  orgId: string
}
export default class extends React.Component<IProps> {
  static contextType = LoadingContext
  context: React.ContextType<typeof LoadingContext>

  state = {
    dateId: '',
    menuType: '',
    tempList: [], // table值
    bmList: []
  }

  componentDidMount() {
    //获取部门list
    this.getBmList()
    if (this.props.leafKey.includes('-')) {
      //将其拆分
      let datas = this.props.leafKey.split('-')
      this.setState({ dateId: datas[0], menuType: datas[1] }, () => {
        this.initializeData()
      })
    }
  }
  componentDidUpdate(prevProps: Readonly<IProps>, prevState: Readonly<{}>, snapshot?: any): void {
    if (this.props.leafKey != prevProps.leafKey) {
      if (this.props.leafKey.includes('-')) {
        //将其拆分
        let datas = this.props.leafKey.split('-')
        this.setState({ dateId: datas[0], menuType: datas[1] }, () => {
          this.initializeData()
        })
      }
    }
  }

  getBmList = () => {
    getOrgList('01,02,03').then((res: any) => {
      if (res?.data?.list?.length > 0) {
        this.setState({ bmList: res.data.list })
      }
    })
  }

  initializeData = () => {
    this.getTempList() // 临时性非常规工作填报table数据，分为不同角色，
  }

  getTempList = () => {
    this.context.showLoading()
    getTempNonRoutineWorkList({ date: this.state.dateId, status: '12', orgId: this.props.orgId, source: 'audit' })
      .then((res) => {
        if (res?.data?.list?.length > 0) {
          for (let i = 0; i < res.data.list.length; i++) {
            let obj = res.data.list[i]

            for (let j = 0; j < obj?.allLineList?.length; j++) {
              let lineList = obj.allLineList[j].lineList
              let orgIds = []
              for (let k = 0; k < lineList?.length; k++) {
                let line = lineList[k]
                orgIds.push(line.orgId)
              }
              obj.allLineList[j].orgIds = orgIds
            }
            //设置合计
            let count = this.getHj(i, res.data.list)
            obj.hj = count
          }
          this.setState({ tempList: res.data.list })
        } else {
          this.setState({ tempList: [] })
        }
        this.context.hideLoading()
      })
      .catch(() => {
        this.context.hideLoading()
      })
  }

  //计算合计
  getHj = (index, list) => {
    let count = 0
    let bmqkList = list[index].allLineList
    for (let i = 0; i < bmqkList.length; i++) {
      let obj = bmqkList[i]
      count = count + obj.orgIds.length * obj.score
    }
    return getNumber(count)
  }

  render(): React.ReactNode {
    return (
      <div style={{ height: '100%' }}>
        <div className="div-table">
          <table className="common-table">
            <thead>
              <tr>
                <th>序号</th>
                <th>提报部门</th>
                <th>考评内容</th>
                <th>部门名称</th>
                <th>得分</th>
                <th>合计</th>
              </tr>
            </thead>
            <tbody>
              {this.state.tempList.map((item, index) => {
                return item.allLineList.map((obj, index2) => {
                  return (
                    <tr key={index2}>
                      {index2 == 0 && (
                        <td style={{ width: '45px' }} rowSpan={item.allLineList.length}>
                          {index + 1}
                        </td>
                      )}
                      {index2 == 0 && <td rowSpan={item.allLineList.length}>{item.repoDepName}</td>}
                      {index2 == 0 && <td rowSpan={item.allLineList.length}>{item.assessContent}</td>}
                      <td>{getOrgNames(obj.orgIds, this.state.bmList)}</td>
                      <td style={{ width: '90px' }}>{obj.score}</td>
                      {index2 == 0 && <td rowSpan={item.allLineList.length}>{item.hj}</td>}
                    </tr>
                  )
                })
              })}
            </tbody>
          </table>
        </div>
      </div>
    )
  }
}
