.pagination-theme1 {
  text-align: right;
  margin-top: 10px;
  .ant-pagination-item,
  .ant-pagination-prev,
  .ant-pagination-next,
  .ant-pagination-jump-prev,
  .ant-pagination-jump-next,
  .ant-pagination-options-quick-jumper input {
    height: 25px !important;
    line-height: 23px !important;
    width: 45px !important;
    font-size: 12px;
  }
  .ant-pagination-prev,
  .ant-pagination-next {
    width: 32px !important;
    height: 32px !important;

    &:hover {
      color: var(--C0);
      border: 1px solid var(--C0);
    }
  }
  .ant-pagination-prev .ant-pagination-item-link,
  .ant-pagination-next .ant-pagination-item-link {
    border: 0;
    border-radius: 5px;
    background: linear-gradient(180deg, #cee3fe, #e0f2fe);
  }
  .ant-pagination-item {
    width: 32px !important;
    height: 32px !important;
    line-height: 32px !important;
  }
  .ant-pagination-item,
  .ant-pagination-prev,
  .ant-pagination-next,
  .ant-pagination-jump-prev,
  .ant-pagination-jump-next,
  .ant-pagination-options-quick-jumper input {
    border: 1px solid #95bcec;
    border-radius: 5px;
    background: linear-gradient(180deg, #cee3fe, #e0f2fe);
  }
  .ant-pagination-item-active {
    background: var(--C8);
    > a {
      color: var(--C4);
    }
  }
  .ant-pagination-disabled {
    border: 1px solid var(--C3) !important;
  }
}
