import React, { Component, createContext } from 'react'
import withLoadingIndicator from '.'

interface IProps {
  showLoading?: () => void
  hideLoading?: () => void
  children: React.ReactNode
}

export const LoadingContext = createContext(null)

@withLoadingIndicator
export class LoadingProvider extends Component<IProps> {
  render() {
    return (
      <LoadingContext.Provider value={{ showLoading: this.props.showLoading, hideLoading: this.props.hideLoading }}>
        {this.props.children}
      </LoadingContext.Provider>
    )
  }
}
